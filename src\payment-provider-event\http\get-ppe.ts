import { Middleware } from "@koa/router";
import {
  AppError,
  Auth,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { ppe } from "@mainframe-peru/types";
import { paymentProviderEventService } from "../service";

export const getPaymentProviderEvent: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  ppe.GetPaymentProviderEventResponse
> = async ({ request, response }) => {
  const params = ppe.GetPaymentProviderEventRequestSchema.parse(request.query);
  const entity = await paymentProviderEventService.get(
    "transactionId",
    params.transactionId,
  );

  if (!entity) {
    throw new AppError({
      code: "PaymentEventNotFound",
      message: "No se encontró el evento solicitado",
      statusCode: "NOT_FOUND",
    });
  }

  response.body = {
    id: entity.id,
    state: entity.state,
    provider: entity.provider || undefined,
    type: entity.type || undefined,
    createdAt: entity.createdAt,
    message: entity.responseMessage || undefined,
  };
};
