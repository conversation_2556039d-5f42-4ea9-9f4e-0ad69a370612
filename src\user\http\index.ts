import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
  sanitizeEmail,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { createUserEndpoint } from "./create-user";
import { deleteUserEndpoint } from "./delete-user";
import { getToken } from "./get-token";
import { getUserEndpoint } from "./get-user";
import { listUsers } from "./list-users";
import { loginUserEndpoint } from "./login-user";
import { logout } from "./logout";
import { resetPasswordEndpoint } from "./reset-user-password";
import { updateAttributesEndpoint } from "./update-attributes";
import { updateUserEndpoint } from "./update-user";
import { updateUserPasswordEndpoint } from "./update-user-password";
import { validateUserSessionEndpoint } from "./validation-user-session";
import { googleSignInEndpoint } from "./google-signin";
import { onboardFreeUsersEndpoint } from "./onboard-free-users";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.get(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      user: ["READ_USER"],
    },
    admin: {
      user: ["READ_USER"],
    },
    user: {
      general: ["TRANSIENT"],
    },
  }),
  getUserEndpoint,
);

router.get(
  "/list-users",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      user: ["LIST_USERS"],
    },
    admin: {
      user: ["LIST_USERS"],
    },
  }),
  listUsers,
);

router.delete(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      user: ["DELETE_USER"],
    },
    admin: {
      user: ["DELETE_USER"],
    },
  }),
  deleteUserEndpoint,
);

router.get(
  "/validate-session",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    user: {
      general: ["REGULAR"],
    },
  }),
  validateUserSessionEndpoint,
);

router.put(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      user: ["PUT_USER"],
    },
    admin: {
      user: ["PUT_USER"],
    },
    user: {
      general: ["TRANSIENT"],
    },
  }),
  updateUserEndpoint,
);

router.get(
  "/token",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    user: {
      general: ["REGULAR"],
    },
  }),
  getToken,
);

router.post(
  "/attributes",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      user: ["PUT_USER"],
    },
    admin: {
      user: ["PUT_USER"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  updateAttributesEndpoint,
);

router.post(
  "/onboard",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      user: ["PUT_USER"],
    },
    admin: {
      user: ["PUT_USER"],
    },
  }),
  onboardFreeUsersEndpoint,
);
/**
 * Endpoints without authentication
 */
router.post("/", sanitizeEmail, createUserEndpoint);
router.post("/login", sanitizeEmail, loginUserEndpoint);
router.post("/google-signin", googleSignInEndpoint);
router.post("/logout", logout);
router.post("/reset-password", sanitizeEmail, resetPasswordEndpoint);
router.put("/update-password", updateUserPasswordEndpoint);

export const userRouter = router;
