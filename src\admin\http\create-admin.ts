import { Middleware } from "@koa/router";
import { admin } from "@mainframe-peru/types";
import { adminService } from "../service";
import {
  AppError,
  AuthAdmin,
  AuthOfficer,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";

export const createAdminEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin | AuthOfficer>,
  unknown,
  admin.CreateAdminResponse
> = async ({ request, response, state }) => {
  const body = admin.CreateAdminRequestSchema.parse(request.body);

  if (
    state.auth.iss === "mainframe:officer" &&
    body.influencerId === undefined
  ) {
    throw new AppError({
      code: "MissingInfluencerId",
      message: "Influencer id is required to create admin",
      statusCode: "BAD_REQUEST",
    });
  }

  const entityId = await adminService.processCreation({
    ...body,
    influencerId:
      state.auth.iss === "mainframe:admin"
        ? state.auth.influencerId
        : body.influencerId || "",
  });

  response.body = {
    id: entityId.id,
  };
};
