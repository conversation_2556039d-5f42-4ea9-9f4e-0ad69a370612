import { Middleware } from "@koa/router";
import {
  AuthOffice<PERSON>,
  AuthAdmin,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { user } from "@mainframe-peru/types";
import { ListUsersRequestSchema } from "@mainframe-peru/types/build/user";
import { userService } from "../../user/service";
import { restrictParams } from "../../common";
import { format } from "fast-csv";

export const listSubscribers: Middleware<
  AuthorizedContextState<AuthOfficer | AuthAdmin>,
  unknown,
  user.ListUsersResponse
> = async (ctx) => {
  ctx.status = 200;
  ctx.set("Content-Type", "text/csv");
  ctx.set("Content-Disposition", 'attachment; filename="subscribers-list.csv"');

  const csvStream = format({ headers: true });
  csvStream.pipe(ctx.res);

  const query = ctx.request.query
    ? ListUsersRequestSchema.parse(ctx.request.query)
    : {};

  restrictParams(ctx.state.auth, query);

  const entities = await userService.getUsers({
    detailed: true,
    influencerId: query.influencerId,
    hasRecurrence: true,
  });
  const publicResult = entities.map((x) => ({
    alias: x.alias,
    firstName: x.firstName,
    lastName: x.lastName,
    city: x.city,
    subscribedSince: x.activeRecurrentPaymentStartDate,
  }));
  publicResult.forEach((row) => csvStream.write(row));
  csvStream.end();
};
