import request from "supertest";
import { app } from "../../src/api-handler";
import { officer } from "@mainframe-peru/types";
import { officerAuthToken } from "../common";
import { officerService } from "../../src/officer";

describe("create or update officer tests", () => {
  test("Create a new officer", async () => {
    const testEntity: officer.PutOfficerRequest = {
      email: "<EMAIL>",
      password: "1234567890",
      firstName: "John",
      lastName: "Tester",
      policies: {
        officer: {
          PUT_OFFICER: true,
          READ_OFFICER: false,
          DELETE_OFFICER: false,
          LIST_OFFICERS: false,
        },
        transaction: {
          PUT_TRANSACTION: true,
          READ_TRANSACTION: true,
          LIST_TRANSACTIONS: false,
        },
      },
    };

    // Creation the officer
    const response = await request(app)
      .put("/live/officer")
      .send(testEntity)
      .set("Cookie", `session=${await officer<PERSON>uthToken}`);

    expect(response.statusCode).toEqual(200);
    const user = await officerService.get("email", testEntity.email);

    expect(user).toEqual(
      expect.objectContaining({
        email: testEntity.email,
        hash: expect.any(String),
        firstName: testEntity.firstName,
        lastName: testEntity.lastName,
        policies: expect.objectContaining({
          officer: 1,
          transaction: 3,
        }),
      }),
    );
  });

  test("Update an officer", async () => {
    const testEntity: officer.PutOfficerRequest = {
      email: "<EMAIL>",
      password: "1234567890",
      firstName: "Charles",
      lastName: "Tester",
      policies: {
        officer: {
          PUT_OFFICER: true,
          READ_OFFICER: false,
          DELETE_OFFICERS: false,
          LIST_OFFICERS: false,
        },
        transaction: {
          PUT_TRANSACTION: true,
          READ_TRANSACTION: true,
          LIST_TRANSACTION: false,
        },
      },
    };

    // Creating the officer
    await request(app)
      .put("/live/officer")
      .send(testEntity)
      .set("Cookie", `session=${await officerAuthToken}`);

    testEntity.firstName = "Elaine";

    // Updating the officer
    await request(app)
      .put("/live/officer")
      .send(testEntity)
      .set("Cookie", `session=${await officerAuthToken}`);

    const user = await officerService.get("email", testEntity.email);

    expect(user).toEqual(
      expect.objectContaining({
        email: testEntity.email,
        hash: expect.any(String),
        firstName: "Elaine",
        lastName: testEntity.lastName,
        policies: expect.objectContaining({
          officer: 1,
          transaction: 3,
        }),
      }),
    );
  });
});
