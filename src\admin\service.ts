import { faker } from "@faker-js/faker";
import { hashPassword } from "@mainframe-peru/common-core";
import { InferInsertModel } from "drizzle-orm";
import { emailNotification } from "../common";
import { adminTable } from "../schema";
import { ServiceBase } from "../service-base";
import { sc } from "../services";
import { AdminEntity, adminRepository } from "./repository";
import { NTU } from "../helpers";

type ProcessCreationInput = Omit<
  InferInsertModel<typeof adminTable>,
  "hash"
> & {
  password?: string;
};

class AdminService extends ServiceBase<typeof adminRepository, AdminEntity> {
  constructor() {
    super(adminRepository);
  }

  // Initializes the creation process, processing the password and converting it to the hash
  async processCreation(data: ProcessCreationInput) {
    const randomPassword = faker.string.alphanumeric(10);
    const admin = await super.create({
      ...data,
      influencerId: data.influencerId as string,
      hash: await hashPassword(data.password || randomPassword),
    });
    if (data.password === undefined) {
      await emailNotification.sendNewAdminEmail(admin.email, {
        Email: admin.email,
        Password: randomPassword,
        Url: sc.adminUrl(admin.influencerId),
      });
    }
    return admin;
  }

  // Gets admin by influencerId and email (useful for login)
  async getAdmin(
    influencerId: string,
    email: string,
  ): Promise<AdminEntity | undefined> {
    return await this.repository.findAdmin(influencerId, email);
  }

  async getAdmins(
    filter: Parameters<typeof adminRepository.findAdmins>[0],
  ): Promise<NTU<AdminEntity>[]> {
    return await this.repository.findAdmins(filter);
  }
}

export const adminService = new AdminService();
