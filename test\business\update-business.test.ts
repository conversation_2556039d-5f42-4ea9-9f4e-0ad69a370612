import { business as bu } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken } from "../common";
import { sc } from "../../src/services";
import { mockClient } from "aws-sdk-client-mock";
import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";

describe("update business tests", () => {
  test("update a business", async () => {
    const s3Mock = mockClient(S3Client);
    s3Mock.on(PutObjectCommand).resolves({});

    await influencerService.create(TestData.influencer);
    const business = await businessService.create(TestData.business);

    const base64Image =
      "/9j/4AAQSkZJRgABAQEAAAAAAAD/4QCcRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAEAAIdpAAQAAAABAAAAJgAAAAAAAqACAAQAAAABAAAAJqADAAQAAAABAAAAJgAAAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFgABAQEAAAAAAAAAAAAAAAAAAAUG/8QAFxEBAQEBAAAAAAAAAAAAAAAAAAECA//EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEAMQAAABy0Cj/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAgBAQABPxA//8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAgBAgEBPxA//8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAgBAwEBPxA//9k=";
    const body = {
      id: business.id,
      name: crypto.randomUUID(),
      description: crypto.randomUUID(),
      ruc: crypto.randomUUID(),
    };

    const response = await request(app)
      .put("/live/business")
      .field("id", body.id)
      .field("name", body.name)
      .field("description", body.description)
      .field("ruc", body.ruc)
      .attach("file", Buffer.from(base64Image, "base64"), "test-image.jpg")
      .set("Cookie", `session=${await adminAuthToken}`);

    const entity = await businessService.get("id", business.id);
    expect(response.status).toBe(200);
    expect(entity).toEqual({
      id: response.body.id,
      influencerId: TestData.influencer.id,
      name: body.name,
      description: body.description,
      ruc: body.ruc,
      imageUrl: expect.stringContaining(
        `https://${sc.vars.modulesStorageBucket}.s3.us-east-1.amazonaws.com/`,
      ),
      createdAt: expect.any(Date),
      updatedAt: expect.any(Date),
    });
  });
});
