import {
  AppError,
  Auth,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { storage } from "@mainframe-peru/types";
import { Middleware } from "koa";
import { isImageExtensionValid } from "../../helpers";
import { sc } from "../../services";
import { storageService } from "../../storage/service";
import { reCaptchaApi } from "../../ext";
import sharp from "sharp";

export const uploadImageEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  storage.UploadImageResponse
> = async ({ request, response }) => {
  const data = storage.UploadImageRequestSchema.parse(request.body);
  if (data.folderName === "support") {
    if (!data.reCaptchaToken) {
      throw new AppError({
        code: "MissingReCaptcha",
        message: "User must send reCaptcha",
        logLevel: "INFO",
        statusCode: "BAD_REQUEST",
      });
    }
    await reCaptchaApi.validate(data.reCaptchaToken, request.ip);
  }

  if (!isImageExtensionValid(request.file.mimetype)) {
    throw new AppError({
      code: "InvalidImage",
      message: "La imagen no tiene una extensión válida.",
      logLevel: "ERROR",
      statusCode: "BAD_REQUEST",
    });
  }

  let buffer;
  try {
    buffer = await sharp(request.file.buffer).webp().toBuffer();
  } catch {
    buffer = request.file.buffer;
  }

  const imageUrl = await storageService.uploadFile({
    bucket: sc.vars.modulesStorageBucket,
    folder: data.folderName,
    fileName: data.id ?? crypto.randomUUID(),
    buffer,
    mimetype: request.file.mimetype,
    metadata: data.metadata ? JSON.parse(data.metadata) : {},
  });

  response.body = {
    imageUrl,
  };
};
