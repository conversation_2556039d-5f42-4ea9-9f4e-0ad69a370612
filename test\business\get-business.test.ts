import { business as bu } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken } from "../common";

describe("get business tests", () => {
  test("get a business", async () => {
    await influencerService.create(TestData.influencer);
    const business = await businessService.create(TestData.business);

    const query: bu.GetBusinessRequest = {
      id: business.id,
    };

    const response = await request(app)
      .get("/live/business")
      .query(query)
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody: bu.GetBusinessResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody).toEqual({
      id: response.body.id,
      name: business.name,
      description: business.description,
      ruc: business.ruc,
      imageUrl: business.imageUrl,
      createdAt: business.createdAt.toISOString(),
      updatedAt: business.updatedAt.toISOString(),
    });
  });
});
