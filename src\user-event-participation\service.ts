import { common } from "@mainframe-peru/types";
import { ServiceBase } from "../service-base";
import {
  UserEventParticipationEntity,
  userEventParticipationRepository,
} from "./repository";

class UserEventParticipationService extends ServiceBase<
  typeof userEventParticipationRepository,
  UserEventParticipationEntity
> {
  constructor() {
    super(userEventParticipationRepository);
  }

  async getByUserIdAndEventId(
    userId: number,
    eventId: number,
  ): Promise<UserEventParticipationEntity | undefined> {
    return userEventParticipationRepository.findByUserIdAndEventId(
      userId,
      eventId,
    );
  }

  async addSingleUserEventParticipation(
    userId: number,
    eventId: number,
    participationFormValues?: common.AttributeValues,
  ): Promise<boolean> {
    const userEventParticipation =
      await userEventParticipationRepository.findByUserIdAndEventId(
        userId,
        eventId,
      );
    if (!userEventParticipation) {
      await userEventParticipationRepository.create({
        userId,
        eventId,
        quantity: 1,
        participationFormValues,
      });
      return true;
    }
    return false;
  }

  async addUserEventParticipation(
    userId: number,
    eventId: number,
    quantity: number,
  ) {
    const userEventParticipation =
      await userEventParticipationRepository.findByUserIdAndEventId(
        userId,
        eventId,
      );
    if (userEventParticipation) {
      await userEventParticipationRepository.updateUserEventParticipation(
        userId,
        eventId,
        quantity + userEventParticipation.quantity,
      );
    } else {
      await userEventParticipationRepository.create({
        userId,
        eventId,
        quantity,
      });
    }
  }
}

export const userEventParticipationService =
  new UserEventParticipationService();
