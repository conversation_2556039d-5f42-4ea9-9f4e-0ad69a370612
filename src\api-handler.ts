import serverlessExpress from "@codegenie/serverless-express";
import { bodyParser } from "@koa/bodyparser";
import Router from "@koa/router";
import {
  errorMiddleware,
  logger,
  loggerMiddleware,
} from "@mainframe-peru/common-core";
import { sql } from "drizzle-orm";
import <PERSON><PERSON> from "koa";
import { adminRouter } from "./admin";
import { attributeRouter } from "./attribute";
import { businessRouter } from "./business";
import { businessPromotionRouter } from "./business-promotion";
import { businessPromotionCodeRouter } from "./business-promotion-code";
import { cardRouter } from "./card";
import { codeRouter } from "./code/http";
import { complaintRouter } from "./complaint/http";
import { eventRouter } from "./event";
import { influencerRouter } from "./influencer/http";
import { invoiceRouter } from "./invoice/http";
import { officerRouter } from "./officer";
import { paymentProviderEventRouter } from "./payment-provider-event/http";
import { paymentRouter } from "./payment/http";
import { planRouter } from "./plan";
import { recurrenceRouter } from "./recurrence";
import { reportRouter } from "./report/http";
import { sc } from "./services";
import { transactionRouter } from "./transaction";
import { userRouter } from "./user";
import { userPaymentProvideRouter } from "./user-payment-provider";
import { storageRouter } from "./storage/http";
import { userEventParticipationRouter } from "./user-event-participation/http";
import { userEventInviteRouter } from "./user-event-invite/http";
import { contactRouter } from "./contact/http";

const koa = new Koa();
const router = new Router({
  prefix: "/live",
});

/**
 * Middlewares
 */
koa.use(errorMiddleware);
koa.use(bodyParser());
koa.use(loggerMiddleware);
koa.use(async (_, next) => {
  try {
    await next();
  } catch (e) {
    if (
      e instanceof Error &&
      e.message.includes("password authentication failed")
    ) {
      await sc.refreshDb();
      await next();
    } else {
      throw e;
    }
  }
});

/**
 * Setup routes
 */
router.use("/admin", adminRouter.routes());
router.use("/card", cardRouter.routes());
router.use("/code", codeRouter.routes());
router.use("/influencer", influencerRouter.routes());
router.use("/officer", officerRouter.routes());
router.use("/plan", planRouter.routes());
router.use("/report", reportRouter.routes());
router.use("/transaction", transactionRouter.routes());
router.use("/recurrence", recurrenceRouter.routes());
router.use("/user", userRouter.routes());
router.use("/user-payment-provider", userPaymentProvideRouter.routes());
router.use("/payment-provider-event", paymentProviderEventRouter.routes());
router.use("/complaint", complaintRouter.routes());
router.use("/payment", paymentRouter.routes());
router.use("/invoice", invoiceRouter.routes());
router.use("/event", eventRouter.routes());
router.use("/attribute", attributeRouter.routes());
router.use("/business", businessRouter.routes());
router.use("/business-promotion", businessPromotionRouter.routes());
router.use("/business-promotion-code", businessPromotionCodeRouter.routes());
router.use("/storage", storageRouter.routes());
router.use("/user-event-participation", userEventParticipationRouter.routes());
router.use("/user-event-invite", userEventInviteRouter.routes());
router.use("/contact", contactRouter.routes());
/**
 * Health route
 */
router.get("/health", async ({ response }) => {
  try {
    const db = await sc.getDB();
    const statement = sql`SELECT version()`;
    const res = await db.execute(statement);
    logger.info("Got postgres version");
    logger.info(res.rows[0].version as string);
    response.body = {
      ok: true,
    };
  } catch (e) {
    logger.error(e instanceof Error ? e : "");
    throw e;
  }
});

/**
 * Finish setup
 */
koa.use(router.routes());

export const app = koa.callback();
export const handler = serverlessExpress({ app });
