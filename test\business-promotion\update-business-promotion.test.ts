import { businessPromotion as bp } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken } from "../common";
import { businessPromotionService } from "../../src/business-promotion";
import { faker } from "@faker-js/faker";

describe("update business promotion tests", () => {
  test("update a business promotion", async () => {
    await influencerService.create(TestData.influencer);
    const business = await businessService.create(TestData.business);
    const promotion = await businessPromotionService.create(
      TestData.businessPromotion,
    );

    const body: bp.UpdateBusinessPromotionRequest = {
      id: promotion.id,
      content: faker.lorem.words(100),
      description: faker.lorem.words(100),
      name: faker.lorem.words(2),
      status: "INACTIVE",
      value: faker.word.words(5),
      imageUrl: "http://www.google.com/image.png",
      termsAndConditions: "kk",
    };

    const response = await request(app)
      .put("/live/business-promotion")
      .send(body)
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody: bp.UpdateBusinessPromotionResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody).toEqual({
      id: response.body.id,
      business: {
        id: business.id,
        description: business.description,
        imageUrl: business.imageUrl,
        name: business.name,
      },
      content: body.content,
      createdAt: expect.any(String),
      description: body.description,
      expirationDate: expect.any(String),
      name: body.name,
      updatedAt: expect.any(String),
      value: body.value,
      type: "PERSONAL",
      status: "INACTIVE",
      imageUrl: body.imageUrl,
      termsAndConditions: body.termsAndConditions,
    });

    const saved = await businessPromotionService.get("id", promotion.id);
    expect(saved).toEqual(expect.objectContaining(body));
  });
});
