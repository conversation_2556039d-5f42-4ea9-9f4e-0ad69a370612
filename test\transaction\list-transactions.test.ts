import { transaction } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { TransactionEntity } from "../../src/transaction/repository";
import { transactionService } from "../../src/transaction/service";
import { userService } from "../../src/user";
import { TestData, adminAuthToken, userAuthToken } from "../common";

describe("transaction.list-transaction", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
  });

  test("list user transactions (json)", async () => {
    const ts: TransactionEntity[] = [];
    for (let i = 0; i < 10; i++) {
      const t = await transactionService.create({
        id: i + 10 ** 6,
        publicId: crypto.randomUUID(),
        userId: TestData.user.id,
        influencerId: TestData.influencer.id,
        state: "SUCCESS",
        amount: "123.45",
        currency: "PEN",
        type: "PURCHASE",
      });
      ts.push(t);
    }

    const response = await request(app)
      .get("/live/transaction/list-transactions")
      .set("Accept", "application/json")
      .query({
        detailed: false,
        influencerId: TestData.influencer.id,
        userId: TestData.user.id,
        state: "SUCCESS",
        type: "PURCHASE",
      })
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    const body = response.body as transaction.ListTransactionsResponse;
    expect(body.items.length).toEqual(10);
    expect(body.items.map((i) => i.id)).toEqual(
      expect.arrayContaining(ts.map((i) => i.id)),
    );
  });

  test("list user transactions (csv)", async () => {
    const ts: TransactionEntity[] = [];
    const count = 50;
    for (let i = 0; i < count; i++) {
      const t = await transactionService.create({
        publicId: crypto.randomUUID(),
        userId: TestData.user.id,
        influencerId: TestData.influencer.id,
        state: "SUCCESS",
        amount: "123.45",
        currency: "PEN",
        type: "PURCHASE",
        createdAt: new Date(2000, 1, 1),
      });
      ts.push(t);
    }

    const query: transaction.ListTransactionsRequest = {
      detailed: true,
      limit: -1,
    };
    const response = await request(app)
      .get("/live/transaction/list-transactions")
      .set("Accept", "text/csv")
      .query(query)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.text.split("\n").length).toEqual(count + 1);
  });
});
