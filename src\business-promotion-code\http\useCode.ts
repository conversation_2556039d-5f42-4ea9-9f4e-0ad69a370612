import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import { businessPromotionService } from "../../business-promotion/service";
import { businessPromotionCodeService } from "../service";

export const useBusinessPromotionCodeEndpoint: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  bpc.UseBusinessPromotionCodeResponse
> = async ({ request, response, state }) => {
  const params = bpc.UseBusinessPromotionCodeRequestSchema.parse(request.body);

  const promotion = await businessPromotionService.getSecure({
    id: params.promotionId,
    influencerId: state.auth.influencerId,
  });

  const code = await businessPromotionCodeService.getUserOrAvailableCode({
    promotionId: params.promotionId,
    userId: state.auth.id,
  });
  if (!code) {
    throw new AppError({
      code: "NoCodeAvailable",
      message: "No available code for this promotion",
      statusCode: "NOT_FOUND",
      logLevel: "INFO",
    });
  }

  if (promotion.type === "PERSONAL" && !code.userId) {
    await businessPromotionCodeService.update(code.id, {
      userId: state.auth.id,
    });
  }

  response.body = {
    id: code.id,
    code: code.code,
  };
};
