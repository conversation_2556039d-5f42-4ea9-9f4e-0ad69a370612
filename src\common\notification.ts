import { PublishCommand, PublishCommandOutput } from "@aws-sdk/client-sns";
import { sc } from "../services";
import { email } from "@mainframe-peru/types";
import { InfluencerEntity } from "../influencer/repository";
import { logger } from "@mainframe-peru/common-core";

export const emailNotification = {
  async sendWelcomeEmail(
    to: string,
    influencer: InfluencerEntity,
    content: email.FccWelcomeEmailContent,
  ): Promise<PublishCommandOutput | undefined> {
    const template = influencer.emailsConfiguration?.templates.welcome;
    const message: email.SendEmailRequest = {
      provider: "ses",
      from: template?.from || this.getInfluencerFromEmail(influencer),
      to,
      metadata: {},
      subject: template?.subject || "¡Bienvenido a la comunidad!",
      type: "welcome",
      template: template?.template || "welcome",
      data: content,
    };
    return await this.publishEmailMessage(message);
  },
  async sendResetPassword(
    to: string,
    influencer: InfluencerEntity,
    content: email.ResetPasswordEmailContent,
  ): Promise<PublishCommandOutput | undefined> {
    const template = influencer.emailsConfiguration?.templates.passwordReset;
    const message: email.SendEmailRequest = {
      provider: "ses",
      from: template?.from || this.getInfluencerFromEmail(influencer),
      to,
      metadata: {},
      subject: template?.subject || "Cambio de contraseña",
      type: "passwordReset",
      template: template?.template || "reset-password",
      data: content,
    };
    return await this.publishEmailMessage(message);
  },
  async sendOnboardEmail(
    to: string,
    influencer: InfluencerEntity,
    content: email.ResetPasswordEmailContent,
  ): Promise<PublishCommandOutput | undefined> {
    const template = influencer.emailsConfiguration?.templates.passwordReset;
    const message: email.SendEmailRequest = {
      provider: "ses",
      from: template?.from || this.getInfluencerFromEmail(influencer),
      to,
      metadata: {},
      subject: template?.subject || "Bienvenido al club!",
      type: "onboard",
      template: template?.template || "fcc-onboard",
      data: content,
    };
    return await this.publishEmailMessage(message);
  },
  async sendInvoiceEmail(
    to: string,
    content: email.InvoiceEmailContent,
  ): Promise<PublishCommandOutput | undefined> {
    const fromDomain =
      sc.vars.env === "prod" ? "pchujoy.app" : `${sc.vars.env}.pchujoy.app`;
    const message: email.SendEmailRequest = {
      provider: "ses",
      from: `Notificaciones PChuJoy <notifications@${fromDomain}>`,
      to,
      metadata: {},
      subject:
        "Notificación - Comprobante electrónico emitido por MAINFRAME S.A.C.",
      type: "invoice",
      template: "invoice",
      data: content,
    };
    return await this.publishEmailMessage(message);
  },
  async sendNewAdminEmail(
    to: string,
    content: email.NewAdminEmailContent,
  ): Promise<PublishCommandOutput | undefined> {
    const message: email.SendEmailRequest = {
      provider: "ses",
      from: `Notificaciones PChuJoy <notifications@${sc.appDomain}>`,
      to,
      metadata: {},
      subject: "Creación de nueva cuenta admin",
      type: "newAdmin",
      template: "new-admin",
      data: content,
    };
    return await this.publishEmailMessage(message);
  },
  async sendComplaintEmail(
    to: string,
    influencer: InfluencerEntity,
    content: email.NewComplainContent,
  ): Promise<PublishCommandOutput | undefined> {
    const template = influencer.emailsConfiguration?.templates.complaint;
    const message: email.SendEmailRequest = {
      provider: "ses",
      from: template?.from || this.getInfluencerFromEmail(influencer),
      to,
      metadata: {},
      subject: template?.subject || `Creación de reclamo - ${influencer.name}`,
      type: "complaint",
      template: template?.template || "new-complaint",
      data: content,
    };
    return await this.publishEmailMessage(message);
  },
  async sendContactEmail(
    to: string,
    influencer: InfluencerEntity,
    content: email.ContactEmailContent,
  ): Promise<PublishCommandOutput | undefined> {
    const template = influencer.emailsConfiguration?.templates.contact;
    const message: email.SendEmailRequest = {
      provider: "ses",
      from: template?.from || this.getInfluencerFromEmail(influencer),
      to,
      cc: template?.cc || "<EMAIL>",
      metadata: {},
      subject: template?.subject || `Resumen de contacto - ${influencer.name}`,
      type: "contact",
      template: template?.template || "contact",
      data: content,
    };
    return await this.publishEmailMessage(message);
  },
  async sendSupportEmail(
    to: string,
    influencer: InfluencerEntity,
    content: email.SupportEmailContent,
    attachment?: string,
  ): Promise<PublishCommandOutput | undefined> {
    const template = influencer.emailsConfiguration?.templates.support;
    const message: email.SendEmailRequest = {
      provider: "ses",
      from: template?.from || "<EMAIL>",
      to,
      bcc: template?.bcc || "<EMAIL>",
      metadata: {},
      subject: template?.subject || `Resumen de soporte - ${influencer.name}`,
      type: "support",
      template: template?.template || "support",
      data: content,
      attachments: attachment ? [attachment] : [],
    };
    return await this.publishEmailMessage(message);
  },
  async sendEventInvite(
    to: string,
    influencer: InfluencerEntity,
    content: {
      eventName: string;
      eventDescription: string;
      inviteUrl: string;
      eventDate?: string;
      eventLocation?: string;
    },
  ): Promise<PublishCommandOutput | undefined> {
    const template = influencer.emailsConfiguration?.templates.contact;
    const message: email.SendEmailRequest = {
      provider: "ses",
      from: template?.from || this.getInfluencerFromEmail(influencer),
      to,
      metadata: {},
      subject: template?.subject || `Invitación a evento: ${content.eventName}`,
      type: "contact",
      template: template?.template || "contact",
      data: {
        Name: to,
        Email: to,
        Status: "INVITE",
        Message: `Has sido invitado al evento: ${content.eventName}. ${content.eventDescription}. Acepta tu invitación en: ${content.inviteUrl}`,
        Subject: `Invitación a evento: ${content.eventName}`,
      },
    };
    return await this.publishEmailMessage(message);
  },
  getInfluencerFromEmail(influencer: InfluencerEntity): string {
    return sc.vars.env === "prod"
      ? `${influencer.name} <notification@${influencer.domain}>`
      : `${influencer.name} <notification@${sc.vars.env}.pchujoy.app>`;
  },
  async publishEmailMessage(
    message: Record<string, unknown>,
  ): Promise<PublishCommandOutput | undefined> {
    try {
      const command = new PublishCommand({
        TopicArn: sc.vars.snsTopicArn,
        Message: JSON.stringify(message),
        MessageAttributes: {
          service: {
            DataType: "String",
            StringValue: "email",
          },
        },
      });
      return await sc.sns.send(command);
    } catch (e) {
      logger.error("Error sending message to sqs email queue", e);
    }
  },
};
