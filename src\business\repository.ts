import { business as bu } from "@mainframe-peru/types";
import { InferSelectModel, SQL, and, eq, ilike } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { businessTable } from "../schema";
import { sc } from "../services";

export type BusinessEntity = InferSelectModel<typeof businessTable>;

type FindBusinessInput = bu.ListBusinessRequest & { influencerId: string };

class BusinessRepository extends RepositoryBase<typeof businessTable> {
  constructor() {
    super(businessTable);
  }

  async findBusiness(filter: FindBusinessInput): Promise<BusinessEntity[]> {
    const db = await sc.getDB();

    const conditions: SQL[] = [
      eq(this.table.influencerId, filter.influencerId),
    ];
    if (filter.name) {
      conditions.push(ilike(this.table.name, `%${filter.name}%`));
    }
    if (filter.ruc) {
      conditions.push(ilike(this.table.name, `%${filter.ruc}%`));
    }
    return db
      .select()
      .from(this.table)
      .where(and(...conditions))
      .orderBy(this.table.id);
  }
}

export const businessRepository = new BusinessRepository();
