import { Enums } from "@mainframe-peru/types/build/common";
import { recurrence as recT } from "@mainframe-peru/types";
import { add } from "date-fns";
import request from "supertest";
import { app } from "../../../src/api-handler";
import { influencerService } from "../../../src/influencer/service";
import { planService } from "../../../src/plan";
import { RecurrenceEntity } from "../../../src/recurrence/repository";
import { recurrenceService } from "../../../src/recurrence/service";
import { userService } from "../../../src/user";
import { TestData, userAuthToken } from "../../common";
import { productService } from "../../../src/product/service";

describe("cancel-recurrence", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await productService.create(TestData.product);
    await planService.create(TestData.plan);
  });

  test("cancel recurrence (as user)", async () => {
    // Mock recurrent payment
    const recurrence = await recurrenceService.create({
      id: 10 ** 6,
      userId: TestData.user.id,
      type: Enums.RecurrenceType.Values.CARD,
      planId: TestData.plan.id,
      renewalDate: add(new Date(), {
        days: 10,
      }),
      startDate: new Date(),
      status: "ACTIVE",
    });

    // Do request
    const response = await request(app)
      .delete("/live/recurrence")
      .set("Cookie", `session=${await userAuthToken}`);

    // Check http response
    expect(response.status).toBe(204);
    expect(response.body).toEqual({});

    // Check the endDate on the recurrence has been updated
    const storedRecurrence = await recurrenceService.get("id", recurrence.id);
    const rp = storedRecurrence as RecurrenceEntity;
    expect(rp.endDate).toEqual(expect.any(Date));
  });

  test("cancel recurrence (as admin)", async () => {
    // Mock recurrent payment
    const recurrence = await recurrenceService.create({
      id: 10 ** 6,
      userId: TestData.user.id,
      type: Enums.RecurrenceType.Values.CARD,
      planId: TestData.plan.id,
      renewalDate: add(new Date(), {
        days: 10,
      }),
      startDate: new Date(),
      status: "ACTIVE",
    });

    // Do request
    const query: recT.CancelRecurrenceRequest = {
      userId: recurrence.userId,
    };
    const response = await request(app)
      .delete("/live/recurrence")
      .query(query)
      .set("Cookie", `session=${await userAuthToken}`);

    // Check http response
    expect(response.status).toBe(204);
    expect(response.body).toEqual({});

    // Check the endDate on the recurrence has been updated
    const storedRecurrence = await recurrenceService.get("id", recurrence.id);
    const rp = storedRecurrence as RecurrenceEntity;
    expect(rp.endDate).toEqual(expect.any(Date));
  });
});
