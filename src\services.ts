import { S3Client } from "@aws-sdk/client-s3";
import { SNSClient } from "@aws-sdk/client-sns";
import { SQSClient } from "@aws-sdk/client-sqs";
import { config } from "dotenv";
import { NodePgDatabase, drizzle } from "drizzle-orm/node-postgres";
import { Pool } from "pg";
import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from "@aws-sdk/client-secrets-manager";
import { AppError } from "@mainframe-peru/common-core";
import { SchedulerClient } from "@aws-sdk/client-scheduler";

type SecretValue = {
  username: string;
  password: string;
};

class ServiceController {
  sqs: SQSClient;
  sns: SNSClient;
  s3: S3Client;
  scheduler: SchedulerClient;

  private _db: NodePgDatabase | undefined;

  constructor() {
    this.sqs = new SQSClient();
    this.sns = new SNSClient();
    this.s3 = new S3Client();
    this.scheduler = new SchedulerClient();

    // Load environment variables using dotenv
    config();
  }

  get vars() {
    return {
      env: (process.env.ENV_NAME || "prod") as "dev" | "stg" | "prod",
      sqsQueueUrl: process.env.SQS_QUEUE_URL || "",
      fifoSqsQueueUrl: process.env.FIFO_SQS_QUEUE_URL || "",
      snsTopicArn: process.env.SNS_TOPIC_ARN || "",
      keys: {
        officer: {
          private: process.env.OFFICER_JWT_PRIVATE_KEY || "",
          public: process.env.OFFICER_JWT_PUBLIC_KEY || "",
        },
        admin: {
          private: process.env.ADMIN_JWT_PRIVATE_KEY || "",
          public: process.env.ADMIN_JWT_PUBLIC_KEY || "",
        },
        user: {
          private: process.env.USER_JWT_PRIVATE_KEY || "",
          public: process.env.USER_JWT_PUBLIC_KEY || "",
        },
      },
      reCaptchaKey: process.env.RECAPTCHA_KEY || "",
      nubefactKey: process.env.NUBEFACT_KEY || "",
      nubefactUrl: process.env.NUBEFACT_URL || "",
      modulesStorageBucket: process.env.MODULE_ASSETS_S3_BUCKET || "",
      fccFrontendDistributionId: process.env.FCC_FRONTEND_DISTRIBUTION_ID || "",
      schedulerTargetArn: process.env.SCHEDULER_TARGET_ARN || "",
      schedulerTargetRoleArn: process.env.SCHEDULER_TARGET_ROLE_ARN || "",
    };
  }

  get appDomain(): string {
    return sc.vars.env === "prod"
      ? "pchujoy.app"
      : `${sc.vars.env}.pchujoy.app`;
  }

  adminUrl(influencerId: string): string {
    return `https://admin.${this.appDomain}/${influencerId}/login`;
  }

  async getDB(): Promise<NodePgDatabase> {
    if (this._db) return this._db;

    const isTest = !!process.env.JEST_WORKER_ID;
    if (isTest) {
      const pool = new Pool({
        host: "localhost",
        database: "mainframe",
        user: process.env.DB_USER || "",
        password: process.env.DB_PASSWORD || "",
      });
      this._db = drizzle(pool, { logger: false });
      return this._db;
    }

    return this.refreshDb();
  }

  async refreshDb(): Promise<NodePgDatabase> {
    const secretArn = process.env.DB_SECRET || "";

    const secretManagerClient = new SecretsManagerClient({
      region: "us-east-1",
    });
    const secretValue = await secretManagerClient.send(
      new GetSecretValueCommand({
        SecretId: secretArn,
      }),
    );

    if (!secretValue.SecretString) {
      throw new AppError({
        code: "NoSecretStringFound",
        message: "Returned secret string was undefined",
      });
    }

    const parsedSecretValue = JSON.parse(
      secretValue.SecretString,
    ) as SecretValue;

    const pool = new Pool({
      host: process.env.DB_ENDPOINT || "",
      database: process.env.DB_NAME || "",
      user: parsedSecretValue.username,
      password: parsedSecretValue.password,
      ssl: true,
    });
    this._db = drizzle(pool);
    return this._db;
  }
}

export const sc = new ServiceController();
