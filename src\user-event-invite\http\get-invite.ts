import { Middleware } from "@koa/router";
import { AppError } from "@mainframe-peru/common-core";
import { userEventInvite } from "@mainframe-peru/types";
import { userEventInviteService } from "../service";
import { eventService } from "../../event";

export const getInviteEndpoint: Middleware<
  unknown,
  unknown,
  userEventInvite.GetUserEventInviteResponse
> = async ({ request, response }) => {
  const query = userEventInvite.GetUserEventInviteRequestSchema.parse(
    request.query,
  );

  const invite = await userEventInviteService.get("id", query.id);

  if (!invite) {
    throw new AppError({
      code: "InviteNotFound",
      message: "Invite not found",
      statusCode: "NOT_FOUND",
      logLevel: "INFO",
    });
  }

  const event = await eventService.get("id", invite.eventId);
  if (!event) {
    throw new AppError({
      code: "EventNotFound",
      message: "Event not found",
      statusCode: "NOT_FOUND",
      logLevel: "INFO",
    });
  }

  if (event.endDate && event.endDate < new Date()) {
    throw new AppError({
      code: "EventEnded",
      message: "The given event has already ended",
      statusCode: "BAD_REQUEST",
      logLevel: "INFO",
    });
  }

  response.body = {
    id: invite.id,
    status: invite.status,
    participationForm: event.inviteForm || undefined,
    createdAt: invite.createdAt,
    acceptedAt: invite.acceptedAt || undefined,
  };
};
