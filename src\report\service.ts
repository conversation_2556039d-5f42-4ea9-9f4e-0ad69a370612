import { report } from "@mainframe-peru/types";
import { recurrenceRepository } from "../recurrence/repository";
import { transactionRepository } from "../transaction/repository";
import { UserEntity, userRepository } from "../user/repository";
import { eventService } from "../event";
import { AppError } from "@mainframe-peru/common-core";
import { userEventParticipationRepository } from "../user-event-participation/repository";
import { NTU } from "../helpers";
import { format } from "fast-csv";
import { Upload } from "@aws-sdk/lib-storage";
import { sc } from "../services";
import stream from "stream";

type ListEventParticipantsInput = {
  eventId: number;
  influencerId: string;
};

class ReportService {
  constructor() {}

  async getTransactionsCountByState(filter: {
    influencerId?: string;
    startDate?: Date;
    endDate?: Date;
  }): Promise<report.TransactionCountByStateResponse> {
    return transactionRepository.getTransactionsCountByState(filter);
  }

  getActiveRecurrenceCount: typeof recurrenceRepository.getActiveRecurrenceCount =
    (i) => recurrenceRepository.getActiveRecurrenceCount(i);

  getTotalUsers: typeof userRepository.getTotalUsers = (...a) =>
    userRepository.getTotalUsers(...a);

  async listEventParticipants(
    input: ListEventParticipantsInput,
  ): Promise<NTU<(UserEntity & { quantity: number })[]>> {
    const event = await eventService.get("id", input.eventId);
    if (!event || event.influencerId !== input.influencerId) {
      throw new AppError({
        code: "EventNotFound",
        message: "Could not find event",
        statusCode: "NOT_FOUND",
        logLevel: "INFO",
      });
    }

    if (event.type === "IN-PERSON") {
      const participants =
        await userEventParticipationRepository.listEventParticipants(event.id);
      return participants.map((p) => ({
        ...p.user,
        quantity: p.user_event_participation.quantity,
      }));
    } else {
      const users = await userRepository.findUsers({
        hasRecurrence: true,
        detailed: true,
        limit: -1,
      });
      return users.map((u) => ({
        ...u,
        quantity: 1,
      }));
    }
  }

  async generateParticipantsCsv(input: {
    eventId: number;
    influencerId: string;
  }): Promise<string> {
    const participants = await reportService.listEventParticipants({
      eventId: input.eventId,
      influencerId: input.influencerId,
    });

    const mappedParticipants =
      await reportService.eventParticipantToCsvRecords(participants);

    const passThroughStream = new stream.PassThrough();
    const upload = new Upload({
      client: sc.s3,
      params: {
        Bucket: sc.vars.modulesStorageBucket,
        Key: `events/participants-list/event-${input.eventId}.csv`,
        ContentType: "text/csv",
        ACL: "public-read",
        Body: passThroughStream,
        Metadata: {
          eventId: input.eventId.toString(),
        },
      },
    });

    const csvStream = format({ headers: true });
    csvStream.pipe(passThroughStream);
    mappedParticipants.forEach((row) => csvStream.write(row));
    csvStream.end();

    const { Bucket, Key } = await upload.done();

    const eventListUrl = `https://${Bucket}.s3.us-east-1.amazonaws.com/${Key}`;
    await eventService.update(input.eventId, {
      eventListUrl,
    });
    return eventListUrl;
  }

  eventParticipantToCsvRecords(
    participants: NTU<UserEntity & { quantity: number }>[],
  ): report.ListEventParticipantsResponse {
    return participants.map((p) => ({
      quantity: p.quantity,
      id: p.id,
      email: p.email,
      firstName: p.firstName,
      lastName: p.lastName,
      alias: p.alias,
      phone: p.phone,
      gender: p.gender,
      birthDate: p.birthDate?.toISOString(),
      documentType: p.documentType,
      documentValue: p.documentValue,
      country: p.country,
      city: p.city,
      province: p.province,
      district: p.district,
      zipCode: p.zipCode,
      line1: p.line1,
      line2: p.line2,
    }));
  }
}

export const reportService = new ReportService();
