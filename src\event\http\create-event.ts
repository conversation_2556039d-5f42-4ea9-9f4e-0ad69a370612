import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { event } from "@mainframe-peru/types";
import { restrictParams } from "../../common";
import { transformNullToUndefined } from "../../helpers";
import { eventService } from "../service";

export const createEventEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  event.CreateEventResponse
> = async ({ request, response, state }) => {
  const body = event.CreateEventRequestSchema.parse(request.body);

  restrictParams(state.auth, body);

  const entity = await eventService.create({
    influencerId: body.influencerId,
    name: body.name,
    type: body.type,
    status: body.status,
    startDate: body.startDate,
    eventDate: body.eventDate,
    endDate: body.endDate,
    description: body.description,
    imageUrl: body.imageUrl,
    participationForm: body.participationForm,
    inviteLimit: body.inviteLimit,
    inviteForm: body.inviteForm,
    location: body.location,
  });

  if (entity.type === "PRIZE" && entity.eventDate) {
    await eventService.createOrUpdateEventListGenerationSchedule(
      entity,
      "create",
    );
  }

  response.body = event.CreateEventResponseSchema.parse(
    transformNullToUndefined(entity),
  );
};
