import { Middleware } from "@koa/router";
import { admin } from "@mainframe-peru/types";
import { adminService } from "../service";
import {
  AppError,
  AuthAdmin,
  AuthOfficer,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";

export const updateAdminEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin | AuthOfficer>,
  unknown,
  admin.UpdateAdminResponse
> = async ({ request, response, state }) => {
  const body = admin.UpdateAdminRequestSchema.parse(request.body);

  const existing = await adminService.get("id", body.id);
  if (
    !existing ||
    (state.auth.iss === "mainframe:admin" &&
      existing.influencerId !== state.auth.influencerId)
  ) {
    throw new AppError({
      code: "NotFound",
      message: "Could not find admin",
      statusCode: "NOT_FOUND",
      logLevel: "INFO",
    });
  }
  const entityId = await adminService.update(body.id, {
    email: body.email,
    policies: body.policies,
    firstName: body.firstName,
    lastName: body.lastName,
  });

  response.body = {
    id: entityId.id,
  };
};
