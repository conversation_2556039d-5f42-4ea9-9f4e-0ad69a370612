AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: MS for saving modifiable pdf documents for contract creators
Parameters:
  ServiceName:
    Type: String
    Default: core-backend

  EnvName:
    Type: AWS::SSM::Parameter::Value<String>
    Description: The environment name.
    Default: /env/name

  CulqiSecret:
    Type: AWS::SSM::Parameter::Value<String>
    Description: The user JWT public key.
    Default: /payments/culqi-secret

  NubefactKey:
    Type: AWS::SSM::Parameter::Value<String>
    Description: The Nubefact secret value.
    Default: /nubefact/private-key

  NubefactUrl:
    Type: AWS::SSM::Parameter::Value<String>
    Description: The Nubefact environment URL.
    Default: /nubefact/url

  ReCAPTCHAPrivateKey:
    Type: AWS::SSM::Parameter::Value<String>
    Description: The reCAPTCHA server key.
    Default: /recaptcha/private-key

Conditions:
  IsDev: !Equals [!Ref EnvName, "dev"]
  IsStg: !Equals [!Ref EnvName, "stg"]
  IsProd: !Equals [!Ref EnvName, "prod"]
  PrefixResources: !Or
    - !Equals [!Ref EnvName, "dev"]
    - !Equals [!Ref EnvName, "stg"]

Resources:
  #----------------------------------------------------------------
  # Lambda Role
  #----------------------------------------------------------------
  LambdaFunctionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${ServiceName}-lambdafnc-role
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action:
              - sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        - arn:aws:iam::aws:policy/service-role/AWSLambdaENIManagementAccess

  # Main policy, separated from the role so that they
  # can use dependant items without making loops
  LambdaFunctionRolePolicy:
    Type: AWS::IAM::Policy
    Properties:
      PolicyName: MainPolicy
      Roles:
        - !Ref LambdaFunctionRole
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: AccessRDSSecret
            Effect: Allow
            Action:
              - secretsmanager:GetSecretValue
            Resource:
              - !ImportValue RDS-SECRET
          - Sid: AccessRDSSecretKMS
            Effect: Allow
            Action:
              - kms:Decrypt
            Resource:
              - !ImportValue RDS-SECRET-KMS-KEY
          - Sid: AllowPushToSNS
            Effect: Allow
            Action: sns:Publish
            Resource:
              - !ImportValue MAIN-SNS-EVENT-BUS-ARN
          - Sid: AccessSQS
            Effect: Allow
            Action:
              - "sqs:ReceiveMessage"
              - "sqs:DeleteMessage"
              - "sqs:Send*"
              - "sqs:GetQueueAttributes"
            Resource:
              - !GetAtt SQSBusEventsQueue.Arn
              - !GetAtt SQSBusEventsDeadLetterQueue.Arn
              - !GetAtt SQSBusEventsFifoQueue.Arn
              - !GetAtt SQSBusEventsFifoDeadLetterQueue.Arn
          - Sid: AccessOwnBucket
            Effect: Allow
            Action:
              - "s3:Get*"
              - "s3:List*"
              - "s3:Head*"
              - "s3:Put*"
              - "s3:Delete*"
            Resource:
              - !GetAtt S3ModuleAssetsBucket.Arn
              - !Join ["", [!GetAtt S3ModuleAssetsBucket.Arn, "/*"]]
          - Sid: CreateCloudfrontInvalidation
            Effect: Allow
            Action:
              - "cloudfront:CreateInvalidation"
            Resource:
              - "*"
          - Sid: PutScheduler
            Effect: Allow
            Action:
              - "scheduler:CreateSchedule"
              - "scheduler:UpdateSchedule"
              - "iam:PassRole"
            Resource:
              - "*"

  #----------------------------------------------------------------
  # Scheduled Lambda Role
  #----------------------------------------------------------------
  ScheduledLambdaFunctionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${ServiceName}-scheduler-role
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - scheduler.amazonaws.com
            Action:
              - sts:AssumeRole

  ScheduledLambdaFunctionRolePolicy:
    Type: AWS::IAM::Policy
    Properties:
      PolicyName: MainPolicy
      Roles:
        - !Ref ScheduledLambdaFunctionRole
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: Lambdas
            Effect: Allow
            Action:
              - lambda:InvokeFunction
            Resource:
              - "*"

  LambdaSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: !Sub ${ServiceName} lambda security group
      VpcId: !ImportValue MAIN-VPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          CidrIp: 10.0.0.0/16
          FromPort: 0
          ToPort: 65535

  #----------------------------------------------------------------
  # API Gateway processing of events
  #----------------------------------------------------------------
  ApiGateway:
    Type: AWS::Serverless::HttpApi
    Properties:
      Name: !Ref ServiceName
      StageName: live

  # The lambda function that will process Api Gateway requests
  LambdaApiFunction:
    Type: AWS::Serverless::Function
    DependsOn: LambdaFunctionRolePolicy
    Properties:
      FunctionName: !Sub ${ServiceName}-api
      Role: !GetAtt LambdaFunctionRole.Arn
      Runtime: nodejs20.x
      Timeout: 27
      MemorySize: 384
      Tracing: Active
      Layers:
        - !Sub arn:aws:lambda:${AWS::Region}:580247275435:layer:LambdaInsightsExtension:2
      AutoPublishAlias: live
      DeploymentPreference:
        Type: AllAtOnce
      CodeUri: ./build
      Handler: api-handler.handler
      VpcConfig:
        SecurityGroupIds:
          - !Ref LambdaSecurityGroup
        SubnetIds:
          - !ImportValue PRIVATE-SUBNET-A
          - !ImportValue PRIVATE-SUBNET-B
      Environment:
        Variables:
          ENV_NAME: !Ref EnvName
          DB_ENDPOINT: !ImportValue RDS-ENDPOINT
          DB_SECRET: !ImportValue RDS-SECRET
          SQS_QUEUE_URL: !Ref SQSBusEventsQueue
          SNS_TOPIC_ARN: !ImportValue MAIN-SNS-EVENT-BUS-ARN
          CULQI_SECRET: !Ref CulqiSecret
          NUBEFACT_KEY: !Ref NubefactKey
          NUBEFACT_URL: !Ref NubefactUrl
          RECAPTCHA_KEY: !Ref ReCAPTCHAPrivateKey
          NODE_EXTRA_CA_CERTS: /var/task/us-east-1-bundle.pem
          MODULE_ASSETS_S3_BUCKET: !Ref S3ModuleAssetsBucket
          FCC_FRONTEND_DISTRIBUTION_ID: !ImportValue FCC-FRONTEND-DISTRIBUTION-ID
          SCHEDULER_TARGET_ARN: !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${ServiceName}-schedule
          SCHEDULER_TARGET_ROLE_ARN: !GetAtt ScheduledLambdaFunctionRole.Arn
      Events:
        DefaultEventSource:
          Type: HttpApi
          Properties:
            ApiId: !Ref ApiGateway

  ApiGatewayApiMapping:
    Type: AWS::ApiGatewayV2::ApiMapping
    DependsOn: ApiGatewayliveStage
    Properties:
      DomainName: !ImportValue MAIN-API-DOMAIN-NAME
      ApiId: !Ref ApiGateway
      ApiMappingKey: api
      Stage: live

  #----------------------------------------------------------------
  # SQS Processing of messages from the Main SNS Bus Event
  #----------------------------------------------------------------
  SQSBusEventsDeadLetterQueue:
    Type: AWS::SQS::Queue

  SQSBusEventsDeadLetterQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - !Ref SQSBusEventsDeadLetterQueue
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: AllowLambdaToSendMessages
            Effect: Allow
            Principal:
              AWS: "*"
            Action: sqs:SendMessage
            Resource: !GetAtt SQSBusEventsDeadLetterQueue.Arn
            Condition:
              ArnEquals:
                "aws:SourceArn": !GetAtt LambdaFunctionRole.Arn

  # SQS Queue for internal events
  SQSBusEventsQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub ${ServiceName}-queue
      VisibilityTimeout: 1500
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt SQSBusEventsDeadLetterQueue.Arn
        maxReceiveCount: 2

  SQSBusEventsQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - !Ref SQSBusEventsQueue
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: AllowLambdaToSendMessages
            Effect: Allow
            Principal:
              AWS: "*"
            Action: "sqs:SendMessage"
            Resource: !GetAtt SQSBusEventsQueue.Arn
            Condition:
              ArnEquals:
                "aws:SourceArn": !GetAtt LambdaFunctionRole.Arn

  # FIFO SQS queue
  SQSBusEventsFifoDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      FifoQueue: true
      ContentBasedDeduplication: true

  SQSBusEventsFifoDeadLetterQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - !Ref SQSBusEventsFifoDeadLetterQueue
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: AllowLambdaToSendMessages
            Effect: Allow
            Principal:
              AWS: "*"
            Action: sqs:SendMessage
            Resource: !GetAtt SQSBusEventsFifoDeadLetterQueue.Arn
            Condition:
              ArnEquals:
                "aws:SourceArn": !GetAtt LambdaFunctionRole.Arn

  # SQS Queue for internal events
  SQSBusEventsFifoQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub ${ServiceName}-fifo-queue.fifo
      VisibilityTimeout: 1500
      FifoQueue: true
      ContentBasedDeduplication: true
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt SQSBusEventsFifoDeadLetterQueue.Arn
        maxReceiveCount: 2

  SQSBusEventsFifoQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - !Ref SQSBusEventsFifoQueue
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: AllowLambdaToSendMessages
            Effect: Allow
            Principal:
              AWS: "*"
            Action: "sqs:SendMessage"
            Resource: !GetAtt SQSBusEventsFifoQueue.Arn
            Condition:
              ArnEquals:
                "aws:SourceArn": !GetAtt LambdaFunctionRole.Arn

  LambdaSQSFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ServiceName}-sqs
      Role: !GetAtt LambdaFunctionRole.Arn
      Runtime: nodejs20.x
      Timeout: 500
      MemorySize: 384
      AutoPublishAlias: live
      Tracing: Active
      Layers:
        - !Sub arn:aws:lambda:${AWS::Region}:580247275435:layer:LambdaInsightsExtension:2
      DeploymentPreference:
        Type: AllAtOnce
      CodeUri: ./build
      Handler: sqs-handler.handler
      Environment:
        Variables:
          ENV_NAME: !Ref EnvName
          DB_ENDPOINT: !ImportValue RDS-ENDPOINT
          DB_NAME: mainframe
          DB_SECRET: !ImportValue RDS-SECRET
          SQS_QUEUE_URL: !Ref SQSBusEventsQueue
          FIFO_SQS_QUEUE_URL: !Ref SQSBusEventsFifoQueue
          SNS_TOPIC_ARN: !ImportValue MAIN-SNS-EVENT-BUS-ARN
          NUBEFACT_KEY: !Ref NubefactKey
          NUBEFACT_URL: !Ref NubefactUrl
          CULQI_SECRET: !Ref CulqiSecret
          NODE_EXTRA_CA_CERTS: /var/task/us-east-1-bundle.pem
      VpcConfig:
        SecurityGroupIds:
          - !Ref LambdaSecurityGroup
        SubnetIds:
          - !ImportValue PRIVATE-SUBNET-A
          - !ImportValue PRIVATE-SUBNET-B
      Events:
        SQS:
          Type: SQS
          Properties:
            Queue: !GetAtt SQSBusEventsQueue.Arn
            BatchSize: 10
            FunctionResponseTypes:
              - ReportBatchItemFailures
        FIFOSQS:
          Type: SQS
          Properties:
            Queue: !GetAtt SQSBusEventsFifoQueue.Arn
            BatchSize: 1
            FunctionResponseTypes:
              - ReportBatchItemFailures

  #----------------------------------------------------------------
  # Lambda for scheduled cron events
  #----------------------------------------------------------------
  LambdaScheduledFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ServiceName}-cron
      Role: !GetAtt LambdaFunctionRole.Arn
      Runtime: nodejs20.x
      Timeout: 500
      MemorySize: 256
      AutoPublishAlias: live
      Tracing: Active
      Layers:
        - !Sub arn:aws:lambda:${AWS::Region}:580247275435:layer:LambdaInsightsExtension:2
      DeploymentPreference:
        Type: AllAtOnce
      CodeUri: ./build
      Handler: cron-handler.handler
      Environment:
        Variables:
          ENV_NAME: !Ref EnvName
          DB_ENDPOINT: !ImportValue RDS-ENDPOINT
          DB_NAME: mainframe
          DB_SECRET: !ImportValue RDS-SECRET
          SQS_QUEUE_URL: !Ref SQSBusEventsQueue
          SNS_TOPIC_ARN: !ImportValue MAIN-SNS-EVENT-BUS-ARN
          CULQI_SECRET: !Ref CulqiSecret
          NODE_EXTRA_CA_CERTS: /var/task/us-east-1-bundle.pem
      VpcConfig:
        SecurityGroupIds:
          - !Ref LambdaSecurityGroup
        SubnetIds:
          - !ImportValue PRIVATE-SUBNET-A
          - !ImportValue PRIVATE-SUBNET-B
      Events:
        RecurrentChargeSchedule:
          Type: ScheduleV2
          Properties:
            Input: >
              {
                "type": "recurrent-charge"
              }
            ScheduleExpression: cron(0 4 * * ? *)
            ScheduleExpressionTimezone: America/Lima

  #----------------------------------------------------------------
  # Lambda for scheduled events
  #----------------------------------------------------------------
  LambdaStandaloneScheduledFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ServiceName}-schedule
      Role: !GetAtt LambdaFunctionRole.Arn
      Runtime: nodejs20.x
      Timeout: 500
      MemorySize: 256
      AutoPublishAlias: live
      Tracing: Active
      Layers:
        - !Sub arn:aws:lambda:${AWS::Region}:580247275435:layer:LambdaInsightsExtension:2
      DeploymentPreference:
        Type: AllAtOnce
      CodeUri: ./build
      Handler: schedule-handler.handler
      Environment:
        Variables:
          ENV_NAME: !Ref EnvName
          DB_ENDPOINT: !ImportValue RDS-ENDPOINT
          DB_SECRET: !ImportValue RDS-SECRET
          SQS_QUEUE_URL: !Ref SQSBusEventsQueue
          SNS_TOPIC_ARN: !ImportValue MAIN-SNS-EVENT-BUS-ARN
          CULQI_SECRET: !Ref CulqiSecret
          NODE_EXTRA_CA_CERTS: /var/task/us-east-1-bundle.pem
          MODULE_ASSETS_S3_BUCKET: !Ref S3ModuleAssetsBucket
      VpcConfig:
        SecurityGroupIds:
          - !Ref LambdaSecurityGroup
        SubnetIds:
          - !ImportValue PRIVATE-SUBNET-A
          - !ImportValue PRIVATE-SUBNET-B

  LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/lambda/${ServiceName}-api

  S3PublicInfluencerAssetsBucket:
    Type: AWS::S3::Bucket
    Properties:
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
      OwnershipControls:
        Rules:
          - ObjectOwnership: ObjectWriter
      VersioningConfiguration:
        Status: Enabled
      CorsConfiguration:
        CorsRules:
          - AllowedOrigins:
              - "*"
            AllowedMethods:
              - GET
            AllowedHeaders:
              - "*"
            MaxAge: 3000
      Tags:
        - Key: "Purpose"
          Value: "Public assets storage for influencers"

  S3ModuleAssetsBucket:
    Type: AWS::S3::Bucket
    Properties:
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
      OwnershipControls:
        Rules:
          - ObjectOwnership: ObjectWriter
      VersioningConfiguration:
        Status: Suspended
      CorsConfiguration:
        CorsRules:
          - AllowedOrigins:
              - "*"
            AllowedMethods:
              - GET
            AllowedHeaders:
              - "*"
            MaxAge: 3000
      Tags:
        - Key: "Purpose"
          Value: "Public assets for the Mainframe modules"
