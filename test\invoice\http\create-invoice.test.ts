import { Enums } from "@mainframe-peru/types/build/common";
import request from "supertest";
import { influencerService } from "../../../src/influencer";
import { invoiceService } from "../../../src/invoice";
import { planService } from "../../../src/plan";
import { app } from "../../../src/api-handler";
import { transactionService } from "../../../src/transaction";
import { userService } from "../../../src/user";
import { userPaymentProviderService } from "../../../src/user-payment-provider";
import { TestData, adminAuthToken } from "../../common";
import { mockNubefactResponse } from "../../mocks";
import { common, invoice as it } from "@mainframe-peru/types";
import { sc } from "../../../src/services";
import { PublishCommand } from "@aws-sdk/client-sns";
import { transactionDetailService } from "../../../src/transaction-detail/service";
import { recurrenceService } from "../../../src/recurrence";
import { productService } from "../../../src/product/service";

jest.mock("node-fetch");

import fetch from "node-fetch";

const f = fetch as unknown as jest.Mock<typeof fetch>;

describe("create invoice tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await productService.create(TestData.product);
    await userPaymentProviderService.create({
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      userId: TestData.user.id,
      value: "test",
    });
    f.mockReset();
  });

  test("should fail to create an invoice for a transaction with an invalid state", async () => {
    await planService.create(TestData.plan);

    const transaction = await transactionService.create({
      ...TestData.transaction,
      state: "PROCESSING",
    });
    await transactionDetailService.create(TestData.transactionDetail);
    await recurrenceService.create(TestData.recurrence);

    const response = await request(app)
      .post("/live/invoice")
      .set("Cookie", `session=${await adminAuthToken}`)
      .send({ transactionId: transaction.id });

    expect(response.status).toBe(400);
    const errorBody = response.body as common.HttpApiError;
    expect(errorBody.code).toEqual("InvalidStateTransaction");

    const storedInvoice = await invoiceService.get(
      "transactionId",
      transaction.id,
    );

    expect(storedInvoice).toBeUndefined();
  });

  test("should create and send an invoice", async () => {
    const snsMock = jest.fn();
    sc.sns.send = snsMock;
    const nubefactMockResponse = mockNubefactResponse();

    await planService.create({
      ...TestData.plan,
    });

    const transaction = await transactionService.create({
      ...TestData.transaction,
      currency: "PEN",
      state: "SUCCESS",
    });
    await transactionDetailService.create(TestData.transactionDetail);
    await recurrenceService.create(TestData.recurrence);

    const response = await request(app)
      .post("/live/invoice")
      .set("Cookie", `session=${await adminAuthToken}`)
      .send({ transactionId: transaction.id });

    expect(response.status).toBe(200);
    const responseBody: it.http.CreateInvoiceResponse = response.body;
    expect(response.body).toEqual({
      invoiceId: expect.any(Number),
      link: nubefactMockResponse.enlace_del_pdf,
      number: nubefactMockResponse.numero,
      serie: "BBB8",
    });

    const mockedBody = f.mock.calls[0][1].body;
    expect(f).toHaveBeenCalledTimes(1);
    expect(JSON.parse(mockedBody).items[0]).toEqual({
      unidad_de_medida: "ZZ",
      codigo: TestData.product.sunatCode,
      codigo_producto_sunat: "90150000",
      descripcion: "Servicio",
      cantidad: TestData.transactionDetail.quantity,
      anticipo_regularizacion: false,
      anticipo_documento_serie: "",
      anticipo_documento_numero: "",
      valor_unitario: 0.85,
      precio_unitario: 1,
      subtotal: 0.85,
      igv: 0.15,
      total: 1,
      tipo_de_igv: 1,
    });

    const storedInvoice = await invoiceService.get(
      "id",
      responseBody.invoiceId,
    );

    expect(storedInvoice).toBeDefined();

    // Expect email to be sent
    expect(snsMock).toHaveBeenCalledTimes(1);
    const command = snsMock.mock.lastCall[0] as PublishCommand;
    expect(command.input).toEqual({
      Message: expect.any(String),
      MessageAttributes: {
        service: {
          DataType: "String",
          StringValue: "email",
        },
      },
      TopicArn: sc.vars.snsTopicArn,
    });
    expect(command.input.Message).toContain(TestData.user.email);
    expect(command.input.Message).toContain(TestData.user.firstName);
    expect(command.input.Message).toContain(
      `Notificaciones PChuJoy <notifications@${sc.vars.env}.pchujoy.app>`,
    );
    expect(command.input.Message).toContain("invoice");
  });

  test("should create an invoice with USD currency", async () => {
    const snsMock = jest.fn();
    sc.sns.send = snsMock;
    const nubefactMockResponse = mockNubefactResponse();

    await planService.create({
      ...TestData.plan,
    });
    await userService.update(TestData.user.id, {
      country: "US",
    });

    const transaction = await transactionService.create({
      ...TestData.transaction,
      currency: "USD",
      state: "SUCCESS",
    });
    await transactionDetailService.create(TestData.transactionDetail);
    await recurrenceService.create(TestData.recurrence);

    const response = await request(app)
      .post("/live/invoice")
      .set("Cookie", `session=${await adminAuthToken}`)
      .send({ transactionId: transaction.id });

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      invoiceId: expect.any(Number),
      link: nubefactMockResponse.enlace_del_pdf,
      number: nubefactMockResponse.numero,
      serie: "BBB8",
    });

    const storedInvoice = await invoiceService.get(
      "transactionId",
      transaction.id,
    );

    const mockedBody = f.mock.calls[0][1].body;
    expect(f).toHaveBeenCalledTimes(1);
    expect(JSON.parse(mockedBody).items[0]).toEqual({
      unidad_de_medida: "ZZ",
      codigo: TestData.product.sunatCode,
      codigo_producto_sunat: "90150000",
      descripcion: "Servicio",
      cantidad: TestData.transactionDetail.quantity,
      anticipo_regularizacion: false,
      anticipo_documento_serie: "",
      anticipo_documento_numero: "",
      valor_unitario: 1,
      precio_unitario: 1,
      subtotal: 1,
      igv: 0,
      total: 1,
      tipo_de_igv: 16,
    });

    expect(storedInvoice).toBeDefined();

    // Expect email to be sent
    expect(snsMock).toHaveBeenCalledTimes(1);
  });
});
