import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { report } from "@mainframe-peru/types";
import { reportService } from "../service";

export const getActiveRecurrenceCountEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  report.GetActiveRecurrenceResponseCount
> = async ({ request, response, state }) => {
  const result = await reportService.getActiveRecurrenceCount(
    state.auth.iss === "mainframe:officer"
      ? request.body.influencerId
      : state.auth.influencerId,
  );
  response.set("Cache-Control", "s-maxage=1800");
  response.body = result;
};
