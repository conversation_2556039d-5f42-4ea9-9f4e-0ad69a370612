import { card } from "@mainframe-peru/types";
import { ServiceBase } from "../service-base";
import { CardEntity, cardRepository } from "./repository";

class CardService extends ServiceBase<typeof cardRepository, CardEntity> {
  constructor() {
    super(cardRepository);
  }

  async listByUserId(userId: number): Promise<CardEntity[]> {
    return await this.repository.findByUserId(userId);
  }

  async getDefaultUserCard(userId: number): Promise<CardEntity | undefined> {
    return await this.repository.getDefaultUserCard(userId);
  }

  toPublic(card: CardEntity): card.PublicUserCard {
    return {
      id: card.id,
      brand: card.brand,
      default: card.default,
      number: card.number.slice(12).padStart(16, "*"),
      provider: card.paymentProvider,
      createdAt: card.createdAt?.toISOString() || "",
      updatedAt: card.updatedAt?.toISOString() || "",
    };
  }
}

export const cardService = new CardService();
