import { and, eq, InferSelectModel } from "drizzle-orm";
import { transformNullToUndefined } from "../helpers";
import { RepositoryBase } from "../repository-base";
import { paymentProviderEnum, userPaymentProviderTable } from "../schema";
import { sc } from "../services";

export type UserPaymentProviderEntity = InferSelectModel<
  typeof userPaymentProviderTable
>;

class UserPaymentProviderRepository extends RepositoryBase<
  typeof userPaymentProviderTable
> {
  constructor() {
    super(userPaymentProviderTable);
  }

  async findUserPaymentProvider(
    userId: number,
    provider: string,
  ): Promise<UserPaymentProviderEntity | undefined> {
    const result = await (
      await sc.getDB()
    )
      .select()
      .from(this.table)
      .where(
        and(
          eq(this.table.userId, userId),
          eq(
            this.table.paymentProvider,
            provider as (typeof paymentProviderEnum.enumValues)[number],
          ),
        ),
      )
      .limit(1);
    return transformNullToUndefined(result[0]) as UserPaymentProviderEntity;
  }
}

export const userPaymentProviderRepository =
  new UserPaymentProviderRepository();
