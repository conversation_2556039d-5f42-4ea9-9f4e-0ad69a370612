import { and, eq, InferSelectModel, or, sql, SQL } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { adminTable } from "../schema";
import { sc } from "../services";
import { NTU, transformNullToUndefined } from "../helpers";

export type AdminEntity = InferSelectModel<typeof adminTable>;

class AdminRepository extends RepositoryBase<typeof adminTable> {
  constructor() {
    super(adminTable);
  }

  async findAdmin(
    influencerId: string,
    email: string,
  ): Promise<AdminEntity | undefined> {
    const result = await (
      await sc.getDB()
    )
      .select()
      .from(this.table)
      .where(
        and(
          eq(this.table.influencerId, influencerId),
          eq(this.table.email, email),
        ),
      )
      .limit(1);
    return result[0] as AdminEntity;
  }

  async findAdmins(filter: {
    influencerId?: string;
    compound?: string;
  }): Promise<NTU<AdminEntity>[]> {
    const query = (await sc.getDB()).select().from(this.table);

    const conditions: (SQL | undefined)[] = [];

    if (filter.influencerId) {
      conditions.push(eq(this.table.influencerId, filter.influencerId));
    }

    if (filter.compound) {
      conditions.push(
        or(
          or(
            sql`${sql`LOWER(${this.table.firstName})`} LIKE ${`%${filter.compound.toLowerCase()}%`}`,
            sql`${sql`LOWER(${this.table.lastName})`} LIKE ${`%${filter.compound.toLowerCase()}%`}`,
          ),
          sql`${sql`LOWER(${this.table.email})`} LIKE ${`%${filter.compound.toLowerCase()}%`}`,
        ),
      );
    }

    query.where(and(...conditions));
    return transformNullToUndefined(await query);
  }
}

export const adminRepository = new AdminRepository();
