import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { listTransactions } from "./list-transactions";
import { getTransaction } from "./get-transaction";
import { publicGetTransaction } from "./public-get-transaction";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.get(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      transaction: ["READ_TRANSACTION"],
    },
    admin: {
      transaction: ["READ_TRANSACTION"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  getTransaction,
);

router.get(
  "/list-transactions",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      transaction: ["LIST_TRANSACTIONS"],
    },
    admin: {
      transaction: ["LIST_TRANSACTIONS"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  listTransactions,
);

// Public
router.get(
  "/public",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    user: {
      general: ["TRANSIENT"],
    },
  }),
  publicGetTransaction,
);

export const transactionRouter = router;
