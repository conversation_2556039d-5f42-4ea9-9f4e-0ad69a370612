import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { mockClient } from "aws-sdk-client-mock";
import { eventService } from "../src/event";
import { influencerService } from "../src/influencer";
import { handler } from "../src/schedule-handler";
import { userService } from "../src/user";
import { userEventParticipationService } from "../src/user-event-participation/service";
import { TestData } from "./common";
import { productService } from "../src/product/service";
import "aws-sdk-client-mock-jest";
import { sc } from "../src/services";

describe("schedule-handler", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await productService.create(TestData.product);
  });

  test("handle event list generation", async () => {
    const s3Mock = mockClient(S3Client);

    s3Mock.on(PutObjectCommand).resolves({});

    await eventService.create({
      ...TestData.event,
      type: "IN-PERSON",
    });

    for (let i = 0; i < 3; i++) {
      const u = await userService.create({
        ...TestData.user,
        id: undefined,
        email: `josue+${i}@mail.com`,
      });
      await userEventParticipationService.create({
        eventId: TestData.event.id,
        quantity: 1,
        userId: u.id,
      });
    }

    await handler({
      type: "event-list-generation",
      eventId: TestData.event.id,
      influencerId: TestData.influencer.id,
    });

    expect(s3Mock).toHaveReceivedCommandTimes(PutObjectCommand, 1);

    const event = await eventService.get("id", TestData.event.id);
    expect(event?.eventListUrl).toEqual(
      `https://${sc.vars.modulesStorageBucket}.s3.us-east-1.amazonaws.com/events/participants-list/event-${TestData.event.id}.csv`,
    );
  });
});
