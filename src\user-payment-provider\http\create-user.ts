import { Middleware } from "@koa/router";
import {
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { paymentProviderEventService } from "../../payment-provider-event/service";
import { userPaymentProviderService } from "../service";

export const createUser: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  null
> = async ({ response, state }) => {
  const userId = Number(state.auth.id);
  response.body = null;

  const userProvider = await userPaymentProviderService.get("userId", userId);
  if (userProvider?.paymentProvider === "CULQI") {
    return;
  }

  const culqiCustomer =
    await paymentProviderEventService.executeUserCreation(userId);
  await userPaymentProviderService.create({
    paymentProvider: "CULQI",
    userId: userId,
    value: culqiCustomer.id,
  });
};
