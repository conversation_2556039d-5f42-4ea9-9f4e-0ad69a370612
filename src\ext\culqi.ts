import { ext } from "@mainframe-peru/types";
import fetch, { Response } from "node-fetch";
import { AppError } from "@mainframe-peru/common-core";

type OkResponse<T = null> = {
  ok: true;
  json: () => Promise<T>;
};

type ErrorResponse = {
  ok: false;
  json(): Promise<ext.culqi.ErrorResponse>;
};

type AppResponse<T = null> = (OkResponse<T> | ErrorResponse) & Response;

class CulqiApi {
  headers(culqiSecret: string | undefined): Record<string, string> {
    if (!culqiSecret) {
      throw new AppError({
        code: "InvalidCulqiConfiguration",
        message: "La llave de Culqi es incorrecta",
        statusCode: "INTERNAL_SERVER_ERROR",
        logLevel: "ERROR",
      });
    }
    return {
      "Content-Type": "application/json",
      Authorization: `Bearer ${culqiSecret}`,
    };
  }

  async createCustomer(
    body: ext.culqi.CreateCustomerRequest,
    culqiSecret: string | undefined,
  ): Promise<AppResponse<ext.culqi.CreateCustomerResponse>> {
    return (await fetch("https://api.culqi.com/v2/customers", {
      method: "POST",
      headers: this.headers(culqiSecret),
      body: JSON.stringify(body),
    })) as AppResponse<ext.culqi.CreateCustomerResponse>;
  }

  async updateCustomer(
    customerId: string,
    body: ext.culqi.UpdateCustomerRequest,
    culqiSecret: string | undefined,
  ): Promise<AppResponse<ext.culqi.UpdateCustomerResponse>> {
    return (await fetch(`https://api.culqi.com/v2/customers/${customerId}`, {
      method: "PATCH",
      headers: this.headers(culqiSecret),
      body: JSON.stringify(body),
    })) as AppResponse<ext.culqi.UpdateCustomerResponse>;
  }

  async createCard(
    body: ext.culqi.CreateCardRequest,
    culqiSecret: string | undefined,
  ): Promise<AppResponse<ext.culqi.CreateCardResponse>> {
    return (await fetch("https://api.culqi.com/v2/cards", {
      method: "POST",
      headers: this.headers(culqiSecret),
      body: JSON.stringify(body),
    })) as AppResponse<ext.culqi.CreateCardResponse>;
  }

  async deleteCard(
    cardId: string,
    culqiSecret: string | undefined,
  ): Promise<AppResponse<ext.culqi.DeleteCardResponse>> {
    return (await fetch(`https://api.culqi.com/v2/cards/${cardId}`, {
      method: "DELETE",
      headers: this.headers(culqiSecret),
    })) as AppResponse<ext.culqi.DeleteCardResponse>;
  }

  async createCharge(
    body: ext.culqi.CreateChargeRequest,
    culqiSecret: string | undefined,
  ): Promise<AppResponse<ext.culqi.CreateChargeResponse>> {
    return (await fetch("https://api.culqi.com/v2/charges", {
      method: "POST",
      headers: {
        ...this.headers(culqiSecret),
        "x-charge-channel": "recurrent",
      },
      body: JSON.stringify(body),
    })) as AppResponse<ext.culqi.CreateChargeResponse>;
  }
}

export const culqiApi = new CulqiApi();
