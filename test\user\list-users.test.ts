import { common, user } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { userService } from "../../src/user";
import { admin<PERSON>uthToken, officerAuthToken, TestData } from "../common";
import { recurrenceService } from "../../src/recurrence";
import { planService } from "../../src/plan";
import { generateRandomAlias } from "../../src/helpers";
import { attributeService } from "../../src/attribute";
import { productService } from "../../src/product/service";

describe("list users tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await productService.create(TestData.product);
    const plan = await planService.create(TestData.plan);
    for (const i of [0, 1]) {
      const user = await userService.create({
        email: "<EMAIL>",
        firstName: "Generic",
        lastName: "Test",
        alias: generateRandomAlias(),
        phone: "928192929",
        gender: common.Enums.Gender.Values.M,
        birthDate: new Date(),
        documentType: "DNI",
        documentValue: "7777777",
        influencerId: TestData.influencer.id,
      });
      if (i === 0) {
        await recurrenceService.create({
          planId: plan.id,
          renewalDate: new Date(),
          status: "ACTIVE",
          type: "CARD",
          userId: user.id,
        });
      }
    }
  });

  test("lists users by compound (json)", async () => {
    const queryParams: user.ListUsersRequest = {
      influencerId: TestData.influencer.id,
      detailed: true,
      compound: "7777",
    };

    const response = await request(app)
      .get("/live/user/list-users")
      .query(queryParams)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await officerAuthToken}`);

    const responseBody = response.body as user.ListUsersResponse;

    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(2);
    expect(responseBody[0].activeRecurrence).toBeUndefined();
    expect(responseBody[1].activeRecurrence?.id).toEqual(expect.any(Number));
    expect(responseBody[1].activeRecurrence?.plan).toEqual(TestData.plan.name);
  });

  test("list users by status (json)", async () => {
    const queryParams: user.ListUsersRequest = {
      influencerId: TestData.influencer.id,
      detailed: true,
      hasRecurrence: true,
    };

    const response = await request(app)
      .get("/live/user/list-users")
      .query(queryParams)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await officerAuthToken}`);

    const responseBody = response.body as user.ListUsersResponse;

    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(1);
    expect(responseBody[0].activeRecurrence?.id).toEqual(expect.any(Number));
  });

  test("list users (csv)", async () => {
    const queryParams: user.ListUsersRequest = {
      influencerId: TestData.influencer.id,
      detailed: true,
    };

    const response = await request(app)
      .get("/live/user/list-users")
      .query(queryParams)
      .set("Accept", "text/csv")
      .set("Cookie", `session=${await officerAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.text.split("\n").length).toEqual(3);
    expect((response.text.match(/928192929/g) || []).length).toEqual(2);
    expect((response.text.match(/CARD/g) || []).length).toEqual(1);
    expect((response.text.match(/<EMAIL>/g) || []).length).toEqual(2);
    expect(
      (response.text.match(new RegExp(TestData.plan.name, "g")) || []).length,
    ).toEqual(1);
  });

  test("list all 200 users (csv)", async () => {
    const userCount = 100;
    for (let i = 3; i < userCount; i++) {
      await userService.create({
        email: "<EMAIL>",
        firstName: "Generic",
        lastName: "Test",
        alias: crypto.randomUUID(),
        phone: "928192929",
        gender: common.Enums.Gender.Values.M,
        birthDate: new Date(),
        documentType: "DNI",
        documentValue: "7777777",
        influencerId: TestData.influencer.id,
      });
    }
    const queryParams: user.ListUsersRequest = {
      detailed: true,
      limit: -1,
    };

    const response = await request(app)
      .get("/live/user/list-users")
      .query(queryParams)
      .set("Accept", "text/csv")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.text.split("\n").length).toEqual(userCount);
  });

  test("list 100 users with attributes (csv)", async () => {
    const userCount = 20;
    const f1 = await attributeService.create({
      id: "field1",
      influencerId: TestData.influencer.id,
      entity: "USER",
      text: "example",
      type: "MULTI",
      options: [],
    });
    for (let i = 0; i < userCount; i++) {
      await userService.create({
        email: "<EMAIL>",
        firstName: "Generic",
        lastName: "Test",
        alias: crypto.randomUUID(),
        phone: "928192929",
        gender: common.Enums.Gender.Values.M,
        birthDate: new Date(),
        documentType: "DNI",
        documentValue: "7777777",
        influencerId: TestData.influencer.id,
        attributes: {
          [f1.id]: "uni",
        },
      });
    }
    const queryParams: user.ListUsersRequest = {
      detailed: true,
      limit: -1,
    };

    const response = await request(app)
      .get("/live/user/list-users")
      .query(queryParams)
      .set("Accept", "text/csv")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.text.split("\n").length).toEqual(userCount + 3);
    for (const [i, section] of response.text.split("\n").entries()) {
      if (i !== 0 && i < 21) expect(section).toContain("uni");
    }
  });

  test("list users with offset", async () => {
    const queryParams: user.ListUsersRequest = {
      offset: 2,
    };

    const response = await request(app)
      .get("/live/user/list-users")
      .query(queryParams)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body.length).toBe(0);
  });
});
