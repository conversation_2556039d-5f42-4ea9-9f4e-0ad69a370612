import {
  and,
  desc,
  eq,
  getTableColumns,
  gt,
  InferSelectModel,
  isNotNull,
  isNull,
  like,
  lt,
  or,
  sql,
  SQL,
} from "drizzle-orm";
import { addLimitAndOffsetToQuery } from "../common";
import { NTU, transformNullToUndefined } from "../helpers";
import { RepositoryBase } from "../repository-base";
import { planTable, recurrenceTable, userTable } from "../schema";
import { sc } from "../services";

export type UserEntity = InferSelectModel<typeof userTable>;
type UserActiveRecurrentPaymentEntity = {
  activeRecurrentPaymentId: number;
  activeRecurrentPaymentType: string;
  activeRecurrentPaymentStartDate: Date;
  activeRecurrentPaymentRenewalDate: Date;
  activeRecurrentPlan: string;
};

export type DetailedUserEntity = UserEntity &
  Partial<UserActiveRecurrentPaymentEntity> & {
    attributeValues?: Record<string, unknown>;
  };

class UserRepository extends RepositoryBase<typeof userTable> {
  constructor() {
    super(userTable);
  }

  async findUser(
    influencerId: string,
    email: string,
  ): Promise<UserEntity | undefined> {
    const result = await (
      await sc.getDB()
    )
      .select()
      .from(this.table)
      .where(
        and(
          eq(this.table.influencerId, influencerId),
          eq(this.table.email, email),
        ),
      )
      .limit(1);
    return transformNullToUndefined(result[0]) as UserEntity;
  }

  async findUsers(filter: {
    detailed?: boolean;
    influencerId?: string;
    name?: string;
    documentValue?: string;
    phone?: string;
    compound?: string; // Searches by name or documentValue or phone
    hasRecurrence?: boolean;
    creationStart?: Date;
    creationEnd?: Date;
    limit?: number;
    offset?: number;
  }): Promise<NTU<DetailedUserEntity>[]> {
    const activeRecurrentPaymentJoin = filter.detailed
      ? {
          activeRecurrentPaymentId: recurrenceTable.id,
          activeRecurrentPaymentType: recurrenceTable.type,
          activeRecurrentPaymentStartDate: recurrenceTable.startDate,
          activeRecurrentPaymentRenewalDate: recurrenceTable.renewalDate,
          activeRecurrentPlan: planTable.name,
        }
      : undefined;

    const query = (await sc.getDB())
      .select({
        ...getTableColumns(this.table),
        ...activeRecurrentPaymentJoin,
      })
      .from(this.table);

    if (filter.detailed) {
      query
        .leftJoin(
          recurrenceTable,
          and(
            eq(this.table.id, recurrenceTable.userId),
            eq(recurrenceTable.status, "ACTIVE"),
          ),
        )
        .leftJoin(planTable, eq(recurrenceTable.planId, planTable.id));
    }

    const conditions: (SQL | undefined)[] = [];

    if (filter.compound) {
      conditions.push(
        or(
          or(
            sql`${sql`LOWER(${this.table.firstName})`} LIKE ${`%${filter.compound.toLowerCase()}%`}`,
            sql`${sql`LOWER(${this.table.lastName})`} LIKE ${`%${filter.compound.toLowerCase()}%`}`,
          ),
          sql`${sql`LOWER(${this.table.email})`} LIKE ${`%${filter.compound.toLowerCase()}%`}`,
          sql`${sql`LOWER(${this.table.documentValue})`} LIKE ${`%${filter.compound.toLowerCase()}%`}`,
          sql`${sql`LOWER(${this.table.phone})`} LIKE ${`%${filter.compound.toLowerCase()}%`}`,
        ),
      );
    } else {
      if (filter.influencerId) {
        conditions.push(eq(this.table.influencerId, filter.influencerId));
      }
      if (filter.name) {
        conditions.push(
          or(
            like(this.table.firstName, `%${filter.name}%`),
            like(this.table.lastName, `%${filter.name}%`),
          ),
        );
      }
      if (filter.documentValue) {
        conditions.push(eq(this.table.documentValue, filter.documentValue));
      }
      if (filter.phone) {
        conditions.push(eq(this.table.phone, filter.phone));
      }
      if (filter.detailed && filter.hasRecurrence !== undefined) {
        if (filter.hasRecurrence) {
          conditions.push(isNotNull(recurrenceTable.id));
        } else {
          conditions.push(isNull(recurrenceTable.id));
        }
      }

      if (filter.creationStart) {
        conditions.push(gt(this.table.createdAt, filter.creationStart));
      }
      if (filter.creationEnd) {
        conditions.push(lt(this.table.createdAt, filter.creationEnd));
      }
    }

    query.where(and(...conditions));
    query.orderBy(desc(this.table.id));
    addLimitAndOffsetToQuery(filter, query);

    return transformNullToUndefined(await query);
  }

  async getTotalUsers(influencerId: string): Promise<number> {
    const db = await sc.getDB();
    return await db.$count(
      this.table,
      eq(this.table.influencerId, influencerId),
    );
  }
}

export const userRepository = new UserRepository();
