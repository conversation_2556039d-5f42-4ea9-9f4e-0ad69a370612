import { faker } from "@faker-js/faker";
import { ServiceBase } from "../service-base";
import {
  BusinessPromotionCodeEntity,
  businessPromotionCodeRepository,
} from "./repository";
import { businessPromotionCode as bpc } from "@mainframe-peru/types";

class BusinessPromotionCodeService extends ServiceBase<
  typeof businessPromotionCodeRepository,
  BusinessPromotionCodeEntity
> {
  constructor() {
    super(businessPromotionCodeRepository);
  }

  deleteCodes: (typeof businessPromotionCodeRepository)["deleteCodes"] = (f) =>
    this.repository.deleteCodes(f);
  insertMultiple: (typeof businessPromotionCodeRepository)["insertMultiple"] = (
    f,
  ) => this.repository.insertMultiple(f);
  getByUser: (typeof businessPromotionCodeRepository)["getByUser"] = (f) =>
    this.repository.getByUser(f);
  getUserOrAvailableCode: (typeof businessPromotionCodeRepository)["getUserOrAvailableCode"] =
    (f) => this.repository.getUserOrAvailableCode(f);

  async generateCodes(input: bpc.GeneratePromotionCodesRequest): Promise<void> {
    const codes: string[] = faker.helpers.multiple(
      () =>
        faker.string.alphanumeric({
          casing: "upper",
          length: 6,
        }),
      {
        count: input.amount,
      },
    );
    await this.repository.insertMultiple({
      codes,
      promotionId: input.promotionId,
    });
  }
}

export const businessPromotionCodeService = new BusinessPromotionCodeService();
