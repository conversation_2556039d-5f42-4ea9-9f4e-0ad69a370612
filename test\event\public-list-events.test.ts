import { event } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { TestData } from "../common";
import { eventService } from "../../src/event";

describe("publicly list events tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
  });

  test("publicly list events", async () => {
    for (let i = 0; i < 5; i++) {
      await eventService.create({
        influencerId: TestData.influencer.id,
        name: "Test event",
        description: "New description",
        type: "PRIZE",
        status: i & 1 ? "ACTIVE" : "INACTIVE",
        imageUrl: "www.pchujoy.com",
        startDate: new Date(),
        location: "Lima",
      });
    }

    const queryParams: event.PublicListEventsRequest = {
      influencerId: TestData.influencer.id,
      sortBy: "id",
      sortDirection: "DESCENDANT",
    };

    const response = await request(app)
      .get("/live/event/public-list-events")
      .query(queryParams)
      .set("Accept", "application/json");

    const responseBody = response.body as event.PublicListEventsResponse;

    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(2);
    expect(responseBody[0].location).toEqual("Lima");
  });

  test("publicly list past events", async () => {
    for (let i = 0; i < 5; i++) {
      await eventService.create({
        influencerId: TestData.influencer.id,
        name: "Test event",
        description: "New description",
        type: "PRIZE",
        status: "INACTIVE",
        imageUrl: "www.pchujoy.com",
        eventDate: new Date(2000, i, 10),
      });
    }

    const queryParams: event.PublicListEventsRequest = {
      influencerId: TestData.influencer.id,
      status: "INACTIVE",
      startDate: new Date(2000, 0, 1),
      endDate: new Date(2000, 2, 30),
    };

    const response = await request(app)
      .get("/live/event/public-list-events")
      .query(queryParams)
      .set("Accept", "application/json");

    const responseBody = response.body as event.PublicListEventsResponse;

    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(3);
  });
});
