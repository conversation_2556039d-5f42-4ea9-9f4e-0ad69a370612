import { Middleware } from "@koa/router";
import {
  AppError,
  Auth,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { user as ut } from "@mainframe-peru/types";
import { userService } from "../service";
import { restrictParams } from "../../common";

export const updateUserEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  ut.UpdateUserResponse
> = async ({ request, response, state }) => {
  const body = ut.UpdateUserRequestSchema.parse(request.body);
  restrictParams(state.auth, body);

  const { userId, ...userUpdate } = body;
  if (!userId) {
    throw new AppError({
      code: "UserIdIsRequired",
      message: "Se requiere un userId",
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  }

  await userService.update(userId, userUpdate);
  response.body = {
    id: userId,
  };
};
