import { invoice, recurrence } from "@mainframe-peru/types";
import { handler } from "../src/sqs-handler";
import { TestData } from "./common";
import { influencerRepository } from "../src/influencer/repository";
import { userRepository } from "../src/user/repository";
import { planRepository } from "../src/plan/repository";
import { recurrenceRepository } from "../src/recurrence/repository";
import { transactionRepository } from "../src/transaction/repository";
import fetch, { Response } from "node-fetch";
import { productService } from "../src/product/service";

jest.mock("node-fetch");

describe("sqs-handler", () => {
  test("handle multiple events", async () => {
    await influencerRepository.create(TestData.influencer);
    await userRepository.create(TestData.user);
    await productService.create(TestData.product);
    await planRepository.create(TestData.plan);
    await recurrenceRepository.create(TestData.recurrence);
    await transactionRepository.create({
      ...TestData.transaction,
      state: "SUCCESS",
    });

    const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
    mockFetch.mockResolvedValueOnce({
      ok: false,
      text: jest.fn().mockReturnValue("test"),
    } as unknown as Response);
    const req1: invoice.sqs.CreateInvoiceRequest = {
      attempt: 0,
      invoiceDestination: "PERSON",
      transactionId: TestData.transaction.id,
    };
    const req2: recurrence.CancelRecurrenceRequest = {
      influencerId: TestData.influencer.id,
      userId: TestData.user.id,
    };
    const errorMessageId = crypto.randomUUID();
    const result = await handler(
      {
        Records: [
          {
            messageId: errorMessageId,
            receiptHandle: "AQEBjr5k0kh1Smzqs1g==",
            body: JSON.stringify(req1),
            attributes: {
              ApproximateReceiveCount: "1",
              AWSTraceHeader: "Root=1-6777a72cc;Sampled=1;Lineage=2:8b1ac1fe:0",
              SentTimestamp: "1735894823980",
              SenderId: "AROA47CR256MFHU73FIPT:core-backend-sqs",
              ApproximateFirstReceiveTimestamp: "1735894825041",
            },
            messageAttributes: {
              path: {
                stringValue: "/invoice/create",
                stringListValues: [],
                binaryListValues: [],
                dataType: "String",
              },
              method: {
                stringValue: "POST",
                stringListValues: [],
                binaryListValues: [],
                dataType: "String",
              },
              origin: {
                stringValue: "core-backend",
                stringListValues: [],
                binaryListValues: [],
                dataType: "String",
              },
            },
            md5OfBody: "0d5acfb4b0c779e9f889d3cf2572069d",
            md5OfMessageAttributes: "cd1b884d41fa147aa31c29339c5f1d10",
            eventSource: "aws:sqs",
            eventSourceARN:
              "arn:aws:sqs:us-east-1:************:core-backend-queue",
            awsRegion: "us-east-1",
          },
          {
            messageId: "b9a47352-207d-4bb9-9b43-19646753d365",
            receiptHandle: "AQEBjr5k0kh1Smzqs1g==",
            body: JSON.stringify(req2),
            attributes: {
              ApproximateReceiveCount: "1",
              AWSTraceHeader: "Root=1-6777a72cc;Sampled=1;Lineage=2:8b1ac1fe:0",
              SentTimestamp: "1735894823980",
              SenderId: "AROA47CR256MFHU73FIPT:core-backend-sqs",
              ApproximateFirstReceiveTimestamp: "1735894825041",
            },
            messageAttributes: {
              path: {
                stringValue: "/recurrence/cancellation",
                stringListValues: [],
                binaryListValues: [],
                dataType: "String",
              },
              method: {
                stringValue: "POST",
                stringListValues: [],
                binaryListValues: [],
                dataType: "String",
              },
              origin: {
                stringValue: "core-backend",
                stringListValues: [],
                binaryListValues: [],
                dataType: "String",
              },
            },
            md5OfBody: "0d5acfb4b0c779e9f889d3cf2572069d",
            md5OfMessageAttributes: "cd1b884d41fa147aa31c29339c5f1d10",
            eventSource: "aws:sqs",
            eventSourceARN:
              "arn:aws:sqs:us-east-1:************:core-backend-queue",
            awsRegion: "us-east-1",
          },
        ],
      },
      {
        awsRequestId: crypto.randomUUID(),
        callbackWaitsForEmptyEventLoop: false,
        functionName: "core-backend-sqs",
        functionVersion: "187",
        getRemainingTimeInMillis: () => 0,
        invokedFunctionArn:
          "arn:aws:sqs:us-east-1:************:core-backend-queue",
        logGroupName: "core-backend-sqs",
        logStreamName: crypto.randomUUID(),
        memoryLimitInMB: "500",
        succeed: () => undefined,
        done: () => undefined,
        fail: () => undefined,
      },
      () => undefined,
    );

    expect(result).toBeInstanceOf(Object);
    if (result instanceof Object) {
      expect(result.batchItemFailures.length).toEqual(1);
      expect(result.batchItemFailures[0].itemIdentifier).toEqual(
        errorMessageId,
      );
    }
  });
});
