import { Middleware } from "@koa/router";
import {
  AuthAdmin,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { business as bu } from "@mainframe-peru/types";
import { businessService } from "../service";
import { transformNullToUndefined } from "../../helpers";

export const listBusinessEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin | AuthEndUser>,
  unknown,
  bu.ListBusinessResponse
> = async ({ request, response, state }) => {
  const params = bu.ListBusinessRequestSchema.parse(request.query);

  const business = await businessService.findBusiness({
    ...params,
    influencerId: state.auth.influencerId,
  });

  response.body = business.map((b) =>
    transformNullToUndefined({
      id: b.id,
      name: b.name,
      description: b.description,
      imageUrl: b.imageUrl,
      ruc: b.ruc,
      createdAt: b.createdAt,
      updatedAt: b.updatedAt,
    }),
  );
};
