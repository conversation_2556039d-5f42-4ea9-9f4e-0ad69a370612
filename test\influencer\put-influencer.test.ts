import { influencer } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { officerAuthToken } from "../common";

describe("create or update influencer tests", () => {
  test("Create a new influencer", async () => {
    const influencerId = "netflix";
    const testEntity: influencer.CreateInfluencerRequest = {
      id: influencerId,
      name: "Netflix name",
      status: Enums.InfluencerStatus.Values.ACTIVE,
      logoUrl: "www",
      domain: "domain.com",
    };

    // Creation the influencer
    const response = await request(app)
      .post("/live/influencer")
      .send(testEntity)
      .set("Cookie", `session=${await officerAuthToken}`);

    expect(response.statusCode).toEqual(200);

    const entity = await influencerService.get("id", influencerId);

    expect(entity).toEqual(
      expect.objectContaining({
        id: testEntity.id,
        status: testEntity.status,
        logoUrl: testEntity.logoUrl,
        domain: testEntity.domain,
      }),
    );
  });

  //////////////////////

  /*test("Update an influencer", async () => {
    const influencerId = "nestle";
    const testEntity: InfluencerEntity = {
      id: influencerId,
      name: "Nestle name",
      status: "active",
      logoUrl: "www",
      createdAt: new Date(),
    };

    // Creating the influencer
    const responseCreation: influencer.CreateInfluencerResponse = (
      await request(app)
        .put("/live/influencer")
        .send(testEntity)
        .set("Cookie", `session=${await officerAuthToken}`)
    ).body;

    testEntity.name = "Elaine Company";

    // Updating the influencer
    await request(app)
      .put("/live/influencer")
      .send(testEntity)
      .set("Cookie", `session=${await officerAuthToken}`);

    const entity = await influencerService.get("id", influencerId);

    expect(entity).toEqual(
      expect.objectContaining({
        id: influencerId,
        logoUrl: testEntity.logoUrl,
        name: testEntity.name,
      }),
    );
  });*/
});
