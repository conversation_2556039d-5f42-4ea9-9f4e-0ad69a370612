import { userEventParticipation } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { eventService } from "../../src/event";
import { influencerService } from "../../src/influencer";
import { userService } from "../../src/user";
import { userEventInviteService } from "../../src/user-event-invite";
import { TestData, userAuthToken } from "../common";

describe("User Event Invite Creation", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
  });

  test("should create invites when adding user event participation with invite emails", async () => {
    const event = await eventService.create({
      influencerId: TestData.influencer.id,
      name: "Test Event",
      description: "Test Description",
      type: "PRIZE",
      status: "ACTIVE",
      imageUrl: "https://example.com/image.jpg",
      inviteLimit: 10,
      inviteForm: [
        {
          id: "name",
          text: "Full Name",
          type: "TEXT",
          required: true,
        },
      ],
    });

    const body: userEventParticipation.CreateUserEventParticipationRequest = {
      eventId: event.id,
      userId: TestData.user.id,
      inviteEmails: ["<EMAIL>", "<EMAIL>"],
    };

    const response = await request(app)
      .post("/live/user-event-participation")
      .send(body)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      userId: TestData.user.id,
      eventId: event.id,
      inviteIds: expect.any(Array),
    });

    // Verify invites were created
    const invites = await userEventInviteService.list({
      eventId: event.id,
    });
    expect(invites).toHaveLength(2);
    expect(invites.map((i) => i.email)).toContain("<EMAIL>");
    expect(invites.map((i) => i.email)).toContain("<EMAIL>");
  });

  test("should fail when invite limit is exceeded", async () => {
    const event = await eventService.create({
      influencerId: TestData.influencer.id,
      name: "Test Event",
      description: "Test Description",
      type: "PRIZE",
      status: "ACTIVE",
      imageUrl: "https://example.com/image.jpg",
      inviteLimit: 1,
    });

    const body: userEventParticipation.CreateUserEventParticipationRequest = {
      eventId: event.id,
      userId: TestData.user.id,
      inviteEmails: ["<EMAIL>", "<EMAIL>"],
    };

    const response = await request(app)
      .post("/live/user-event-participation")
      .send(body)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toEqual(400);
    expect(response.body.code).toEqual("InviteLimitExceeded");
  });

  test("should fail when duplicate invite email is used", async () => {
    const event = await eventService.create({
      influencerId: TestData.influencer.id,
      name: "Test Event",
      description: "Test Description",
      type: "PRIZE",
      status: "ACTIVE",
      imageUrl: "https://example.com/image.jpg",
    });

    // Create first invite
    await userEventInviteService.create({
      id: crypto.randomUUID(),
      eventId: event.id,
      userId: TestData.user.id,
      email: "<EMAIL>",
    });

    const body: userEventParticipation.CreateUserEventParticipationRequest = {
      eventId: event.id,
      userId: TestData.user.id,
      inviteEmails: ["<EMAIL>"],
    };

    const response = await request(app)
      .post("/live/user-event-participation")
      .send(body)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toEqual(400);
    expect(response.body.code).toEqual("InviteAlreadyExists");
  });
});
