import { SendMessageCommandInput } from "@aws-sdk/client-sqs";
import { payment } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import { add } from "date-fns";
import request from "supertest";
import { app } from "../../../src/api-handler";
import { cardService } from "../../../src/card/service";
import { influencerService } from "../../../src/influencer/service";
import { planService } from "../../../src/plan";
import { productService } from "../../../src/product/service";
import { recurrenceService } from "../../../src/recurrence";
import { sc } from "../../../src/services";
import { transactionService } from "../../../src/transaction";
import { userService } from "../../../src/user";
import { TestData, userAuthToken } from "../../common";
import { transactionDetailService } from "../../../src/transaction-detail/service";

describe("payment.execute", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await productService.create(TestData.product);
    await planService.create(TestData.plan);
  });

  test("create recurrent payment (card)", async () => {
    const sqsMock = jest.fn();
    sc.sqs.send = sqsMock;

    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    await cardService.create(TestData.card);

    const requestBody: payment.ExecutePaymentRequest = {
      entity: "RECURRENCE",
      source: {
        source: "CARD",
      },
      entityId: TestData.plan.id,
      invoiceDestination: TestData.recurrence.invoiceDestination,
    };
    const response = await request(app)
      .post("/live/payment")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);

    // Check recurrence has been created
    const savedRecurrence = await recurrenceService.get(
      "userId",
      TestData.user.id,
    );
    expect(savedRecurrence?.planId).toEqual(TestData.plan.id);
    expect(savedRecurrence?.status).toEqual("CREATING");
    expect(savedRecurrence?.invoiceDestination).toEqual(
      TestData.recurrence.invoiceDestination,
    );

    // Check SQS event has been sent
    expect(sqsMock).toHaveBeenCalled();
    const input = sqsMock.mock.calls[0][0].input as SendMessageCommandInput;

    expect(input.QueueUrl).toBe("mainframe");
    expect(input.MessageAttributes).toEqual({
      method: { DataType: "String", StringValue: "POST" },
      path: {
        DataType: "String",
        StringValue: "/payment-provider/charge",
      },
      origin: { DataType: "String", StringValue: "core-backend" },
    });
    expect(JSON.parse(input.MessageBody || "")).toEqual({
      userId: TestData.user.id,
      amount: TestData.product.amount,
      currency: TestData.product.currency,
      invoiceDestination: TestData.recurrence.invoiceDestination,
      productId: TestData.product.id,
      source: {
        source: "CARD",
      },
      variant: {
        variant: "RECURRENCE_CREATION",
        planId: TestData.plan.id,
        transactionId: expect.any(Number),
      },
    });

    const storedTransaction = await transactionService.get(
      "userId",
      TestData.user.id,
    );
    expect(storedTransaction?.amount).toEqual(TestData.product.amount);
    expect(storedTransaction?.channel).toEqual("CARD");

    const storedTransactionDetail = await transactionDetailService.get(
      "transactionId",
      storedTransaction?.id || 0,
    );
    expect(storedTransactionDetail).toEqual({
      amount: TestData.product.amount,
      entityId: savedRecurrence?.id,
      id: expect.any(Number),
      productId: TestData.product.id,
      quantity: 1,
      transactionId: storedTransaction?.id,
    });

    // Test a second recurrence creation fails
    const requestBody2: payment.ExecutePaymentRequest = {
      entity: "RECURRENCE",
      source: {
        source: "CARD",
      },
      entityId: TestData.plan.id,
      invoiceDestination: TestData.recurrence.invoiceDestination,
    };
    const response2 = await request(app)
      .post("/live/payment")
      .send(requestBody2)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response2.status).toBe(400);
    expect(response2.body.code).toBe("RecurrenceHasBeenQueued");
  });

  test("create recurrent payment (yape)", async () => {
    const sqsMock = jest.fn();
    sc.sqs.send = sqsMock;

    const token = "ype_" + crypto.randomUUID();
    const requestBody: payment.ExecutePaymentRequest = {
      entity: "RECURRENCE",
      entityId: TestData.plan.id,
      invoiceDestination: "COMPANY",
      source: {
        source: "YAPE",
        token,
      },
    };
    const response = await request(app)
      .post("/live/payment")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);

    // Check recurrence has been created
    const savedRecurrence = await recurrenceService.get(
      "userId",
      TestData.user.id,
    );
    expect(savedRecurrence?.planId).toEqual(TestData.plan.id);
    expect(savedRecurrence?.status).toEqual("CREATING");
    expect(savedRecurrence?.invoiceDestination).toEqual("COMPANY");

    // Check SQS event has been sent
    expect(sqsMock).toHaveBeenCalled();
    const input = sqsMock.mock.calls[0][0].input as SendMessageCommandInput;
    expect(input.QueueUrl).toBe("mainframe");
    expect(input.MessageAttributes).toEqual({
      method: { DataType: "String", StringValue: "POST" },
      path: {
        DataType: "String",
        StringValue: "/payment-provider/charge",
      },
      origin: { DataType: "String", StringValue: "core-backend" },
    });
    expect(JSON.parse(input.MessageBody || "")).toEqual({
      userId: TestData.user.id,
      amount: TestData.product.amount,
      currency: TestData.product.currency,
      invoiceDestination: "COMPANY",
      productId: TestData.product.id,
      source: {
        source: "YAPE",
        token,
      },
      variant: {
        variant: "RECURRENCE_CREATION",
        planId: TestData.plan.id,
        transactionId: expect.any(Number),
      },
    });

    const storedTransaction = await transactionService.get(
      "userId",
      TestData.user.id,
    );
    expect(storedTransaction?.amount).toEqual(TestData.product.amount);
    expect(storedTransaction?.channel).toEqual("YAPE");
  });

  test("double creation fails", async () => {
    await recurrenceService.create({
      planId: TestData.plan.id,
      renewalDate: new Date(),
      status: "CREATING",
      type: "MANUAL",
      userId: TestData.user.id,
    });

    //creating another recurrent payment fails
    const requestBody2: payment.ExecutePaymentRequest = {
      entity: "RECURRENCE",
      entityId: TestData.plan.id,
      invoiceDestination: "PERSON",
      source: {
        source: "YAPE",
        token: "token",
      },
    };
    const response2 = await request(app)
      .post("/live/payment")
      .send(requestBody2)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response2.status).toBe(400);
    expect(response2.body.code).toBe("RecurrenceHasBeenQueued");
  });

  test("renewing too soon fails", async () => {
    await recurrenceService.create({
      id: 12356,
      userId: TestData.user.id,
      type: Enums.RecurrenceType.Values.MANUAL,
      planId: TestData.plan.id,
      renewalDate: add(new Date(), { months: 1 }),
      startDate: new Date(),
      status: "ACTIVE",
    });

    const requestBody: payment.ExecutePaymentRequest = {
      entity: "RECURRENCE",
      entityId: TestData.plan.id,
      invoiceDestination: "PERSON",
      source: {
        source: "YAPE",
        token: "token",
      },
    };
    const response = await request(app)
      .post("/live/payment")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(400);
    expect(response.body.code).toBe("CannotRenewNow");
  });
});
