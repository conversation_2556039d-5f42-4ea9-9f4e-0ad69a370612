import { Middleware } from "@koa/router";
import {
  AppError,
  AuthAdmin,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { businessPromotion as bp } from "@mainframe-peru/types";
import { businessPromotionService } from "../service";
import { businessService } from "../../business/service";

export const createBusinessPromotionEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  bp.CreateBusinessPromotionResponse
> = async ({ request, response, state }) => {
  const body = bp.CreateBusinessPromotionRequestSchema.parse(request.body);

  const business = await businessService.get("id", body.businessId);

  if (!business || business.influencerId !== state.auth.influencerId) {
    throw new AppError({
      code: "BusinessNotFound",
      message: "Could not find business",
      statusCode: "NOT_FOUND",
      logLevel: "INFO",
    });
  }

  const businessPromotion = await businessPromotionService.create({
    businessId: body.businessId,
    influencerId: state.auth.influencerId,
    content: body.content,
    expirationDate: new Date(body.expirationDate),
    name: body.name,
    status: body.status || "ACTIVE",
    type: body.type,
    value: body.value,
    description: body.description,
    imageUrl: body.imageUrl,
    termsAndConditions: body.termsAndConditions,
  });

  response.body = businessPromotionService.getPublic({
    business,
    promotion: businessPromotion,
  });
};
