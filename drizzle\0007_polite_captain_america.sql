CREATE TABLE "core_backend"."product" (
	"id" serial PRIMARY KEY NOT NULL,
	"influencer_id" text NOT NULL,
	"amount" numeric NOT NULL,
	"currency" "core_backend"."currency" NOT NULL,
	"description" text,
	"sunat_code" text
);

-- insert into core_backend.product (amount, currency, description, sunat_code)
-- values ('7.99', 'PEN', 'Plan mensual', 'FCC-PMC'),
-- ('20.99', 'PEN', 'Plan trimestral', 'FCC-PTD');

--> statement-breakpoint
CREATE TABLE "core_backend"."user_event_participation" (
	"user_id" integer NOT NULL,
	"event_id" integer NOT NULL,
	"quantity" integer NOT NULL,
	CONSTRAINT "user_event_participation_user_id_event_id_pk" PRIMARY KEY("user_id","event_id")
);
--> statement-breakpoint
ALTER TABLE "core_backend"."event" ADD COLUMN "participation_product_id" integer;--> statement-breakpoint
ALTER TABLE "core_backend"."plan" ADD COLUMN "product_id" integer NOT NULL default 1;--> statement-breakpoint

update core_backend.plan set product_id = case when amount > 10 then 2 else 1 end;
alter table core_backend.plan alter column product_id drop default;

ALTER TABLE "core_backend"."transaction_detail" ADD COLUMN "product_id" integer NOT NULL default 1;--> statement-breakpoint

update core_backend.transaction_detail set product_id = case when amount > 10 then 2 else 1 end;
alter table core_backend.transaction_detail alter column product_id drop default;

ALTER TABLE "core_backend"."product" ADD CONSTRAINT "product_influencer_id_influencer_id_fk" FOREIGN KEY ("influencer_id") REFERENCES "core_backend"."influencer"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core_backend"."user_event_participation" ADD CONSTRAINT "user_event_participation_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core_backend"."user_event_participation" ADD CONSTRAINT "user_event_participation_event_id_event_id_fk" FOREIGN KEY ("event_id") REFERENCES "core_backend"."event"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core_backend"."event" ADD CONSTRAINT "event_participation_product_id_product_id_fk" FOREIGN KEY ("participation_product_id") REFERENCES "core_backend"."product"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core_backend"."plan" ADD CONSTRAINT "plan_product_id_product_id_fk" FOREIGN KEY ("product_id") REFERENCES "core_backend"."product"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core_backend"."transaction_detail" ADD CONSTRAINT "transaction_detail_product_id_product_id_fk" FOREIGN KEY ("product_id") REFERENCES "core_backend"."product"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core_backend"."plan" DROP COLUMN "code";--> statement-breakpoint
ALTER TABLE "core_backend"."plan" DROP COLUMN "amount";--> statement-breakpoint
ALTER TABLE "core_backend"."plan" DROP COLUMN "currency";

update core_backend.plan p
set product_id = p.id;
