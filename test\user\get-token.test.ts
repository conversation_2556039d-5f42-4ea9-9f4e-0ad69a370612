import { user } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { userService } from "../../src/user";
import { TestData, userAuthToken } from "../common";
import { verifyClientJWT } from "@mainframe-peru/common-core";
import { sc } from "../../src/services";

describe("get user token tests", () => {
  test("get user token", async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);

    const response = await request(app)
      .get("/live/user/token")
      .set("Cookie", `session=${await userAuthToken}`);

    const responseBody: user.GetTokenResponse = response.body;
    expect(response.statusCode).toEqual(200);
    expect(responseBody.token).toEqual(expect.any(String));

    const auth = await verifyClientJWT(
      responseBody.token,
      sc.vars.keys.user.public,
    );
    expect(auth.email).toEqual(TestData.user.email);
  });
});
