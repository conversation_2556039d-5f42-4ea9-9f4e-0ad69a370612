import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { getUserEventParticipationEndpoint } from "./get-user-event-participation";
import { addUserEventParticipationEndpoint } from "./add-user-event-participation";

const router = new Router();
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.get(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      event: ["GET_EVENT"],
    },
    admin: {
      event: ["GET_EVENT"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  getUserEventParticipationEndpoint,
);

router.post(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      event: ["CREATE_EVENT"],
    },
    admin: {
      event: ["CREATE_EVENT"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  addUserEventParticipationEndpoint,
);

export const userEventParticipationRouter = router;
