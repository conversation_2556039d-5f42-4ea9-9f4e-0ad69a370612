import { Middleware } from "@koa/router";
import {
  AppError,
  AuthAdmin,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import { businessPromotionCodeService } from "../service";
import { businessPromotionService } from "../../business-promotion/service";

export const createBusinessPromotionCodeEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  bpc.CreateBusinessPromotionCodeResponse
> = async ({ request, response, state }) => {
  const body = bpc.CreateBusinessPromotionCodeRequestSchema.parse(request.body);

  const promotion = await businessPromotionService.get("id", body.promotionId);
  if (!promotion || promotion.influencerId !== state.auth.influencerId) {
    throw new AppError({
      code: "BusinessPromotionNotFound",
      message: "Could not find business promotion",
      statusCode: "NOT_FOUND",
      logLevel: "INFO",
    });
  }

  const entity = await businessPromotionCodeService.create({
    promotionId: body.promotionId,
    code: body.code,
    userId: body.userId,
  });

  response.body = {
    code: entity.code,
    createdAt: entity.createdAt,
  };
};
