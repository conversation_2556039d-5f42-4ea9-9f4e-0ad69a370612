import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { createInvoiceEndpoint } from "./create-invoice";
import { sendInvoiceEndpoint } from "./send-invoice";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.post(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      invoice: ["PUT_INVOICE"],
    },
  }),
  createInvoiceEndpoint,
);

router.put(
  "/send",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      invoice: ["SEND_INVOICE"],
    },
  }),
  sendInvoiceEndpoint,
);

export const invoiceRouter = router;
