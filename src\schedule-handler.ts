import { Upload } from "@aws-sdk/lib-storage";
import { logger } from "@mainframe-peru/common-core";
import { format } from "fast-csv";
import stream from "stream";
import { eventService } from "./event";
import { reportService } from "./report/service";
import { sc } from "./services";

export type EventScheduleInput = {
  type: "event-list-generation";
  eventId: number;
  influencerId: string;
};

export const handler = async (scheduledEvent: EventScheduleInput) => {
  logger.info("Received scheduled event", { scheduledEvent });

  const participants = await reportService.listEventParticipants({
    eventId: scheduledEvent.eventId,
    influencerId: scheduledEvent.influencerId,
  });

  const mappedParticipants =
    await reportService.eventParticipantToCsvRecords(participants);

  const passThroughStream = new stream.PassThrough();
  const upload = new Upload({
    client: sc.s3,
    params: {
      Bucket: sc.vars.modulesStorageBucket,
      Key: `events/participants-list/event-${scheduledEvent.eventId}.csv`,
      ContentType: "text/csv",
      ACL: "public-read",
      Body: passThroughStream,
      Metadata: {
        eventId: scheduledEvent.eventId.toString(),
      },
    },
  });

  const csvStream = format({ headers: true });
  csvStream.pipe(passThroughStream);
  mappedParticipants.forEach((row) => csvStream.write(row));
  csvStream.end();

  const { Bucket, Key } = await upload.done();

  await eventService.update(scheduledEvent.eventId, {
    eventListUrl: `https://${Bucket}.s3.us-east-1.amazonaws.com/${Key}`,
  });
};
