import { common } from "@mainframe-peru/types";
import { quest } from "../common";
import { ServiceBase } from "../service-base";
import { AttributeEntity, attributeRepository } from "./repository";
import { transformNullToUndefined } from "../helpers";

export type ValidateValuesInput = {
  attributes: AttributeEntity[];
  values: common.AttributeValues;
};

class AttributeService extends ServiceBase<
  typeof attributeRepository,
  AttributeEntity
> {
  constructor() {
    super(attributeRepository);
  }

  /**
   * Validates all values pass rules defined by the attribute
   * @param input Defined attributes and a list of values
   * @returns Set of valid attribute ids
   * @throws AppError when validation fails
   */
  parseValues(input: ValidateValuesInput): common.AttributeValues {
    return quest.parseValues({
      attributes: transformNullToUndefined(input.attributes),
      values: input.values,
    });
  }
}

export const attributeService = new AttributeService();
