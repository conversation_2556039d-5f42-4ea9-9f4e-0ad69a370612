import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { redeemCodeEndpoint } from "./redeem-code";
import { createCodesEndpoint } from "./create-codes";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

// Endpoint for users to redeem codes
router.post(
  "/redeem",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    user: {
      general: ["REGULAR"],
    },
  }),
  redeemCodeEndpoint,
);

// Endpoint for admins to create codes in batches
router.post(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      code: ["PUT_CODE"],
    },
  }),
  createCodesEndpoint,
);

export const codeRouter = router;
