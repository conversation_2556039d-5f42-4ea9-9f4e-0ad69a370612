import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { influencerService } from "../../src/influencer";
import { TestData, userAuthToken } from "../common";
import { businessPromotionService } from "../../src/business-promotion";
import { businessPromotionCodeService } from "../../src/business-promotion-code";
import { userService } from "../../src/user";
import { recurrenceService } from "../../src/recurrence";
import { productService } from "../../src/product/service";
import { planService } from "../../src/plan";

describe("get business promotion code tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await businessService.create(TestData.business);
    await businessPromotionService.create(TestData.businessPromotion);
    await productService.create(TestData.product);
    await planService.create(TestData.plan);
    await recurrenceService.create(TestData.recurrence);
  });

  test("get a business promotion code (personal)", async () => {
    await businessPromotionCodeService.create(TestData.businessPromotionCode);

    const query: bpc.GetBusinessPromotionCodeRequest = {
      promotionId: TestData.businessPromotion.id,
    };

    const response = await request(app)
      .get("/live/business-promotion-code")
      .query(query)
      .set("Cookie", `session=${await userAuthToken}`);

    const responseBody: bpc.GetBusinessPromotionCodeResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody).toEqual({
      code: TestData.businessPromotionCode.code,
    });
  });

  test("get a business promotion code (global)", async () => {
    const bp = await businessPromotionService.create({
      ...TestData.businessPromotion,
      id: undefined,
      type: "GLOBAL",
    });
    await businessPromotionCodeService.create({
      ...TestData.businessPromotionCode,
      id: undefined,
      promotionId: bp.id,
      userId: undefined,
    });

    const query: bpc.GetBusinessPromotionCodeRequest = {
      promotionId: bp.id,
    };

    const response = await request(app)
      .get("/live/business-promotion-code")
      .query(query)
      .set("Cookie", `session=${await userAuthToken}`);

    const responseBody: bpc.GetBusinessPromotionCodeResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody).toEqual({
      code: TestData.businessPromotionCode.code,
    });
  });

  test("user does not have a business promotion code", async () => {
    const query: bpc.GetBusinessPromotionCodeRequest = {
      promotionId: TestData.businessPromotion.id,
    };

    const response = await request(app)
      .get("/live/business-promotion-code")
      .query(query)
      .set("Cookie", `session=${await userAuthToken}`);

    const responseBody: bpc.GetBusinessPromotionCodeResponse = response.body;
    expect(response.status).toBe(404);
    expect(responseBody).toEqual(
      expect.objectContaining({
        code: "CodeNotFound",
      }),
    );
  });
});
