import { Middleware } from "@koa/router";
import { businessPromotion as bp } from "@mainframe-peru/types";
import { transformNullToUndefined } from "../../helpers";
import { businessPromotionService } from "../service";

export const listBusinessPromotionEndpoint: Middleware<
  unknown,
  unknown,
  bp.ListBusinessPromotionResponse
> = async ({ request, response }) => {
  const params = bp.ListBusinessPromotionRequestSchema.parse(request.query);

  const promotions = await businessPromotionService.listPromotions(params);

  response.set("Cache-Control", "s-maxage=1800");
  response.body = promotions.map((i) =>
    transformNullToUndefined(
      businessPromotionService.getPublic({
        business: i.business,
        promotion: i.business_promotion,
      }),
    ),
  );
};
