import { admin } from "@mainframe-peru/types";
import request from "supertest";
import { adminService } from "../../src/admin";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer";
import { TestData, officerAuthToken } from "../common";
import { mockClient } from "aws-sdk-client-mock";
import "aws-sdk-client-mock-jest";
import { PublishCommand, SNSClient } from "@aws-sdk/client-sns";

describe("create admin tests", () => {
  test("Create a new admin", async () => {
    const snsMock = mockClient(SNSClient);
    await influencerService.create(TestData.influencer);

    const testEntity: admin.CreateAdminRequest = {
      influencerId: TestData.influencer.id,
      email: "<EMAIL>",
      password: "1234567890",
      firstName: "<PERSON>",
      lastName: "Tester",
      policies: {},
    };

    // Creation the influencer
    const response = await request(app)
      .post("/live/admin")
      .send(testEntity)
      .set("Cookie", `session=${await officerAuthToken}`);
    const resBody: admin.CreateAdminResponse = response.body;

    expect(response.statusCode).toEqual(200);

    const admin = await adminService.get("id", resBody.id);

    expect(admin).toEqual(
      expect.objectContaining({
        id: resBody.id,
        email: testEntity.email,
        firstName: testEntity.firstName,
        lastName: testEntity.lastName,
      }),
    );
    expect(snsMock).toHaveReceivedCommandTimes(PublishCommand, 0);
  });

  test("Create a new admin with generated password", async () => {
    const snsMock = mockClient(SNSClient);
    await influencerService.create(TestData.influencer);

    const testEntity: admin.CreateAdminRequest = {
      influencerId: TestData.influencer.id,
      email: "<EMAIL>",
      firstName: "John",
      lastName: "Tester",
      policies: {},
    };

    // Creation the influencer
    const response = await request(app)
      .post("/live/admin")
      .send(testEntity)
      .set("Cookie", `session=${await officerAuthToken}`);

    expect(response.statusCode).toEqual(200);
    expect(snsMock).toHaveReceivedCommandTimes(PublishCommand, 1);
  });
});
