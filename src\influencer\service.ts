import { common } from "@mainframe-peru/types";
import { ServiceBaseWithAttributes } from "../service-attribute-base";
import { InfluencerEntity, influencerRepository } from "./repository";

class InfluencerService extends ServiceBaseWithAttributes<
  typeof influencerRepository,
  InfluencerEntity
> {
  constructor() {
    super(influencerRepository);
  }

  get attributeEntity(): common.AttributeEntity {
    return "INFLUENCER";
  }
}

export const influencerService = new InfluencerService();
