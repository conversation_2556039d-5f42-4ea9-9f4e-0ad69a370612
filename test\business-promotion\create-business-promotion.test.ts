import { businessPromotion as bp } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { businessPromotionService } from "../../src/business-promotion";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken } from "../common";

describe("create business promotion tests", () => {
  test("create a business promotion", async () => {
    await influencerService.create(TestData.influencer);
    await businessService.create(TestData.business);

    const body: bp.CreateBusinessPromotionRequest = {
      businessId: TestData.business.id,
      content: crypto.randomUUID(),
      expirationDate: new Date(),
      name: "name",
      type: "PERSONAL",
      value: "value",
      description: "some description",
    };
    const response = await request(app)
      .post("/live/business-promotion")
      .send(body)
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody: bp.CreateBusinessPromotionResponse = response.body;

    const entity = await businessPromotionService.get("id", responseBody.id);
    expect(response.status).toBe(200);
    expect(entity).toEqual({
      id: responseBody.id,
      businessId: TestData.business.id,
      content: body.content,
      createdAt: expect.any(Date),
      description: body.description,
      expirationDate: expect.any(Date),
      influencerId: TestData.influencer.id,
      name: body.name,
      status: "ACTIVE",
      type: body.type,
      updatedAt: expect.any(Date),
      value: body.value,
    });
  });
});
