import { ext } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import fetch, { Response } from "node-fetch";
import { InvoiceResponse } from "../src/invoice/service";
import { ReCaptchaResponseBody } from "../src/ext";

export const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

export function mockCulqiCreateCustomer(): ext.culqi.CreateCustomerResponse {
  const json = jest.fn();
  const culqiCreateCustomerResponse: ext.culqi.CreateCustomerResponse = {
    object: "customer",
    id: "cus_test_Lz6Yfsm7QqCPIECW",
    creation_date: 1487041774773,
    email: "<EMAIL>",
    antifraud_details: {
      country_code: "US",
      first_name: "<PERSON>",
      last_name: "Hend<PERSON>",
      address_city: "Palo Alto",
      address: "San Francisco Bay Area",
      phone: "6505434800",
      object: "client",
    },
    metadata: {},
  };
  json.mockResolvedValue(culqiCreateCustomerResponse);
  mockFetch.mockResolvedValueOnce({ ok: true, json } as unknown as Response);

  return culqiCreateCustomerResponse;
}

export function mockCulqiCreateCard(): ext.culqi.CreateCardResponse {
  const json = jest.fn();
  const culqiCreateCardResponse: ext.culqi.CreateCardResponse = {
    object: "card",
    id: "crd_test_RzjTyGUwZioJLpZt",
    active: true,
    creation_date: *************,
    customer_id: "cus_test_Lz6Yfsm7QqCPIECW",
    source: {
      object: "token",
      id: "tkn_test_vEcZSCOVz5PGDPdQ",
      type: "card",
      creation_date: *************,
      email: "<EMAIL>",
      card_number: "411111******1111",
      last_four: "1111",
      active: true,
      iin: {
        object: "iin",
        bin: "411111",
        card_brand: "Visa",
        card_type: "credit",
        card_category: "Clásica",
        issuer: {
          name: "JPMORGAN CHASE BANK, N.A.",
          country: "United States",
          country_code: Enums.Country.Enum.PE,
          website: undefined,
          phone_number: undefined,
        },
        installments_allowed: [2, 4, 6, 8, 10, 12],
      },
      client: {
        ip: "***************",
        ip_country: "Perú",
        ip_country_code: Enums.Country.Enum.PE,
        browser: "Desconocido",
        device_fingerprint: undefined,
        device_type: "Escritorio",
      },
      metadata: {},
    },
    metadata: {
      ZDA: true,
      marca_tarjeta: "VISA",
    },
  };
  json.mockResolvedValue(culqiCreateCardResponse);
  mockFetch.mockResolvedValueOnce({ ok: true, json } as unknown as Response);
  return culqiCreateCardResponse;
}

export function mockCulqiCharge(): ext.culqi.CreateChargeResponse {
  const json = jest.fn();
  const culqiCreateChargeResponse: ext.culqi.CreateChargeResponse = {
    object: "charge",
    id: "chr_test_HuVDSH10IoLKtYMa",
    creation_date: *************,
    amount: 10000,
    amount_refunded: 0,
    current_amount: 10000,
    installments: 0,
    installments_amount: 0,
    currency_code: Enums.Currency.Values.PEN,
    email: "<EMAIL>",
    description: "Demo Phone",
    source: {
      object: "token",
      id: "tkn_test_RvtfMbLatXRJrJQo",
      type: "card",
      creation_date: *************,
      email: "<EMAIL>",
      card_number: "************1096",
      last_four: "1096",
      active: false,
      iin: {
        object: "iin",
        bin: "********",
        card_brand: "Visa",
        card_type: "Crédito",
        card_category: undefined,
        issuer: {
          name: "JPMORGAN CHASE BANK, N.A.",
          country: "UNITED STATES",
          country_code: "US",
          website: "https://www.chase.com",
          phone_number: "+ (1) ************",
        },
        installments_allowed: [],
      },
      client: {
        ip: "*************",
        ip_country: "Peru",
        ip_country_code: Enums.Country.Enum.PE,
        browser: "Chrome-*********",
        device_fingerprint: undefined,
        device_type: "Escritorio",
      },
      metadata: {},
    },
    outcome: {
      type: "venta_exitosa",
      code: "AUT0000",
      merchant_message: "La operación de venta ha sido autorizada exitosamente",
      user_message: "Su compra ha sido exitosa.",
    },
    fraud_score: 25,
    antifraud_details: {
      first_name: "culqi",
      last_name: "core",
      address: undefined,
      address_city: "Lima",
      country_code: Enums.Country.Enum.PE,
      phone: "*********",
      object: "client",
    },
    dispute: false,
    capture: true,
    capture_date: 1669306674000,
    reference_code: "682750616398",
    authorization_code: "ebnNU9",
    duplicated: false,
    metadata: {
      documentNumber: "77723083",
    },
    total_fee: 0,
    fee_details: {
      fixed_fee: {},
      variable_fee: {
        currency_code: Enums.Currency.Values.PEN,
        commision: 0.0399,
        total: 0,
      },
    },
    total_fee_taxes: 0,
    transfer_amount: 0,
    paid: false,
    statement_descriptor: "CULQI*",
    transfer_id: undefined,
  };
  json.mockResolvedValue(culqiCreateChargeResponse);
  mockFetch.mockResolvedValueOnce({
    ok: true,
    json,
    status: 201,
  } as unknown as Response);

  return culqiCreateChargeResponse;
}

export function mockCulqiChargeError(): ext.culqi.ErrorResponse {
  // Mock culqi response
  const culqiErrorResponse: ext.culqi.ErrorResponse = {
    merchant_message: "test",
    object: "error",
    type: "test",
    charge_id: crypto.randomUUID(),
    code: "123",
    decline_code: "error",
    user_message: "Ocurrió un error",
  };
  mockFetch.mockResolvedValueOnce({
    ok: false,
    json: jest.fn().mockReturnValue(culqiErrorResponse),
  } as unknown as Response);
  return culqiErrorResponse;
}

export function mockCulqiCharge3DSResponse(): ext.culqi.threeDSResponse {
  // Mock culqi response
  const culqiErrorResponse: ext.culqi.threeDSResponse = {
    user_message: "El usuario necesita autenticarse",
    action_code: "REVIEW",
  };
  mockFetch.mockResolvedValueOnce({
    ok: true,
    status: 200,
    json: jest.fn().mockReturnValue(culqiErrorResponse),
  } as unknown as Response);
  return culqiErrorResponse;
}

export function mockNubefactResponse(): InvoiceResponse {
  const invoiceResponse: InvoiceResponse = {
    tipo_de_comprobante: 1,
    serie: "",
    numero: Math.floor(Math.random() * 10 ** 6),
    enlace: crypto.randomUUID(),
    enlace_del_pdf: crypto.randomUUID(),
    enlace_del_xml: "",
    enlace_del_cdr: "",
    aceptada_por_sunat: true,
    sunat_description: "",
    sunat_note: "",
    sunat_responsecode: "",
    sunat_soap_error: "",
    cadena_para_codigo_qr: "",
    codigo_hash: "",
  };
  const json = jest.fn();
  json.mockResolvedValue(invoiceResponse);
  mockFetch.mockResolvedValueOnce({ ok: true, json } as unknown as Response);

  return invoiceResponse;
}

export function mockReCaptchaResponse(): ReCaptchaResponseBody {
  const invoiceResponse: ReCaptchaResponseBody = {
    success: true,
    challenge_ts: new Date().toISOString(),
    hostname: "localhost",
  };
  const json = jest.fn();
  json.mockResolvedValue(invoiceResponse);
  mockFetch.mockResolvedValueOnce({ ok: true, json } as unknown as Response);

  return invoiceResponse;
}
