import request from "supertest";
import { influencerService } from "../../../src/influencer";
import { userService } from "../../../src/user";
import { productService } from "../../../src/product/service";
import { codeService } from "../../../src/code";
import { app } from "../../../src/api-handler";
import { TestData, userAuthToken } from "../../common";
import { common, code as ct } from "@mainframe-peru/types";

describe("redeem code tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
  });

  test("should redeem code successfully", async () => {
    // Create a product first
    const product = await productService.create(TestData.product);

    // Create a code
    const codes = await codeService.createBatch(
      ["TEST_CODE_123"],
      product.id,
      TestData.influencer.id,
    );

    const requestBody: ct.http.RedeemCodeRequest = {
      code: "TEST_CODE_123",
    };

    const response = await request(app)
      .post("/live/code/redeem")
      .set("Cookie", `session=${await userAuthToken}`)
      .send(requestBody);

    expect(response.status).toBe(200);
    const responseBody: ct.http.RedeemCodeResponse = response.body;
    expect(responseBody).toEqual({
      success: true,
      transactionId: expect.any(Number),
      productId: product.id,
      redeemedDate: expect.any(String),
    });
  });

  test("should fail when code does not exist", async () => {
    const requestBody: ct.http.RedeemCodeRequest = {
      code: "NONEXISTENT_CODE",
    };

    const response = await request(app)
      .post("/live/code/redeem")
      .set("Cookie", `session=${await userAuthToken}`)
      .send(requestBody);

    expect(response.status).toBe(404);
    const errorBody = response.body as common.HttpApiError;
    expect(errorBody.code).toBe("CodeNotFound");
  });

  test("should fail when code is already claimed", async () => {
    // Create a product first
    const product = await productService.create(TestData.product);

    // Create a code
    await codeService.createBatch(
      ["ALREADY_CLAIMED_CODE"],
      product.id,
      TestData.influencer.id,
    );

    // Redeem the code first time
    await codeService.redeemCode(
      "ALREADY_CLAIMED_CODE",
      TestData.influencer.id,
      TestData.user.id,
    );

    const requestBody: ct.http.RedeemCodeRequest = {
      code: "ALREADY_CLAIMED_CODE",
    };

    const response = await request(app)
      .post("/live/code/redeem")
      .set("Cookie", `session=${await userAuthToken}`)
      .send(requestBody);

    expect(response.status).toBe(400);
    const errorBody = response.body as common.HttpApiError;
    expect(errorBody.code).toBe("CodeAlreadyClaimed");
  });

  test("should fail without authentication", async () => {
    const requestBody: ct.http.RedeemCodeRequest = {
      code: "TEST_CODE",
    };

    const response = await request(app)
      .post("/live/code/redeem")
      .send(requestBody);

    expect(response.status).toBe(401);
  });

  test("should fail with invalid request body", async () => {
    const invalidRequestBody = {
      // Missing code field
    };

    const response = await request(app)
      .post("/live/code/redeem")
      .set("Cookie", `session=${await userAuthToken}`)
      .send(invalidRequestBody);

    expect(response.status).toBe(400);
  });
});
