import request from "supertest";
import { app } from "../../src/api-handler";
import { eventService } from "../../src/event";
import { influencerService } from "../../src/influencer/service";
import { userEventParticipationService } from "../../src/user-event-participation/service";
import { TestData, adminAuthToken } from "../common";
import { userService } from "../../src/user";
import { productService } from "../../src/product/service";
import { report } from "@mainframe-peru/types";
import { recurrenceService } from "../../src/recurrence";
import { add } from "date-fns";
import { planService } from "../../src/plan";

describe("List event participants", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await productService.create(TestData.product);
  });

  test("list participants in IN-PERSON event (json)", async () => {
    await eventService.create({
      ...TestData.event,
      type: "IN-PERSON",
    });

    for (let i = 0; i < 50; i++) {
      const u = await userService.create({
        ...TestData.user,
        id: undefined,
        email: `josue+${i}@mail.com`,
      });
      await userEventParticipationService.create({
        eventId: TestData.event.id,
        quantity: 1,
        userId: u.id,
      });
    }

    const query: report.ListEventParticipantsRequest = {
      eventId: TestData.event.id,
    };
    const response = await request(app)
      .get("/live/report/event-participants")
      .query(query)
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody = response.body as report.ListEventParticipantsResponse;
    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(50);
  });

  test("list participants in IN-PERSON event (csv)", async () => {
    await eventService.create({
      ...TestData.event,
      type: "IN-PERSON",
    });

    for (let i = 0; i < 50; i++) {
      const u = await userService.create({
        ...TestData.user,
        id: undefined,
        email: `josue+${i}@mail.com`,
      });
      await userEventParticipationService.create({
        eventId: TestData.event.id,
        quantity: 1,
        userId: u.id,
      });
    }

    const query: report.ListEventParticipantsRequest = {
      eventId: TestData.event.id,
    };
    const response = await request(app)
      .get("/live/report/event-participants")
      .query(query)
      .set("Cookie", `session=${await adminAuthToken}`)
      .set("Accept", "text/csv");

    expect(response.status).toBe(200);
    expect(response.text.split("\n").length).toEqual(51);
  });

  test("list participants in PRIZE event (json)", async () => {
    await eventService.create(TestData.event);
    await planService.create(TestData.plan);

    for (let i = 0; i < 50; i++) {
      const u = await userService.create({
        ...TestData.user,
        id: undefined,
        email: `josue+${i}@mail.com`,
      });
      await recurrenceService.create({
        planId: TestData.plan.id,
        renewalDate: add(new Date(), { months: 3 }),
        status: "ACTIVE",
        type: "CARD",
        userId: u.id,
      });
    }

    const query: report.ListEventParticipantsRequest = {
      eventId: TestData.event.id,
    };
    const response = await request(app)
      .get("/live/report/event-participants")
      .query(query)
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody = response.body as report.ListEventParticipantsResponse;
    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(50);
  });
});
