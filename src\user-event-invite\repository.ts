import { and, eq, InferSelectModel } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { userEventInviteTable } from "../schema";
import { sc } from "../services";

export type UserEventInviteEntity = InferSelectModel<
  typeof userEventInviteTable
>;

class UserEventInviteRepository extends RepositoryBase<
  typeof userEventInviteTable,
  string,
  UserEventInviteEntity
> {
  constructor() {
    super(userEventInviteTable);
  }

  async findByEmailAndEventId(
    email: string,
    eventId: number,
  ): Promise<UserEventInviteEntity | undefined> {
    const db = await sc.getDB();
    const [result] = await db
      .select()
      .from(this.table)
      .where(and(eq(this.table.email, email), eq(this.table.eventId, eventId)))
      .limit(1);
    return result;
  }

  async acceptInvite(
    id: string,
    participationFormValues?: Record<string, string | string[]>,
  ): Promise<void> {
    const db = await sc.getDB();
    await db
      .update(this.table)
      .set({
        status: "ACCEPTED",
        participationFormValues,
        acceptedAt: new Date(),
      })
      .where(eq(this.table.id, id));
  }

  async countInvitesByEventId(eventId: number): Promise<number> {
    const db = await sc.getDB();
    const [result] = await db
      .select({ count: db.$count(this.table) })
      .from(this.table)
      .where(eq(this.table.eventId, eventId));
    return result?.count || 0;
  }
}

export const userEventInviteRepository = new UserEventInviteRepository();
