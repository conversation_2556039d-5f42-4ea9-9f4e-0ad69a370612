import { businessPromotionCode as bpc, common } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { businessPromotionService } from "../../src/business-promotion";
import { businessPromotionCodeService } from "../../src/business-promotion-code";
import { influencerService } from "../../src/influencer";
import { TestData, userAuthToken } from "../common";
import { userService } from "../../src/user";

describe("use business promotion code tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await businessService.create(TestData.business);
    await businessPromotionService.create(TestData.businessPromotion);
  });

  test("use a new business promotion code", async () => {
    const code = await businessPromotionCodeService.create({
      code: "ABC123",
      promotionId: TestData.businessPromotion.id,
    });

    const body: bpc.UseBusinessPromotionCodeRequest = {
      promotionId: TestData.businessPromotion.id,
    };

    const response = await request(app)
      .post("/live/business-promotion-code/use")
      .send(body)
      .set("Cookie", `session=${await userAuthToken}`);

    const responseBody: bpc.UseBusinessPromotionCodeResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.code).toEqual(code.code);

    const saved = await businessPromotionCodeService.get("id", code.id);
    expect(saved?.userId).toEqual(TestData.user.id);
  });

  test("use a used business promotion code", async () => {
    const code = await businessPromotionCodeService.create({
      code: "ABC123",
      promotionId: TestData.businessPromotion.id,
      userId: TestData.user.id,
    });

    const body: bpc.UseBusinessPromotionCodeRequest = {
      promotionId: TestData.businessPromotion.id,
    };

    const response = await request(app)
      .post("/live/business-promotion-code/use")
      .send(body)
      .set("Cookie", `session=${await userAuthToken}`);

    const responseBody: bpc.UseBusinessPromotionCodeResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.code).toEqual(code.code);
  });

  test("use a global business promotion code", async () => {
    await businessPromotionService.update(TestData.businessPromotion.id, {
      type: "GLOBAL",
    });
    const code = await businessPromotionCodeService.create({
      code: "ABC123",
      promotionId: TestData.businessPromotion.id,
    });

    const body: bpc.UseBusinessPromotionCodeRequest = {
      promotionId: TestData.businessPromotion.id,
    };

    const response = await request(app)
      .post("/live/business-promotion-code/use")
      .send(body)
      .set("Cookie", `session=${await userAuthToken}`);

    const responseBody: bpc.UseBusinessPromotionCodeResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.code).toEqual(code.code);

    const saved = await businessPromotionCodeService.get("id", code.id);
    expect(saved?.userId).toBeUndefined();
  });

  test("fail is all business promotion codes are used", async () => {
    await userService.create({
      ...TestData.user,
      id: 123,
    });
    await businessPromotionCodeService.create({
      code: "ABC123",
      promotionId: TestData.businessPromotion.id,
      userId: 123,
    });

    const body: bpc.UseBusinessPromotionCodeRequest = {
      promotionId: TestData.businessPromotion.id,
    };

    const response = await request(app)
      .post("/live/business-promotion-code/use")
      .send(body)
      .set("Cookie", `session=${await userAuthToken}`);

    const responseBody: common.HttpApiError = response.body;
    expect(response.status).toBe(404);
    expect(responseBody.code).toEqual("NoCodeAvailable");
  });
});
