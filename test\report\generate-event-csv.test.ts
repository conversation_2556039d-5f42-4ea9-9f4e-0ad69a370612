import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { report } from "@mainframe-peru/types";
import { mockClient } from "aws-sdk-client-mock";
import request from "supertest";
import "aws-sdk-client-mock-jest";
import { eventService } from "../../src/event";
import { influencerService } from "../../src/influencer";
import { productService } from "../../src/product/service";
import { sc } from "../../src/services";
import { userService } from "../../src/user";
import { userEventParticipationService } from "../../src/user-event-participation/service";
import { adminAuthToken, TestData } from "../common";
import { app } from "../../src/api-handler";

describe("generate event csv", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await productService.create(TestData.product);
  });

  test("handle event list generation", async () => {
    const s3Mock = mockClient(S3Client);

    s3Mock.on(PutObjectCommand).resolves({});

    await eventService.create({
      ...TestData.event,
      type: "IN-PERSON",
    });

    for (let i = 0; i < 3; i++) {
      const u = await userService.create({
        ...TestData.user,
        id: undefined,
        email: `josue+${i}@mail.com`,
      });
      await userEventParticipationService.create({
        eventId: TestData.event.id,
        quantity: 1,
        userId: u.id,
      });
    }

    const requestBody: report.EventListGenerationRequest = {
      influencerId: TestData.influencer.id,
      id: TestData.event.id,
    };

    const response = await request(app)
      .post("/live/report/event-participants/csv")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(TestData.event.id);
    expect(response.body.url).toEqual(
      `https://${sc.vars.modulesStorageBucket}.s3.us-east-1.amazonaws.com/events/participants-list/event-${TestData.event.id}.csv`,
    );

    expect(s3Mock).toHaveReceivedCommandTimes(PutObjectCommand, 1);

    const event = await eventService.get("id", TestData.event.id);
    expect(event?.eventListUrl).toEqual(
      `https://${sc.vars.modulesStorageBucket}.s3.us-east-1.amazonaws.com/events/participants-list/event-${TestData.event.id}.csv`,
    );
  });
});
