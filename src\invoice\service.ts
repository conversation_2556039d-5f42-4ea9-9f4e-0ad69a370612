import { AppError, logger } from "@mainframe-peru/common-core";
import { common } from "@mainframe-peru/types";
import { InvoiceDestination } from "@mainframe-peru/types/build/common";
import fetch from "node-fetch";
import { emailNotification } from "../common";
import { formatDateLocale } from "../helpers";
import { ServiceBase } from "../service-base";
import { sc } from "../services";
import { TransactionEntity } from "../transaction/repository";
import { userService } from "../user";
import { InvoiceEntity, invoiceRepository } from "./repository";
import { VoucherFactory } from "./utils/voucher-factory";

export type InvoiceResponse = {
  tipo_de_comprobante: number;
  serie: string;
  numero: number;
  enlace: string;
  enlace_del_pdf: string;
  enlace_del_xml: string;
  enlace_del_cdr: string;
  aceptada_por_sunat: boolean;
  sunat_description: string;
  sunat_note: string | null;
  sunat_responsecode: string;
  sunat_soap_error: string;
  cadena_para_codigo_qr: string;
  codigo_hash: string;
};

class InvoiceService extends ServiceBase<
  typeof invoiceRepository,
  InvoiceEntity
> {
  constructor() {
    super(invoiceRepository);
  }

  async generateAndCreateInvoice(
    transaction: TransactionEntity,
    invoiceDestination: InvoiceDestination,
  ): Promise<InvoiceEntity> {
    const apiUrl = sc.vars.nubefactUrl;
    const apiKey = sc.vars.nubefactKey;

    if (!apiUrl || !apiKey) {
      throw new AppError({
        code: "MissingNubefactEnv",
        message: "Missing NUBEFACT_URL or NUBEFACT_KEY in .env",
      });
    }

    if (transaction.state !== common.Enums.TransactionState.Enum.SUCCESS) {
      throw new AppError({
        code: "InvalidStateTransaction",
        message: "La transacción se encuentra en un estado incorrecto",
      });
    }

    const user = await userService.get("id", transaction.userId);

    if (!user) {
      throw new AppError({
        code: "UserNotFound",
        message: "No se pudo encontrar el usuario",
      });
    }

    const voucher = await VoucherFactory.createVoucher(
      transaction,
      user,
      invoiceDestination,
    );
    logger.debug("Sending invoice to nubefact", { voucher });

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: apiKey,
      },
      body: JSON.stringify(voucher),
    });

    if (!response.ok) {
      throw new AppError({
        code: "NubefactApiError",
        message: "No se pudo generar la boleta",
        allowRetry: true,
        data: {
          response,
          body: await response.text(),
        },
      });
    }

    const data = (await response.json()) as InvoiceResponse;

    const invoice = await this.create({
      transactionId: transaction.id,
      userId: user.id,
      invoiceOrigin: 2,
      voucherType: voucher.tipo_de_comprobante,
      serie: voucher.serie,
      number: data.numero,
      link: data.enlace_del_pdf,
      acceptedBySunat: data.aceptada_por_sunat ? 1 : 0,
      sunatDescription: data.sunat_description || "",
      sunatNote: data.sunat_note || "",
      sunatResponseCode: data.sunat_responsecode || "",
      sunatSoapError: data.sunat_soap_error || "",
      stringQrCode: data.cadena_para_codigo_qr || "",
      hashCode: data.codigo_hash || "",
      payload: {
        response: data,
        request: voucher,
        status: "SUCCESS",
      },
    });

    await emailNotification.sendInvoiceEmail(user.email, {
      CreatedAt: formatDateLocale(new Date()),
      Name: `${user.firstName} ${user.lastName}`,
      ReceiptId: `${voucher.serie} - ${data.numero.toString().padStart(6, "0")}`,
      Total: `${transaction.amount} ${transaction.currency}`,
      Url: data.enlace_del_pdf,
    });

    return invoice;
  }
}

export const invoiceService = new InvoiceService();
