import { business as bu } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { TestData, adminAuthToken } from "../common";
import { influencerService } from "../../src/influencer";
import { mockClient } from "aws-sdk-client-mock";
import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { sc } from "../../src/services";

describe("create business tests", () => {
  test("create a business", async () => {
    const s3Mock = mockClient(S3Client);
    s3Mock.on(PutObjectCommand).resolves({});

    await influencerService.create(TestData.influencer);

    const base64Image =
      "/9j/4AAQSkZJRgABAQEAAAAAAAD/4QCcRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAEAAIdpAAQAAAABAAAAJgAAAAAAAqACAAQAAAABAAAAJqADAAQAAAABAAAAJgAAAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFgABAQEAAAAAAAAAAAAAAAAAAAUG/8QAFxEBAQEBAAAAAAAAAAAAAAAAAAECA//EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEAMQAAABy0Cj/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAgBAQABPxA//8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAgBAgEBPxA//8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAgBAwEBPxA//9k=";
    const requestBody = {
      name: "test",
      description: "description",
      ruc: "123456789",
    };
    const response = await request(app)
      .post("/live/business")
      .field("name", requestBody.name)
      .field("description", requestBody.description)
      .field("ruc", requestBody.ruc)
      .attach("file", Buffer.from(base64Image, "base64"), "test-image.jpg")
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody: bu.CreateBusinessResponse = response.body;

    const entity = await businessService.get("id", responseBody.id);

    expect(response.status).toBe(200);
    expect(entity).toEqual({
      id: response.body.id,
      createdAt: expect.any(Date),
      description: requestBody.description,
      imageUrl: expect.stringContaining(
        `https://${sc.vars.modulesStorageBucket}.s3.us-east-1.amazonaws.com/`,
      ),
      influencerId: TestData.influencer.id,
      name: requestBody.name,
      ruc: requestBody.ruc,
      updatedAt: expect.any(Date),
    });
    expect(s3Mock.commandCalls(PutObjectCommand).length).toBe(1);
  });
});
