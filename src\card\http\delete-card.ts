import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { card as ct } from "@mainframe-peru/types";
import { culqiApi } from "../../ext";
import { cardService } from "../service";
import { recurrenceService } from "../../recurrence";
import { userService } from "../../user";
import { influencerService } from "../../influencer";

export const deleteUserCard: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  null
> = async ({ request, response, state }) => {
  const userId = Number(state.auth.id);
  const body = ct.DeleteUserCardRequestSchema.parse(request.body);
  const card = await cardService.get("id", body.id);
  if (!card) {
    throw new AppError({
      code: "CardNotFound",
      message: "No se encontró la tarjeta",
      statusCode: "NOT_FOUND",
      logLevel: "WARN",
    });
  }

  if (card.default) {
    const lastRecurrence = await recurrenceService.getActiveRecurrence(userId);
    if (lastRecurrence) {
      throw new AppError({
        code: "CardIsDefault",
        message:
          "Given card is default and a recurrent payment is still active",
        statusCode: "NOT_FOUND",
        logLevel: "WARN",
      });
    }
  }

  const user = await userService.get("id", userId);
  if (!user) {
    throw new AppError({
      code: "UserNotFound",
      message: "Sesión inválida",
      logLevel: "ERROR",
      statusCode: "FORBIDDEN",
    });
  }
  const influencer = await influencerService.get("id", user.influencerId);

  const culqiResponse = await culqiApi.deleteCard(
    card.token,
    influencer?.providersConfiguration?.CULQI?.apiKey,
  );
  if (!culqiResponse.ok) {
    throw new AppError({
      code: "CulqiDeleteCardError",
      message: "No se pudo eliminar la tarjeta de Culqi",
    });
  }

  await cardService.delete(body.id);

  response.body = null;
};
