import { InferSelectModel, eq } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { productTable, transactionDetailTable } from "../schema";
import { sc } from "../services";

export type TransactionDetailEntity = InferSelectModel<
  typeof transactionDetailTable
>;

class TransactionDetailRepository extends RepositoryBase<
  typeof transactionDetailTable
> {
  constructor() {
    super(transactionDetailTable);
  }

  async listWithProducts(transactionId: number) {
    const db = await sc.getDB();
    return db
      .select()
      .from(this.table)
      .innerJoin(productTable, eq(this.table.productId, productTable.id))
      .where(eq(this.table.transactionId, transactionId));
  }
}

export const transactionDetailRepository = new TransactionDetailRepository();
