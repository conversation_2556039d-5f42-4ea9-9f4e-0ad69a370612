CREATE TYPE "core_backend"."business_promotion_status" AS ENUM('ACTIVE', 'INACTIVE');--> statement-breakpoint
CREATE TYPE "core_backend"."business_promotion_type" AS ENUM('PERSONAL', 'GLOBAL');--> statement-breakpoint
CREATE TABLE "core_backend"."business_promotion_code" (
	"id" serial PRIMARY KEY NOT NULL,
	"promotion_id" integer NOT NULL,
	"user_id" integer,
	"code" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "core_backend"."business_promotion" (
	"id" serial PRIMARY KEY NOT NULL,
	"influencer_id" text NOT NULL,
	"business_id" integer NOT NULL,
	"name" text NOT NULL,
	"type" "core_backend"."business_promotion_type" NOT NULL,
	"status" "core_backend"."business_promotion_status" NOT NULL,
	"description" text,
	"value" text NOT NULL,
	"content" text NOT NULL,
	"expirationDate" timestamp NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "core_backend"."business" (
	"id" serial PRIMARY KEY NOT NULL,
	"influencer_id" text NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"ruc" text,
	"image_url" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "core_backend"."business_promotion_code" ADD CONSTRAINT "business_promotion_code_promotion_id_business_promotion_id_fk" FOREIGN KEY ("promotion_id") REFERENCES "core_backend"."business_promotion"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core_backend"."business_promotion_code" ADD CONSTRAINT "business_promotion_code_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core_backend"."business_promotion" ADD CONSTRAINT "business_promotion_influencer_id_influencer_id_fk" FOREIGN KEY ("influencer_id") REFERENCES "core_backend"."influencer"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core_backend"."business_promotion" ADD CONSTRAINT "business_promotion_business_id_business_id_fk" FOREIGN KEY ("business_id") REFERENCES "core_backend"."business"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core_backend"."business" ADD CONSTRAINT "business_influencer_id_influencer_id_fk" FOREIGN KEY ("influencer_id") REFERENCES "core_backend"."influencer"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "promotion_code_idx" ON "core_backend"."business_promotion_code" USING btree ("promotion_id","code");