import { Middleware } from "@koa/router";
import { officer } from "@mainframe-peru/types";
import {
  AppError,
  officerPoliciesConstant,
  Policies,
} from "@mainframe-peru/common-core";
import { officerService } from "../service";

export const listOfficers: Middleware<
  unknown,
  unknown,
  officer.ListOfficersResponse
> = async ({ response }) => {
  const entities = await officerService.list();

  if (entities.length == 0) {
    throw new AppError({
      code: "NoItems",
      message: "No se encontraron oficiales.",
      statusCode: "NOT_FOUND",
    });
  }

  response.body = officer.ListOfficersResponseSchema.parse(
    entities.map((x) => ({
      ...x,
      policies: Policies.unmask(
        x.policies as Record<string, number>,
        officerPoliciesConstant,
      ),
      // policies: unmaskPolicies(x.policies).modules,
    })),
  );
};
