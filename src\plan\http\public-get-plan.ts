import { Middleware } from "@koa/router";
import { AppError } from "@mainframe-peru/common-core";
import { plan } from "@mainframe-peru/types";
import { planService } from "../service";
import { productService } from "../../product/service";

export const publicGetPlan: Middleware<
  unknown,
  unknown,
  plan.PublicPlanResponse
> = async ({ request, response }) => {
  const query = plan.PublicPlanRequestSchema.parse(request.query);

  const result = await planService.get("id", query.id);
  if (!result || result.influencerId != query.influencerId) {
    throw new AppError({
      code: "PlanNotFound",
      message: "No se pudo encontrar el plan",
      logLevel: "INFO",
      statusCode: "NOT_FOUND",
    });
  }

  const product = await productService.get("id", result.productId);
  if (!product) {
    throw new AppError({
      code: "ProductNotFound",
      message: "Product not found",
      logLevel: "CRITICAL",
      statusCode: "INTERNAL_SERVER_ERROR",
    });
  }

  response.body = plan.PublicPlanResponseSchema.parse({
    ...result,
    amount: product.amount,
    currency: product.currency,
  });
};
