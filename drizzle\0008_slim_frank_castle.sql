ALTER TABLE "core_backend"."user" ADD COLUMN "attributes" jsonb;

update core_backend.user u
set attributes = tr.attr
from (select av.user_id                                                         as id,
             json_build_object(av.attribute_id, string_to_array(av.value, ',')) as attr
      from core_backend.user_attribute_value av) as tr
where u.id = tr.id;

CREATE TYPE "core_backend"."attribute_entity" AS ENUM('USER', 'INFLUENCER');--> statement-breakpoint
ALTER TYPE "core_backend"."user_attribute_type" RENAME TO "attribute_type";--> statement-breakpoint
ALTER TYPE "core_backend"."attribute_type" ADD VALUE 'FILE';--> statement-breakpoint
ALTER TABLE "core_backend"."user_attribute_value" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
DROP TABLE "core_backend"."user_attribute_value" CASCADE;--> statement-breakpoint
ALTER TABLE "core_backend"."attribute" DROP CONSTRAINT "attribute_pkey";--> statement-breakpoint
ALTER TABLE "core_backend"."attribute" ADD CONSTRAINT "attribute_id_influencer_id_pk" PRIMARY KEY("id","influencer_id");--> statement-breakpoint
ALTER TABLE "core_backend"."attribute" ADD COLUMN "entity" "core_backend"."attribute_entity" DEFAULT 'USER' NOT NULL;--> statement-breakpoint
ALTER TABLE "core_backend"."influencer" ADD COLUMN "attributes" jsonb;--> statement-breakpoint
