import { Middleware } from "@koa/router";
import {
  AuthorizedContextState,
  AuthAdmin,
  AuthOfficer,
  AppError,
} from "@mainframe-peru/common-core";
import { admin as at } from "@mainframe-peru/types";
import { adminService } from "../service";

export const deleteAdminEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin | AuthOfficer>,
  unknown,
  at.DeleteAdminResponse
> = async ({ request, response, state }) => {
  const params = at.DeleteAdminRequestSchema.parse(request.query);

  const admin = await adminService.get("id", params.id);
  if (
    !admin ||
    (state.auth.iss === "mainframe:admin" &&
      state.auth.influencerId !== admin.influencerId)
  ) {
    throw new AppError({
      code: "AdminNotFound",
      message: "Could not find admin",
      statusCode: "NOT_FOUND",
      logLevel: "INFO",
    });
  }

  const result = await adminService.delete(params.id);
  response.body = at.DeleteAdminResponseSchema.parse(result);
};
