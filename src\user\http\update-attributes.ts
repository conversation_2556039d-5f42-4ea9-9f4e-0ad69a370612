import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { user } from "@mainframe-peru/types";
import { restrictParams } from "../../common";
import { userService } from "../service";

export const updateAttributesEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  user.UpdateUserAttributesResponse
> = async ({ request, response, state }) => {
  const body = user.UpdateUserAttributesRequestSchema.parse(request.body);

  restrictParams(state.auth, body);

  const result = await userService.processAttributeUpdate({
    entityId: body.userId || -1,
    influencerId: body.influencerId || "",
    values: body.attributes,
  });

  response.body = {
    id: body.userId || -1,
    attributes: result,
  };
};
