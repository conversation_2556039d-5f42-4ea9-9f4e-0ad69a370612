import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { userEventParticipation } from "@mainframe-peru/types";
import { userEventParticipationService } from "../service";
import { restrictParams } from "../../common";
import { userEventInviteService } from "../../user-event-invite";

export const getUserEventParticipationEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  userEventParticipation.GetUserEventParticipationResponse
> = async ({ request, response, state }) => {
  const params =
    userEventParticipation.GetUserEventParticipationRequestSchema.parse(
      request.query,
    );
  restrictParams(state.auth, params);

  const entity = await userEventParticipationService.getByUserIdAndEventId(
    params.userId,
    params.eventId,
  );

  const invites = await userEventInviteService.list({
    eventId: params.eventId,
    userId: params.userId,
  });

  response.body = {
    userId: params.userId,
    eventId: params.eventId,
    quantity: entity?.quantity || 0,
    participationFormValues: entity?.participationFormValues || undefined,
    invites: invites.map((i) => ({
      id: i.id,
      email: i.email,
      status: i.status,
      acceptedAt: i.acceptedAt || undefined,
    })),
  };
};
