import { Middleware } from "@koa/router";
import {
  AppError,
  AuthAdmin,
  AuthOfficer,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { admin } from "@mainframe-peru/types";
import { adminService } from "../service";

export const getAdmin: Middleware<
  AuthorizedContextState<AuthAdmin | AuthOfficer>,
  unknown,
  admin.GetAdminResponse
> = async ({ request, response, state }) => {
  const params = admin.GetAdminRequestSchema.parse(request.query);

  const entity = await adminService.get("id", params.id);

  if (
    !entity ||
    (state.auth.iss === "mainframe:admin" &&
      state.auth.influencerId !== entity.influencerId)
  ) {
    throw new AppError({
      code: "InfluencerNotFound",
      message: "No se encontró al influencer solicitado",
      statusCode: "NOT_FOUND",
    });
  }

  response.body = admin.GetAdminResponseSchema.parse(entity);
};
