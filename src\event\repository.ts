import { event } from "@mainframe-peru/types";
import {
  and,
  asc,
  desc,
  eq,
  gte,
  ilike,
  InferSelectModel,
  lte,
  SQL,
} from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { eventTable } from "../schema";
import { sc } from "../services";

export type EventEntity = InferSelectModel<typeof eventTable>;

class EventRepository extends RepositoryBase<typeof eventTable> {
  constructor() {
    super(eventTable);
  }

  async findEvents(filters: event.ListEventsRequest): Promise<EventEntity[]> {
    const conditions: SQL[] = [];
    if (filters.status) {
      conditions.push(eq(this.table.status, filters.status));
    }

    if (filters.type) {
      conditions.push(eq(this.table.type, filters.type));
    }

    let sorting = undefined;
    if (filters.sortBy) {
      if (filters.sortDirection && filters.sortDirection === "DESCENDANT") {
        sorting = desc(this.table[filters.sortBy]);
      } else {
        sorting = asc(this.table[filters.sortBy]);
      }
    }

    if (filters.compound) {
      conditions.push(ilike(this.table.name, `%${filters.compound}%`));
    }

    if (filters.startDate) {
      conditions.push(gte(this.table.eventDate, filters.startDate));
    }
    if (filters.endDate) {
      conditions.push(lte(this.table.eventDate, filters.endDate));
    }

    const query = (await sc.getDB())
      .select()
      .from(this.table)
      .where(
        and(
          eq(this.table.influencerId, filters.influencerId as string),
          ...conditions,
        ),
      );
    if (sorting) {
      query.orderBy(sorting, this.table.priority);
    } else {
      query.orderBy(this.table.priority);
    }

    return await query;
  }
}

export const eventRepository = new EventRepository();
