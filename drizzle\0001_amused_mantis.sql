DO $$ BEGIN
 CREATE TYPE "core_backend"."complaint_age_category" AS ENUM('ADULT', 'MINOR');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."complaint_support_status" AS ENUM('OPENED', 'CLOSED', 'ARCHIVED');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."complaint_type" AS ENUM('CLAIM', 'COMPLAINT');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."complaint" (
	"complaint_id" serial PRIMARY KEY NOT NULL,
	"influencer_id" text NOT NULL,
	"first_name" varchar NOT NULL,
	"last_name" varchar NOT NULL,
	"email" varchar NOT NULL,
	"phone" varchar NOT NULL,
	"document_type" varchar NOT NULL,
	"document_value" varchar NOT NULL,
	"cage_category" "core_backend"."complaint_age_category" NOT NULL,
	"status" "core_backend"."complaint_support_status" DEFAULT 'OPENED' NOT NULL,
	"type" "core_backend"."complaint_type" DEFAULT 'COMPLAINT' NOT NULL,
	"subject" varchar NOT NULL,
	"description" varchar NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."complaint" ADD CONSTRAINT "complaint_influencer_id_influencer_id_fk" FOREIGN KEY ("influencer_id") REFERENCES "core_backend"."influencer"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
