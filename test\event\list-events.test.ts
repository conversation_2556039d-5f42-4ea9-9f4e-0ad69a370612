import { event } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { eventService } from "../../src/event";
import { influencerService } from "../../src/influencer/service";
import { adminAuthToken, TestData } from "../common";

describe("list events tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    for (let i = 0; i < 10; i++) {
      await eventService.create({
        influencerId: TestData.influencer.id,
        name: "Test event",
        description: "New description",
        type: "PRIZE",
        status: i & 1 ? "ACTIVE" : "INACTIVE",
        imageUrl: "www.pchujoy.com",
        startDate: new Date(),
        location: "Lima",
      });
    }
  });

  test("list all events", async () => {
    const queryParams: event.ListEventsRequest = {
      influencerId: TestData.influencer.id,
      sortBy: "startDate",
      sortDirection: "ASCENDANT",
    };

    const response = await request(app)
      .get("/live/event/list-events")
      .query(queryParams)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody = response.body as event.ListEventsResponse;

    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(10);
    expect(responseBody[0].location).toEqual("Lima");
  });

  test("list events by status", async () => {
    const queryParams: event.ListEventsRequest = {
      influencerId: TestData.influencer.id,
      status: "ACTIVE",
    };

    const response = await request(app)
      .get("/live/event/list-events")
      .query(queryParams)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody = response.body as event.ListEventsResponse;

    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(5);
  });

  test("search events by partial event name", async () => {
    await eventService.create({
      influencerId: TestData.influencer.id,
      name: "Looking for me!",
      description: "New description",
      type: "PRIZE",
      status: "ACTIVE",
      imageUrl: "www.pchujoy.com",
      startDate: new Date(),
    });

    const queryParams: event.ListEventsRequest = {
      influencerId: TestData.influencer.id,
      compound: "looking",
    };

    const response = await request(app)
      .get("/live/event/list-events")
      .query(queryParams)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody = response.body as event.ListEventsResponse;

    expect(response.status).toBe(200);
    expect(responseBody.length).toBe(1);
  });

  test("search events with no results", async () => {
    const queryParamsNoResult: event.ListEventsRequest = {
      influencerId: TestData.influencer.id,
      compound: "laaking",
    };

    const responseNoResults = await request(app)
      .get("/live/event/list-events")
      .query(queryParamsNoResult)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(responseNoResults.status).toBe(200);
    expect(responseNoResults.body.length).toBe(0);
  });

  test("search event with exact name", async () => {
    await eventService.create({
      influencerId: TestData.influencer.id,
      name: "Looking for me!",
      description: "New description",
      type: "PRIZE",
      status: "ACTIVE",
      imageUrl: "www.pchujoy.com",
      startDate: new Date(),
    });

    const queryParamsExactName: event.ListEventsRequest = {
      influencerId: TestData.influencer.id,
      compound: "looking for me!",
    };

    const responseExactName = await request(app)
      .get("/live/event/list-events")
      .query(queryParamsExactName)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(responseExactName.status).toBe(200);
    expect(responseExactName.body.length).toBe(1);
  });

  test("search with coincidences", async () => {
    const queryParamsCoincidence: event.ListEventsRequest = {
      influencerId: TestData.influencer.id,
      compound: "TEsT",
    };

    const responseCoincidence = await request(app)
      .get("/live/event/list-events")
      .query(queryParamsCoincidence)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(responseCoincidence.status).toBe(200);
    expect(responseCoincidence.body.length).toBeGreaterThan(0);

    const events = responseCoincidence.body as event.ListEventsResponse;
    events.map((event) =>
      expect(event.name.toLowerCase()).toContain(
        queryParamsCoincidence.compound?.toLowerCase(),
      ),
    );
  });

  test("list events ordered by priority", async () => {
    const influencer = await influencerService.create({
      ...TestData.influencer,
      id: "test-influencer",
    });
    for (let i = 0; i < 10; i++) {
      await eventService.create({
        influencerId: influencer.id,
        name: `Test event${i}`,
        description: "New description",
        type: "PRIZE",
        status: i & 1 ? "ACTIVE" : "INACTIVE",
        imageUrl: "www.pchujoy.com",
        startDate: new Date(),
        priority: 9 - i,
      });
    }

    const queryParams: event.ListEventsRequest = {
      influencerId: influencer.id,
    };

    const response = await request(app)
      .get("/live/event/list-events")
      .query(queryParams)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody = response.body as event.ListEventsResponse;

    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(10);

    for (let i = 0; i < 10; i++) {
      expect(responseBody[i].name).toEqual(`Test event${9 - i}`);
    }
  });
});
