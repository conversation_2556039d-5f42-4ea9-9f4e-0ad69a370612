import { Middleware } from "@koa/router";
import {
  AppError,
  AuthAdmin,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { invoice as it } from "@mainframe-peru/types";
import { transactionService } from "../../transaction";
import { invoiceService } from "../service";
import { recurrenceService } from "../../recurrence";

export const createInvoiceEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  it.http.CreateInvoiceResponse
> = async ({ request, response, state }) => {
  const body = it.http.CreateInvoiceRequestSchema.parse(request.body);

  const completeTransaction = await transactionService.getCompleteTransaction(
    body.transactionId,
    state.auth.influencerId,
  );

  if (!completeTransaction) {
    throw new AppError({
      code: "TransactionNotFound",
      message: "Could not find transaction",
      logLevel: "INFO",
      statusCode: "NOT_FOUND",
    });
  }

  const { transaction, transaction_detail } = completeTransaction;
  const invoice = await invoiceService.get("transactionId", transaction.id);
  if (invoice) {
    throw new AppError({
      code: "InvoiceExists",
      message: "A invoice exists for given transaction",
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  }

  const recurrence = await recurrenceService.get(
    "id",
    transaction_detail.entityId,
  );
  if (!recurrence) {
    throw new AppError({
      code: "RecurrenceNotFound",
      message: "Could not find recurrence",
      logLevel: "INFO",
      statusCode: "INTERNAL_SERVER_ERROR",
    });
  }

  const newInvoice = await invoiceService.generateAndCreateInvoice(
    transaction,
    recurrence?.invoiceDestination,
  );

  response.body = {
    invoiceId: newInvoice.id,
    link: newInvoice.link,
    number: newInvoice.number,
    serie: newInvoice.serie,
  };
};
