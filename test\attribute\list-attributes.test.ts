import { attribute as at } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { attributeService } from "../../src/attribute";
import { TestData, adminAuthToken, userAuthToken } from "../common";
import { influencerService } from "../../src/influencer";
import { userService } from "../../src/user";
import { AttributeEntity } from "../../src/attribute/repository";

describe("attribute.list-attributes", () => {
  let f1: AttributeEntity;
  let f2: AttributeEntity;
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    f1 = await attributeService.create({
      id: "field1",
      influencerId: TestData.influencer.id,
      entity: "USER",
      text: "example",
      type: "MULTI",
      options: [
        {
          id: "uni",
          value: "universitario",
        },
        {
          id: "ali",
          value: "alianza",
        },
      ],
    });
    f2 = await attributeService.create({
      id: "field2",
      influencerId: TestData.influencer.id,
      entity: "USER",
      text: "second example",
      type: "SINGLE",
      options: [
        {
          id: "uni",
          value: "universitario",
        },
        {
          id: "ali",
          value: "alianza",
        },
      ],
    });
  });

  test("list all attributes of the user's influencer", async () => {
    const response = await request(app)
      .get("/live/attribute/")
      .query({ entity: "USER" })
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    const body = response.body as at.ListAttributesResponse;
    expect(body.length).toEqual(2);
    expect(body).toEqual([
      {
        id: f1.id,
        options: f1.options,
        text: f1.text,
        type: f1.type,
      },
      {
        id: f2.id,
        options: f2.options,
        text: f2.text,
        type: f2.type,
      },
    ]);
  });

  test("list all attributes of the admins's influencer", async () => {
    const response = await request(app)
      .get("/live/attribute/")
      .query({ entity: "USER" })
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    const body = response.body as at.ListAttributesResponse;
    expect(body.length).toEqual(2);
    expect(body).toEqual([
      {
        id: f1.id,
        options: f1.options,
        text: f1.text,
        type: f1.type,
      },
      {
        id: f2.id,
        options: f2.options,
        text: f2.text,
        type: f2.type,
      },
    ]);
  });
});
