import { user } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { userService } from "../../src/user";
import { TestData, officerAuthToken, userAuthToken } from "../common";
import { recurrenceService } from "../../src/recurrence";
import { planService } from "../../src/plan";
import { productService } from "../../src/product/service";

describe("get user tests", () => {
  test("gets an existing user by id", async () => {
    await influencerService.create(TestData.influencer);
    const testEntity = await userService.create({
      ...TestData.user,
      attributes: {
        team: "uni",
      },
    });

    const queryParams: user.GetUserRequest = {
      id: testEntity.id,
    };

    const response = await request(app)
      .get("/live/user")
      .query(queryParams)
      .set("Cookie", `session=${await officer<PERSON>uthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      activeRecurrence: false,
      birthDate: TestData.user.birthDate?.toISOString(),
      city: TestData.user.city,
      authenticationType: TestData.user.authenticationType,
      companyId: TestData.user.companyId,
      country: TestData.user.country,
      district: TestData.user.district,
      id: testEntity.id,
      email: testEntity.email,
      firstName: testEntity.firstName,
      lastName: testEntity.lastName,
      phone: testEntity.phone,
      gender: testEntity.gender,
      documentType: testEntity.documentType,
      documentValue: testEntity.documentValue,
      province: TestData.user.province,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
      zipCode: TestData.user.zipCode,
      line1: TestData.user.line1,
      line2: TestData.user.line2,
      influencerId: TestData.user.influencerId,
      attributes: {
        team: "uni",
      },
    });
  });

  test("gets an existing user by id with recurrence", async () => {
    await influencerService.create(TestData.influencer);
    await productService.create(TestData.product);
    await planService.create(TestData.plan);
    const testEntity = await userService.create(TestData.user);
    await recurrenceService.create(TestData.recurrence);

    const queryParams: user.GetUserRequest = {
      id: testEntity.id,
    };

    const response = await request(app)
      .get("/live/user")
      .query(queryParams)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual(
      expect.objectContaining({
        planId: TestData.plan.id,
        activeRecurrence: true,
      }),
    );
  });
});
