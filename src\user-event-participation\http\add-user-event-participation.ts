import { Middleware } from "@koa/router";
import {
  AppError,
  Auth,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { common, userEventParticipation } from "@mainframe-peru/types";
import { emailNotification, quest, restrictParams } from "../../common";
import { eventService } from "../../event";
import { influencerService } from "../../influencer";
import { userEventInviteService } from "../../user-event-invite";
import { userEventParticipationService } from "../service";

export const addUserEventParticipationEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  userEventParticipation.CreateUserEventParticipationResponse
> = async ({ request, response, state }) => {
  const body =
    userEventParticipation.CreateUserEventParticipationRequestSchema.parse(
      request.body,
    );

  restrictParams(state.auth, body);
  const event = await eventService.get("id", body.eventId);
  if (!event || (event.type !== "PRIZE" && event.type !== "IN-PERSON")) {
    throw new AppError({
      code: "EventNotFound",
      message: "The given event was not found, or is an invalid type",
      statusCode: "NOT_FOUND",
      logLevel: "INFO",
    });
  }

  if (event.endDate && event.endDate < new Date()) {
    throw new AppError({
      code: "EventEnded",
      message: "The given event has already ended",
      statusCode: "BAD_REQUEST",
      logLevel: "INFO",
    });
  }

  let attributeValues: common.AttributeValues | undefined = undefined;
  if (event.participationForm) {
    attributeValues = quest.parseValues({
      attributes: event.participationForm,
      values: body.participationFormValues || {},
    });
  }

  const created =
    await userEventParticipationService.addSingleUserEventParticipation(
      body.userId,
      body.eventId,
      attributeValues,
    );

  // Handle invite creation if inviteEmails are provided
  const inviteIds: string[] = [];
  if (created && body.inviteEmails && body.inviteEmails.length > 0) {
    const invites = await userEventInviteService.createInvites({
      event,
      emails: body.inviteEmails,
      userId: body.userId,
    });
    inviteIds.push(...invites.map((i) => i.id));

    // Send email invitations
    const influencer = await influencerService.get("id", event.influencerId);
    if (influencer) {
      for (const invite of invites) {
        await emailNotification.sendEventInvite(invite.email, influencer, {
          eventName: event.name,
          eventDescription: event.description,
          inviteUrl: `https://${influencer.domain}/event-invite/${invite.id}`,
          eventDate: event.eventDate?.toISOString(),
          eventLocation: event.location || undefined,
        });
      }
    }
  }

  response.body = {
    userId: body.userId,
    eventId: body.eventId,
    inviteIds,
  };
};
