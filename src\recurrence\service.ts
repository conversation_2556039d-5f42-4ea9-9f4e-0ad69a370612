import { AppError } from "@mainframe-peru/common-core";
import { NTU } from "../helpers";
import { ServiceBase } from "../service-base";
import { RecurrenceEntity, recurrenceRepository } from "./repository";

class RecurrenceService extends ServiceBase<
  typeof recurrenceRepository,
  RecurrenceEntity
> {
  constructor() {
    super(recurrenceRepository);
  }

  async handleCancelFutureRecurrence(userId: number): Promise<void> {
    const currentRecurrence = await this.getActiveRecurrence(userId);
    if (!currentRecurrence) {
      throw new AppError({
        code: "RecurrenceNotFound",
        message: "Este usuario no tiene un pago recurrente",
        statusCode: "NOT_FOUND",
        logLevel: "INFO",
      });
    }

    if (currentRecurrence.endDate) {
      throw new AppError({
        code: "RecurrenceEnded",
        message: "El pago recurrente activo tiene una fecha de finalización",
        statusCode: "BAD_GATEWAY",
        logLevel: "CRITICAL",
      });
    }

    await this.update(currentRecurrence.id, {
      endDate: currentRecurrence.renewalDate,
    });
  }

  async getActiveRecurrence(
    userId: number,
    influencerId?: string,
  ): Promise<NTU<RecurrenceEntity> | undefined> {
    if (influencerId) {
      return this.repository.getActiveRecurrence(userId, influencerId);
    }
    return this.repository.findRecurrenceByStatus(userId, "ACTIVE");
  }

  async getActiveRecurrenceByEmail(
    email: string,
    influencerId: string,
  ): Promise<NTU<RecurrenceEntity> | undefined> {
    return this.repository.getActiveRecurrenceByEmail(email, influencerId);
  }

  async findRecurrenceToRenew() {
    return this.repository.findRecurrenceToRenew();
  }
}

export const recurrenceService = new RecurrenceService();
