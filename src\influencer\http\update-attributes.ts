import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { influencer } from "@mainframe-peru/types";
import { restrictParams } from "../../common";
import { influencerService } from "../service";
import {
  CloudFrontClient,
  CreateInvalidationCommand,
} from "@aws-sdk/client-cloudfront";
import { sc } from "../../services";

export const updateAttributesEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  influencer.UpdateInfluencerAttributesResponse
> = async ({ request, response, state }) => {
  const body = influencer.UpdateInfluencerAttributesRequestSchema.parse(
    request.body,
  );

  restrictParams(state.auth, body);

  const result = await influencerService.processAttributeUpdate({
    entityId: body.influencerId || "",
    influencerId: body.influencerId || "",
    values: body.attributes,
  });

  const command = new CreateInvalidationCommand({
    DistributionId: sc.vars.fccFrontendDistributionId,
    InvalidationBatch: {
      Paths: {
        Quantity: 1,
        Items: [`/api/influencer/public?id=${body.influencerId}`],
      },
      CallerReference: crypto.randomUUID(), // required
    },
  });
  await new CloudFrontClient().send(command);

  response.body = {
    id: body.influencerId || "",
    attributes: result,
  };
};
