import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { createBusinessPromotionCodeEndpoint } from "./create";
import { deleteBusinessPromotionCodeEndpoint } from "./delete";
import { generateCodesEndpoint } from "./generate";
import { listCodesEndpoint } from "./list";
import { uploadCodesEndpoint } from "./upload";
import { useBusinessPromotionCodeEndpoint } from "./useCode";
import { getBusinessPromotionCodeEndpoint } from "./get";

const router = new Router();
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.get(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      businessPromotionCode: ["READ_PROMOTION_CODE"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  getBusinessPromotionCodeEndpoint,
);

router.post(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      businessPromotionCode: ["PUT_PROMOTION_CODE"],
    },
  }),
  createBusinessPromotionCodeEndpoint,
);

router.post(
  "/delete",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      businessPromotionCode: ["DELETE_PROMOTION_CODE"],
    },
  }),
  deleteBusinessPromotionCodeEndpoint,
);

router.post(
  "/generate",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      businessPromotionCode: ["PUT_PROMOTION_CODE"],
    },
  }),
  generateCodesEndpoint,
);

router.get(
  "/list",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      businessPromotionCode: ["LIST_PROMOTION_CODE"],
    },
  }),
  listCodesEndpoint,
);

router.post(
  "/upload",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      businessPromotionCode: ["PUT_PROMOTION_CODE"],
    },
  }),
  uploadCodesEndpoint,
);

router.post(
  "/use",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    user: {
      general: ["REGULAR"],
    },
  }),
  useBusinessPromotionCodeEndpoint,
);

export const businessPromotionCodeRouter = router;
