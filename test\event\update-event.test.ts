import { event } from "@mainframe-peru/types";
import "aws-sdk-client-mock-jest";
import request from "supertest";
import { app } from "../../src/api-handler";
import { eventService } from "../../src/event/service";
import { influencerService } from "../../src/influencer";
import { productService } from "../../src/product/service";
import { sc } from "../../src/services";
import { adminAuthToken, TestData } from "../common";

jest.mock("@aws-sdk/client-scheduler");

describe("Update event test", () => {
  test("Successfully update event", async () => {
    await influencerService.create(TestData.influencer);
    await productService.create(TestData.product);
    await eventService.create(TestData.event);

    const updateEventRequest: event.UpdateEventRequest = {
      id: TestData.event.id,
      influencerId: TestData.influencer.id,
      type: "GAME",
      participationForm: TestData.event.participationForm || undefined,
      eventDate: new Date(2050, 0, 0),
      location: "Arequipa",
    };

    const response = await request(app)
      .put("/live/event")
      .send(updateEventRequest)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    const updatedEvent = await eventService.get("id", response.body.id);

    expect(response.statusCode).toEqual(200);
    expect(updatedEvent?.type).toEqual(updateEventRequest.type);
    expect(updatedEvent?.participationForm).toEqual(
      updateEventRequest.participationForm,
    );
    expect(updatedEvent?.location).toEqual(updateEventRequest.location);

    expect(sc.scheduler.send).toHaveBeenCalledTimes(0);
  });

  test("Successfully update event with scheduler", async () => {
    await influencerService.create(TestData.influencer);
    await productService.create(TestData.product);
    await eventService.create(TestData.event);

    const updateEventRequest: event.UpdateEventRequest = {
      id: TestData.event.id,
      influencerId: TestData.influencer.id,
      eventDate: new Date(2070, 0, 0),
    };

    const response = await request(app)
      .put("/live/event")
      .send(updateEventRequest)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.statusCode).toEqual(200);
    expect(sc.scheduler.send).toHaveBeenCalledTimes(1);
  });

  test("Successfully update event's image", async () => {
    await influencerService.create(TestData.influencer);
    await productService.create(TestData.product);
    await eventService.create(TestData.event);

    const updateEventRequest: event.UpdateEventRequest = {
      id: TestData.event.id,
      influencerId: TestData.influencer.id,
      type: "GAME",
      status: "INACTIVE",
      imageUrl: "https://www.google.com/qwerty/",
    };

    const response = await request(app)
      .put("/live/event")
      .send(updateEventRequest)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    const updatedEvent = await eventService.get("id", response.body.id);

    expect(response.statusCode).toEqual(200);
    expect(updatedEvent?.type).toEqual(updateEventRequest.type);
    expect(updatedEvent?.status).toEqual(updateEventRequest.status);
  });
});
