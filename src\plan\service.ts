import { NTU } from "../helpers";
import { ServiceBase } from "../service-base";
import { PlanEntity, planRepository } from "./repository";

class PlanService extends ServiceBase<typeof planRepository, PlanEntity> {
  constructor() {
    super(planRepository);
  }

  async getByIdAndInfluencer(
    planId: number,
    influencerId?: string,
  ): Promise<NTU<PlanEntity> | undefined> {
    return this.repository.getByIdAndInfluencer(planId, influencerId);
  }
  getPlansProduct: typeof this.repository.getPlansProduct = () =>
    this.repository.getPlansProduct();
}

export const planService = new PlanService();
