import { Middleware } from "@koa/router";
import { AuthAdmin, AuthorizedContextState } from "@mainframe-peru/common-core";
import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import { businessPromotionService } from "../../business-promotion/service";
import { businessPromotionCodeService } from "../service";

export const listCodesEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  bpc.ListBusinessPromotionCodeResponse
> = async ({ request, response, state }) => {
  const params = bpc.ListBusinessPromotionCodeRequestSchema.parse(
    request.query,
  );
  await businessPromotionService.getSecure({
    id: params.promotionId,
    influencerId: state.auth.influencerId,
  });
  const promotionsCodes = await businessPromotionCodeService.list({
    promotionId: params.promotionId,
  });

  response.body = promotionsCodes.map((p) => ({
    id: p.id,
    code: p.code,
    userId: p.userId || undefined,
  }));
};
