import { Middleware } from "@koa/router";
import {
  Auth<PERSON><PERSON><PERSON>,
  AuthOfficer,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { user } from "@mainframe-peru/types";
import {
  ListUsersRequestSchema,
  ListUsersResponseSchema,
} from "@mainframe-peru/types/build/user";
import { restrictParams, setCsvResponse } from "../../common";
import { userService } from "../service";
import { formatDateTimeLocale } from "../../helpers";
import { attributeService } from "../../attribute";

export const listUsers: Middleware<
  AuthorizedContextState<AuthOfficer | AuthAdmin>,
  unknown,
  user.ListUsersResponse | NodeJS.WritableStream
> = async ({ request, response, state }) => {
  const query = request.query
    ? ListUsersRequestSchema.parse(request.query)
    : {};

  restrictParams(state.auth, query);

  const attributes = await attributeService.list({
    influencerId: query.influencerId,
  });
  const entities = await userService.getUsers(query);

  if (request.accepts("text/csv")) {
    const publicResult = entities.map((x) => {
      const r: Record<string, unknown> = {
        id: x.id,
        alias: x.alias,
        firstName: x.firstName,
        lastName: x.lastName,
        email: x.email,
        phone: x.phone,
        plan: x.activeRecurrentPlan,
        address: x.line1,
        city: x.city,
        country: x.country,
        subscribedSince: formatDateTimeLocale(
          x.activeRecurrentPaymentStartDate,
        ),
        paymentType: x.activeRecurrentPaymentType,
        createdAt: formatDateTimeLocale(x.createdAt),
      };
      for (const at of attributes) {
        const v = x.attributes?.[at.id];
        r[at.id] = v && Array.isArray(v) ? v.join(",") : v;
      }
      return r;
    });

    setCsvResponse(publicResult, response, "users");
    return;
  }
  response.body = ListUsersResponseSchema.parse(
    entities.map((x) => ({
      ...x,
      activeRecurrence:
        query.detailed && x.activeRecurrentPaymentId
          ? {
              id: x.activeRecurrentPaymentId,
              type: x.activeRecurrentPaymentType,
              startDate: x.activeRecurrentPaymentStartDate,
              plan: x.activeRecurrentPlan,
              renewalDate: x.activeRecurrentPaymentRenewalDate,
            }
          : undefined,
    })),
  );
};
