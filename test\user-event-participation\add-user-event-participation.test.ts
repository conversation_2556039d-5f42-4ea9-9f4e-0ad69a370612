import { userEventParticipation } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { eventService } from "../../src/event";
import { influencerService } from "../../src/influencer";
import { planService } from "../../src/plan";
import { productService } from "../../src/product/service";
import { userService } from "../../src/user";
import { userEventParticipationService } from "../../src/user-event-participation";
import { TestData, userAuthToken } from "../common";

describe("create (signin a user to an event) user event participation", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await productService.create(TestData.product);
    await planService.create(TestData.plan);
    await eventService.create(TestData.event);
  });

  test("add the user participation", async () => {
    const response = await request(app)
      .post("/live/user-event-participation")
      .send({
        eventId: TestData.event.id,
        userId: TestData.user.id,
      })
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toEqual(200);

    const participation =
      await userEventParticipationService.getByUserIdAndEventId(
        TestData.user.id,
        TestData.event.id,
      );
    expect(participation).toEqual(
      expect.objectContaining({
        userId: TestData.user.id,
        quantity: 1,
      }),
    );
  });

  test("avoiding duplication of user participation", async () => {
    await userEventParticipationService.create(TestData.userEventParticipation);
    const response = await request(app)
      .post("/live/user-event-participation")
      .send({
        eventId: TestData.event.id,
        userId: TestData.user.id,
      })
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toEqual(200);

    const participation =
      await userEventParticipationService.getByUserIdAndEventId(
        TestData.user.id,
        TestData.event.id,
      );
    expect(participation).toEqual(
      expect.objectContaining({
        userId: TestData.user.id,
        quantity: 1, // JUST ONE
      }),
    );
  });

  test("add user participation with form", async () => {
    const e = await eventService.create({
      ...TestData.event,
      id: undefined,
      participationForm: [
        {
          id: "dni",
          text: "DNI",
          type: "TEXT",
          required: true,
        },
      ],
    });

    const body: userEventParticipation.CreateUserEventParticipationRequest = {
      eventId: e.id,
      userId: TestData.user.id,
      participationFormValues: {
        dni: "12345678",
      },
    };
    const response = await request(app)
      .post("/live/user-event-participation")
      .send(body)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toEqual(200);

    const participation =
      await userEventParticipationService.getByUserIdAndEventId(
        TestData.user.id,
        e.id,
      );
    expect(participation).toEqual(
      expect.objectContaining({
        userId: TestData.user.id,
        quantity: 1, // JUST ONE
        participationFormValues: body.participationFormValues,
      }),
    );
  });
});
