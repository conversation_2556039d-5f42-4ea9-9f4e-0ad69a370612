import { and, eq, InferSelectModel } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { userEventParticipationTable, userTable } from "../schema";
import { sc } from "../services";
import { transformNullToUndefined } from "../helpers";

export type UserEventParticipationEntity = InferSelectModel<
  typeof userEventParticipationTable
>;

class UserEventParticipationRepository extends RepositoryBase<
  typeof userEventParticipationTable
> {
  constructor() {
    super(userEventParticipationTable);
  }

  async findByUserIdAndEventId(
    userId: number,
    eventId: number,
  ): Promise<UserEventParticipationEntity | undefined> {
    const result = await (
      await sc.getDB()
    )
      .select()
      .from(this.table)
      .where(
        and(eq(this.table.userId, userId), eq(this.table.eventId, eventId)),
      );
    return result[0];
  }

  async updateUserEventParticipation(
    userId: number,
    eventId: number,
    quantity: number,
  ): Promise<UserEventParticipationEntity> {
    return (
      await (
        await sc.getDB()
      )
        .update(this.table)
        .set({
          quantity,
        })
        .where(
          and(eq(this.table.userId, userId), eq(this.table.eventId, eventId)),
        )
        .returning()
    )[0];
  }

  async listEventParticipants(eventId: number) {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .innerJoin(userTable, eq(this.table.userId, userTable.id))
      .where(eq(this.table.eventId, eventId));
    return transformNullToUndefined(result);
  }
}

export const userEventParticipationRepository =
  new UserEventParticipationRepository();
