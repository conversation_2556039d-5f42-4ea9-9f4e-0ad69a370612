import { Middleware } from "@koa/router";
import {
  AuthEndUser,
  AuthorizedContextState,
  Policies,
  createJWT,
} from "@mainframe-peru/common-core";
import { userPoliciesConstant } from "@mainframe-peru/common-core/build/policies/lib/modules/user";
import { user } from "@mainframe-peru/types";
import { sc } from "../../services";

export const getToken: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  user.GetTokenResponse
> = async ({ response, state }) => {
  const expirationTime = Math.floor(Date.now() / 1000) + 10 * 60; // expires in 10 minutes
  response.body = {
    token: await createJWT(
      "user",
      {
        ...state.auth,
        policies: Policies.mask(
          {
            general: {
              TRANSIENT: true,
            },
          },
          userPoliciesConstant,
        ),
      },
      sc.vars.keys.user.private,
      expirationTime,
    ),
  };
};
