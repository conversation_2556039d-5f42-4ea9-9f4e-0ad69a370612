import { common } from "@mainframe-peru/types";
import { ServiceBase } from "../service-base";
import {
  UserPaymentProviderEntity,
  userPaymentProviderRepository,
} from "./repository";

class UserPaymentProviderService extends ServiceBase<
  typeof userPaymentProviderRepository,
  UserPaymentProviderEntity
> {
  constructor() {
    super(userPaymentProviderRepository);
  }

  async getUserPaymentProvider(
    userId: number,
    providerId: common.PaymentProvider,
  ): Promise<UserPaymentProviderEntity | undefined> {
    return await this.repository.findUserPaymentProvider(userId, providerId);
  }
}

export const userPaymentProviderService = new UserPaymentProviderService();
