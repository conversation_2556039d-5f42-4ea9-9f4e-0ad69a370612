import { Middleware } from "@koa/router";
import { plan } from "@mainframe-peru/types";
import { planService } from "../service";
import { restrictParams } from "../../common";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";

export const createPlanEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  plan.CreatePlanResponse
> = async ({ request, response, state }) => {
  const body = plan.CreatePlanRequestSchema.parse(request.body);

  restrictParams(state.auth, body);
  const entityId = await planService.create(body);

  response.body = {
    id: entityId.id,
  };
};
