import { businessPromotion as bp } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { businessPromotionService } from "../../src/business-promotion";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken } from "../common";

describe("list business promotion tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await businessService.create(TestData.business);
    for (let i = 0; i < 100; i++) {
      await businessPromotionService.create({
        businessId: TestData.business.id,
        content: "test",
        expirationDate: new Date(),
        influencerId: TestData.influencer.id,
        name: `test-${i}`,
        status: "ACTIVE",
        type: "PERSONAL",
        value: "test",
      });
    }

    await businessService.create({
      ...TestData.business,
      id: 777,
    });
    await businessPromotionService.create({
      businessId: 777,
      content: "test",
      expirationDate: new Date(),
      influencerId: TestData.influencer.id,
      name: "Testing Business Id",
      status: "ACTIVE",
      type: "GLOBAL",
      value: "test",
    });
  });

  test("list business promotions", async () => {
    const query: bp.ListBusinessPromotionRequest = {
      influencerId: TestData.influencer.id,
    };
    const response = await request(app)
      .get("/live/business-promotion/list")
      .query(query);

    const responseBody: bp.ListBusinessPromotionResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(101);
  });

  test("list business by name", async () => {
    const query: bp.ListBusinessPromotionRequest = {
      influencerId: TestData.influencer.id,
      name: "test-1",
    };

    const response = await request(app)
      .get("/live/business-promotion/list")
      .query(query);

    const responseBody: bp.ListBusinessPromotionResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(11);
  });

  test("list business promotions by business id", async () => {
    const query: bp.ListBusinessPromotionRequest = {
      influencerId: TestData.influencer.id,
      businessId: 777,
    };

    const response = await request(app)
      .get("/live/business-promotion/list")
      .query(query);

    const responseBody: bp.ListBusinessPromotionResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(1);
  });

  test("list business promotions by group", async () => {
    for (let i = 0; i < 5; i++) {
      await businessPromotionService.create({
        businessId: TestData.business.id,
        content: "test",
        expirationDate: new Date(),
        influencerId: TestData.influencer.id,
        name: `test-dest-${i}`,
        status: "ACTIVE",
        type: "PERSONAL",
        value: "test",
        group: "HIGHLIGHTED",
      });
    }

    const query: bp.ListBusinessPromotionRequest = {
      influencerId: TestData.influencer.id,
      group: "HIGHLIGHTED",
    };
    const response = await request(app)
      .get("/live/business-promotion/list")
      .query(query);

    const responseBody: bp.ListBusinessPromotionResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(5);
  });

  test("list business promotions by category", async () => {
    for (let i = 0; i < 9; i++) {
      await businessPromotionService.create({
        businessId: TestData.business.id,
        content: "test",
        expirationDate: new Date(),
        influencerId: TestData.influencer.id,
        name: `test-dest-${i}`,
        status: "ACTIVE",
        type: "PERSONAL",
        value: "test",
        category: "ELECTRONICS",
      });
    }

    const query: bp.ListBusinessPromotionRequest = {
      influencerId: TestData.influencer.id,
      category: "ELECTRONICS",
    };
    const response = await request(app)
      .get("/live/business-promotion/list")
      .query(query);

    const responseBody: bp.ListBusinessPromotionResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(9);
  });

  test("list business promotions ordered by priority", async () => {
    for (let i = 0; i < 9; i++) {
      await businessPromotionService.create({
        businessId: TestData.business.id,
        content: "test",
        expirationDate: new Date(),
        influencerId: TestData.influencer.id,
        name: `test-dest-${i}`,
        status: "ACTIVE",
        type: "PERSONAL",
        value: "test",
        category: "ELECTRONICS",
        priority: 9 - i,
      });
    }

    const query: bp.ListBusinessPromotionRequest = {
      influencerId: TestData.influencer.id,
      category: "ELECTRONICS",
    };
    const response = await request(app)
      .get("/live/business-promotion/list")
      .query(query);

    const responseBody: bp.ListBusinessPromotionResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(9);

    for (let i = 0; i < 9; i++) {
      expect(responseBody[i].name).toEqual(`test-dest-${8 - i}`);
    }
  });

  test("list business promotions by status", async () => {
    const query: bp.ListBusinessPromotionRequest = {
      influencerId: TestData.influencer.id,
      status: "INACTIVE",
    };

    const response = await request(app)
      .get("/live/business-promotion/list")
      .query(query);

    const responseBody: bp.ListBusinessPromotionResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(0);
  });
});
