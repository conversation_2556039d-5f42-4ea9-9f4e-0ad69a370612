import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer";
import { TestData } from "../common";
import { mockReCaptchaResponse } from "../mocks";
import { mockClient } from "aws-sdk-client-mock";
import "aws-sdk-client-mock-jest";
import { PublishCommand, SNSClient } from "@aws-sdk/client-sns";

jest.mock("node-fetch");
describe("send support email tests", () => {
  test("successfully send support message test", async () => {
    mockReCaptchaResponse();
    const snsMock = mockClient(SNSClient);

    await influencerService.create(TestData.influencer);

    const response = await request(app)
      .post("/live/contact/support")
      .send({
        influencerId: TestData.influencer.id,
        name: "<PERSON>",
        email: "<EMAIL>",
        phone: "999999911",
        supportType: "MEMBERSHIP",
        motive: "I hate",
        operationDate: new Date(),
        message: "I don't like this system",
        documentValue: "123456789",
        reCaptchaToken: "123123123",
      })
      .set("Accept", "application/json");

    expect(response.statusCode).toEqual(200);
    expect(snsMock).toHaveReceivedCommandTimes(PublishCommand, 1);
    expect(snsMock).toHaveReceivedCommandWith(PublishCommand, {
      Message: expect.stringContaining("MEMBERSHIP"),
      MessageAttributes: {
        service: {
          DataType: "String",
          StringValue: "email",
        },
      },
    });
  });
});
