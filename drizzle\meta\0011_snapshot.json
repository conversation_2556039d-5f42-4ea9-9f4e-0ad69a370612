{"id": "90f5afe8-1aee-461a-bc00-d6138eb13722", "prevId": "d040cf56-cf82-4818-aa96-b269bcfcb8ad", "version": "7", "dialect": "postgresql", "tables": {"core_backend.admin": {"name": "admin", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "influencer_id": {"name": "influencer_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "hash": {"name": "hash", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "policies": {"name": "policies", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {"admin_influencer_id_influencer_id_fk": {"name": "admin_influencer_id_influencer_id_fk", "tableFrom": "admin", "tableTo": "influencer", "schemaTo": "core_backend", "columnsFrom": ["influencer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.attribute": {"name": "attribute", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "text", "primaryKey": false, "notNull": true}, "influencer_id": {"name": "influencer_id", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "attribute_type", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "entity": {"name": "entity", "type": "attribute_entity", "typeSchema": "core_backend", "primaryKey": false, "notNull": true, "default": "'USER'"}, "options": {"name": "options", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"attribute_influencer_id_influencer_id_fk": {"name": "attribute_influencer_id_influencer_id_fk", "tableFrom": "attribute", "tableTo": "influencer", "schemaTo": "core_backend", "columnsFrom": ["influencer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"attribute_id_influencer_id_pk": {"name": "attribute_id_influencer_id_pk", "columns": ["id", "influencer_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.business_promotion_code": {"name": "business_promotion_code", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "promotion_id": {"name": "promotion_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"promotion_code_idx": {"name": "promotion_code_idx", "columns": [{"expression": "promotion_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"business_promotion_code_promotion_id_business_promotion_id_fk": {"name": "business_promotion_code_promotion_id_business_promotion_id_fk", "tableFrom": "business_promotion_code", "tableTo": "business_promotion", "schemaTo": "core_backend", "columnsFrom": ["promotion_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "business_promotion_code_user_id_user_id_fk": {"name": "business_promotion_code_user_id_user_id_fk", "tableFrom": "business_promotion_code", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.business_promotion": {"name": "business_promotion", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "influencer_id": {"name": "influencer_id", "type": "text", "primaryKey": false, "notNull": true}, "business_id": {"name": "business_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "business_promotion_type", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "business_promotion_status", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "terms_and_conditions": {"name": "terms_and_conditions", "type": "text", "primaryKey": false, "notNull": false}, "expirationDate": {"name": "expirationDate", "type": "timestamp", "primaryKey": false, "notNull": true}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "group": {"name": "group", "type": "business_promotion_group", "typeSchema": "core_backend", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "business_promotion_category", "typeSchema": "core_backend", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"business_promotion_influencer_id_influencer_id_fk": {"name": "business_promotion_influencer_id_influencer_id_fk", "tableFrom": "business_promotion", "tableTo": "influencer", "schemaTo": "core_backend", "columnsFrom": ["influencer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "business_promotion_business_id_business_id_fk": {"name": "business_promotion_business_id_business_id_fk", "tableFrom": "business_promotion", "tableTo": "business", "schemaTo": "core_backend", "columnsFrom": ["business_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.business": {"name": "business", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "influencer_id": {"name": "influencer_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "ruc": {"name": "ruc", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"business_influencer_id_influencer_id_fk": {"name": "business_influencer_id_influencer_id_fk", "tableFrom": "business", "tableTo": "influencer", "schemaTo": "core_backend", "columnsFrom": ["influencer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.card": {"name": "card", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "default": {"name": "default", "type": "boolean", "primaryKey": false, "notNull": true}, "payment_provider": {"name": "payment_provider", "type": "payment_provider", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "brand": {"name": "brand", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"card_user_id_user_id_fk": {"name": "card_user_id_user_id_fk", "tableFrom": "card", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.complaint": {"name": "complaint", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "influencer_id": {"name": "influencer_id", "type": "text", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "document_type": {"name": "document_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "document_value": {"name": "document_value", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "age_category": {"name": "age_category", "type": "complaint_age_category", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "complaint_support_status", "typeSchema": "core_backend", "primaryKey": false, "notNull": true, "default": "'OPENED'"}, "type": {"name": "type", "type": "complaint_type", "typeSchema": "core_backend", "primaryKey": false, "notNull": true, "default": "'COMPLAINT'"}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"complaint_influencer_id_influencer_id_fk": {"name": "complaint_influencer_id_influencer_id_fk", "tableFrom": "complaint", "tableTo": "influencer", "schemaTo": "core_backend", "columnsFrom": ["influencer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.event": {"name": "event", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "influencer_id": {"name": "influencer_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "event_status", "typeSchema": "core_backend", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "type": {"name": "type", "type": "event_type", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "event_date": {"name": "event_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "participation_product_id": {"name": "participation_product_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"event_influencer_id_influencer_id_fk": {"name": "event_influencer_id_influencer_id_fk", "tableFrom": "event", "tableTo": "influencer", "schemaTo": "core_backend", "columnsFrom": ["influencer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "event_participation_product_id_product_id_fk": {"name": "event_participation_product_id_product_id_fk", "tableFrom": "event", "tableTo": "product", "schemaTo": "core_backend", "columnsFrom": ["participation_product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.influencer": {"name": "influencer", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "influencer_status", "typeSchema": "core_backend", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": true}, "transient_users": {"name": "transient_users", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "domain": {"name": "domain", "type": "text", "primaryKey": false, "notNull": true}, "google_token": {"name": "google_token", "type": "text", "primaryKey": false, "notNull": false}, "attributes": {"name": "attributes", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.invoice": {"name": "invoice", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "transaction_id": {"name": "transaction_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "invoice_origin": {"name": "invoice_origin", "type": "integer", "primaryKey": false, "notNull": true}, "voucher_type": {"name": "voucher_type", "type": "integer", "primaryKey": false, "notNull": true}, "serie": {"name": "serie", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "integer", "primaryKey": false, "notNull": true}, "link": {"name": "link", "type": "text", "primaryKey": false, "notNull": true}, "accepted_by_sunat": {"name": "accepted_by_sunat", "type": "integer", "primaryKey": false, "notNull": true}, "sunat_description": {"name": "sunat_description", "type": "text", "primaryKey": false, "notNull": true}, "sunat_note": {"name": "sunat_note", "type": "text", "primaryKey": false, "notNull": false}, "sunat_responsecode": {"name": "sunat_responsecode", "type": "text", "primaryKey": false, "notNull": true}, "sunat_soap_error": {"name": "sunat_soap_error", "type": "text", "primaryKey": false, "notNull": false}, "string_qr_code": {"name": "string_qr_code", "type": "text", "primaryKey": false, "notNull": true}, "hash_code": {"name": "hash_code", "type": "text", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"invoice_transaction_id_transaction_id_fk": {"name": "invoice_transaction_id_transaction_id_fk", "tableFrom": "invoice", "tableTo": "transaction", "schemaTo": "core_backend", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invoice_user_id_user_id_fk": {"name": "invoice_user_id_user_id_fk", "tableFrom": "invoice", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.officer": {"name": "officer", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "hash": {"name": "hash", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "policies": {"name": "policies", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.payment_provider_event": {"name": "payment_provider_event", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "transaction_id": {"name": "transaction_id", "type": "integer", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "payment_provider_event_state", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "payment_provider", "typeSchema": "core_backend", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "payment_provider_event_type", "typeSchema": "core_backend", "primaryKey": false, "notNull": false}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": false}, "response_message": {"name": "response_message", "type": "text", "primaryKey": false, "notNull": false}, "external": {"name": "external", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"payment_provider_event_user_id_user_id_fk": {"name": "payment_provider_event_user_id_user_id_fk", "tableFrom": "payment_provider_event", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payment_provider_event_transaction_id_transaction_id_fk": {"name": "payment_provider_event_transaction_id_transaction_id_fk", "tableFrom": "payment_provider_event", "tableTo": "transaction", "schemaTo": "core_backend", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.plan": {"name": "plan", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "influencer_id": {"name": "influencer_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "frequency": {"name": "frequency", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"plan_influencer_id_influencer_id_fk": {"name": "plan_influencer_id_influencer_id_fk", "tableFrom": "plan", "tableTo": "influencer", "schemaTo": "core_backend", "columnsFrom": ["influencer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "plan_product_id_product_id_fk": {"name": "plan_product_id_product_id_fk", "tableFrom": "plan", "tableTo": "product", "schemaTo": "core_backend", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.product": {"name": "product", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "influencer_id": {"name": "influencer_id", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "currency", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "sunat_code": {"name": "sunat_code", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"product_influencer_id_influencer_id_fk": {"name": "product_influencer_id_influencer_id_fk", "tableFrom": "product", "tableTo": "influencer", "schemaTo": "core_backend", "columnsFrom": ["influencer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.recurrence": {"name": "recurrence", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "recurrence_type", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "recurrence_status", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "integer", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "renewal_date": {"name": "renewal_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "invoice_destination": {"name": "invoice_destination", "type": "invoice_destination", "typeSchema": "core_backend", "primaryKey": false, "notNull": true, "default": "'PERSON'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"recurrence_user_id_user_id_fk": {"name": "recurrence_user_id_user_id_fk", "tableFrom": "recurrence", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "recurrence_plan_id_plan_id_fk": {"name": "recurrence_plan_id_plan_id_fk", "tableFrom": "recurrence", "tableTo": "plan", "schemaTo": "core_backend", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.transaction_detail": {"name": "transaction_detail", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "transaction_id": {"name": "transaction_id", "type": "integer", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "integer", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "integer", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"transaction_detail_transaction_id_transaction_id_fk": {"name": "transaction_detail_transaction_id_transaction_id_fk", "tableFrom": "transaction_detail", "tableTo": "transaction", "schemaTo": "core_backend", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transaction_detail_product_id_product_id_fk": {"name": "transaction_detail_product_id_product_id_fk", "tableFrom": "transaction_detail", "tableTo": "product", "schemaTo": "core_backend", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.transaction": {"name": "transaction", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "public_id": {"name": "public_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "influencer_id": {"name": "influencer_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "transaction_type", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "transaction_state", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "channel": {"name": "channel", "type": "transaction_channel", "typeSchema": "core_backend", "primaryKey": false, "notNull": true, "default": "'CARD'"}, "amount": {"name": "amount", "type": "numeric", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "currency", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "last_payment_provider_event_id": {"name": "last_payment_provider_event_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"transaction_public_id_index": {"name": "transaction_public_id_index", "columns": [{"expression": "public_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"transaction_user_id_user_id_fk": {"name": "transaction_user_id_user_id_fk", "tableFrom": "transaction", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transaction_influencer_id_influencer_id_fk": {"name": "transaction_influencer_id_influencer_id_fk", "tableFrom": "transaction", "tableTo": "influencer", "schemaTo": "core_backend", "columnsFrom": ["influencer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.user_event_participation": {"name": "user_event_participation", "schema": "core_backend", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "event_id": {"name": "event_id", "type": "integer", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_event_participation_user_id_user_id_fk": {"name": "user_event_participation_user_id_user_id_fk", "tableFrom": "user_event_participation", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_event_participation_event_id_event_id_fk": {"name": "user_event_participation_event_id_event_id_fk", "tableFrom": "user_event_participation", "tableTo": "event", "schemaTo": "core_backend", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"user_event_participation_user_id_event_id_pk": {"name": "user_event_participation_user_id_event_id_pk", "columns": ["user_id", "event_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.user_payment_provider": {"name": "user_payment_provider", "schema": "core_backend", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "payment_provider": {"name": "payment_provider", "type": "payment_provider", "typeSchema": "core_backend", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_payment_provider_user_id_user_id_fk": {"name": "user_payment_provider_user_id_user_id_fk", "tableFrom": "user_payment_provider", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "core_backend.user": {"name": "user", "schema": "core_backend", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "influencer_id": {"name": "influencer_id", "type": "text", "primaryKey": false, "notNull": true}, "authentication_type": {"name": "authentication_type", "type": "authentication_type", "typeSchema": "core_backend", "primaryKey": false, "notNull": false, "default": "'EMAIL'"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "alias": {"name": "alias", "type": "text", "primaryKey": false, "notNull": true}, "hash": {"name": "hash", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "policies": {"name": "policies", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "gender", "typeSchema": "core_backend", "primaryKey": false, "notNull": false, "default": "'NO'"}, "birth_date": {"name": "birth_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "document_type": {"name": "document_type", "type": "document_type", "typeSchema": "core_backend", "primaryKey": false, "notNull": false}, "document_value": {"name": "document_value", "type": "text", "primaryKey": false, "notNull": false}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "province": {"name": "province", "type": "text", "primaryKey": false, "notNull": false}, "district": {"name": "district", "type": "text", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "text", "primaryKey": false, "notNull": false}, "line1": {"name": "line1", "type": "text", "primaryKey": false, "notNull": false}, "line2": {"name": "line2", "type": "text", "primaryKey": false, "notNull": false}, "attributes": {"name": "attributes", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_influencer_id_influencer_id_fk": {"name": "user_influencer_id_influencer_id_fk", "tableFrom": "user", "tableTo": "influencer", "schemaTo": "core_backend", "columnsFrom": ["influencer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"core_backend.attribute_entity": {"name": "attribute_entity", "schema": "core_backend", "values": ["USER", "INFLUENCER"]}, "core_backend.attribute_type": {"name": "attribute_type", "schema": "core_backend", "values": ["TEXT", "MULTI", "SINGLE", "FILE"]}, "core_backend.authentication_type": {"name": "authentication_type", "schema": "core_backend", "values": ["EMAIL", "GOOGLE"]}, "core_backend.business_promotion_category": {"name": "business_promotion_category", "schema": "core_backend", "values": ["RESTAURANT", "HEALTH", "ELECTRONICS"]}, "core_backend.business_promotion_group": {"name": "business_promotion_group", "schema": "core_backend", "values": ["HIGHLIGHTED"]}, "core_backend.business_promotion_status": {"name": "business_promotion_status", "schema": "core_backend", "values": ["ACTIVE", "INACTIVE"]}, "core_backend.business_promotion_type": {"name": "business_promotion_type", "schema": "core_backend", "values": ["PERSONAL", "GLOBAL"]}, "core_backend.complaint_age_category": {"name": "complaint_age_category", "schema": "core_backend", "values": ["ADULT", "MINOR"]}, "core_backend.complaint_support_status": {"name": "complaint_support_status", "schema": "core_backend", "values": ["OPENED", "CLOSED", "ARCHIVED"]}, "core_backend.complaint_type": {"name": "complaint_type", "schema": "core_backend", "values": ["CLAIM", "COMPLAINT"]}, "core_backend.currency": {"name": "currency", "schema": "core_backend", "values": ["PEN", "USD"]}, "core_backend.document_type": {"name": "document_type", "schema": "core_backend", "values": ["CE", "DNI", "PS", "OT"]}, "core_backend.event_status": {"name": "event_status", "schema": "core_backend", "values": ["ACTIVE", "INACTIVE"]}, "core_backend.event_type": {"name": "event_type", "schema": "core_backend", "values": ["PRIZE", "PRODUCT", "GAME", "IN-PERSON"]}, "core_backend.gender": {"name": "gender", "schema": "core_backend", "values": ["F", "M", "NB", "NO"]}, "core_backend.influencer_status": {"name": "influencer_status", "schema": "core_backend", "values": ["ACTIVE", "INACTIVE"]}, "core_backend.invoice_destination": {"name": "invoice_destination", "schema": "core_backend", "values": ["PERSON", "COMPANY"]}, "core_backend.payment_provider": {"name": "payment_provider", "schema": "core_backend", "values": ["CULQI", "PAYPAL"]}, "core_backend.payment_provider_event_state": {"name": "payment_provider_event_state", "schema": "core_backend", "values": ["FAIL", "SUCCESS"]}, "core_backend.payment_provider_event_type": {"name": "payment_provider_event_type", "schema": "core_backend", "values": ["USER_CREATION", "CARD_CREATION", "CHARGE_CREATION"]}, "core_backend.recurrence_status": {"name": "recurrence_status", "schema": "core_backend", "values": ["CREATING", "ACTIVE", "INACTIVE"]}, "core_backend.recurrence_type": {"name": "recurrence_type", "schema": "core_backend", "values": ["CARD", "MANUAL"]}, "core_backend.transaction_channel": {"name": "transaction_channel", "schema": "core_backend", "values": ["CARD", "YAPE"]}, "core_backend.transaction_state": {"name": "transaction_state", "schema": "core_backend", "values": ["PROCESSING", "FAIL", "SUCCESS"]}, "core_backend.transaction_type": {"name": "transaction_type", "schema": "core_backend", "values": ["RECURRENCE", "PURCHASE"]}}, "schemas": {"core_backend": "core_backend"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}