import {
  SendMessageCommand,
  SendMessageCommandOutput,
} from "@aws-sdk/client-sqs";
import { sc } from "../services";
import { invoice } from "@mainframe-peru/types";

export const invoiceQueue = {
  async sendInvoiceCreation(
    request: invoice.sqs.CreateInvoiceRequest,
  ): Promise<SendMessageCommandOutput> {
    const command = new SendMessageCommand({
      QueueUrl: sc.vars.fifoSqsQueueUrl,
      MessageDeduplicationId: request.transactionId.toString(),
      MessageGroupId: "invoice",
      MessageBody: JSON.stringify(request),
      DelaySeconds: 0,
      MessageAttributes: {
        path: {
          DataType: "String",
          StringValue: "/invoice/create",
        },
        method: {
          DataType: "String",
          StringValue: "POST",
        },
        origin: {
          DataType: "String",
          StringValue: "core-backend",
        },
      },
    });
    return await sc.sqs.send(command);
  },
};
