import { PublishCommand } from "@aws-sdk/client-sns";
import { SendMessageCommandInput } from "@aws-sdk/client-sqs";
import { ppe } from "@mainframe-peru/types";
import { Enums, HttpApiError } from "@mainframe-peru/types/build/common";
import { add } from "date-fns";
import { randomUUID } from "node:crypto";
import request from "supertest";
import { influencerService } from "../../../src/influencer/service";
import { paymentProviderEventService } from "../../../src/payment-provider-event/service";
import { planService } from "../../../src/plan";
import { productService } from "../../../src/product/service";
import { recurrenceService } from "../../../src/recurrence/service";
import { sc } from "../../../src/services";
import { app } from "../../../src/sqs-handler";
import { transactionService } from "../../../src/transaction/service";
import { userService } from "../../../src/user";
import { userPaymentProviderService } from "../../../src/user-payment-provider";
import { TestData } from "../../common";
import { mockCulqiCharge, mockCulqiChargeError } from "../../mocks";

jest.mock("node-fetch");

describe("charge-yape", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await productService.create(TestData.product);
    await planService.create(TestData.plan);

    await userPaymentProviderService.create({
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      userId: TestData.user.id,
      value: "test",
    });
  });

  test("charge yape and create recurrence", async () => {
    const sqsMock = jest.fn();
    sc.sqs.send = sqsMock;

    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    // Mock charging recurrence
    await recurrenceService.create({
      id: 10 ** 6,
      userId: TestData.user.id,
      type: Enums.RecurrenceType.Values.MANUAL,
      planId: TestData.plan.id,
      renewalDate: new Date(),
      startDate: new Date(),
      status: "CREATING",
    });

    // Mock ongoing transaction
    const transaction = await transactionService.create({
      ...TestData.transaction,
      channel: "YAPE",
    });

    // Mock culqi response
    mockCulqiCharge();

    // Do request
    const requestBody: ppe.ChargeRequest = {
      productId: -1,
      source: {
        source: "YAPE",
        token: randomUUID(),
      },
      variant: {
        variant: "RECURRENCE_CREATION",
        planId: TestData.plan.id,
        transactionId: transaction.id,
      },
      amount: TestData.product.amount,
      currency: TestData.product.currency,
      userId: TestData.user.id,
      userUpdate: {
        city: "Mordor",
      },
      invoiceDestination: "PERSON",
    };
    const response = await request(app)
      .post("/payment-provider/charge")
      .send(requestBody);

    // Check http response
    expect(response.status).toBe(204);
    expect(response.body).toEqual({});

    // Check a new transaction has been created
    const storedTransactions = await transactionService.getTransactions({
      userId: TestData.user.id,
    });
    expect(storedTransactions?.length).toEqual(1);
    expect(storedTransactions?.[0]).toEqual({
      createdAt: expect.any(Date),
      amount: requestBody.amount,
      currency: requestBody.currency,
      publicId: expect.any(String),
      influencerId: TestData.influencer.id,
      id: expect.any(Number),
      userId: TestData.user.id,
      type: Enums.TransactionType.Values.RECURRENCE,
      state: Enums.TransactionState.Values.SUCCESS,
      channel: "YAPE",
    });

    // Check provider event has been created
    const providerEvents = await paymentProviderEventService.get(
      "userId",
      TestData.user.id,
    );
    expect(providerEvents?.transactionId).toEqual(transaction.id);

    const triggerDate = add(new Date(), {
      days: TestData.plan.frequency,
    });
    // Check a recurrent payment has been created
    const storedRecurrence = await recurrenceService.getActiveRecurrence(
      TestData.user.id,
    );

    expect(storedRecurrence).toEqual({
      createdAt: expect.any(Date),
      userId: TestData.user.id,
      id: expect.any(Number),
      planId: TestData.plan.id,
      type: Enums.RecurrenceType.Values.MANUAL,
      status: Enums.RecurrenceStatus.Values.ACTIVE,
      invoiceDestination: Enums.InvoiceDestination.Values.PERSON,
      renewalDate: expect.any(Date),
      startDate: expect.any(Date),
      updatedAt: expect.any(Date),
      endDate: undefined,
    });
    const normalizeDate = (date: Date) => new Date(date.setHours(0, 0, 0, 0));
    const expectedRenewalDate = normalizeDate(triggerDate);
    expect(normalizeDate(storedRecurrence?.renewalDate as Date)).toEqual(
      expectedRenewalDate,
    );

    // Expect user to be updated
    const user = await userService.get("id", TestData.user.id);
    expect(user?.city).toEqual("Mordor");

    // Expect email to be sent
    expect(snsMock).toHaveBeenCalledTimes(1);
    const command = snsMock.mock.lastCall[0] as PublishCommand;
    expect(command.input).toEqual({
      Message: expect.any(String),
      MessageAttributes: {
        service: {
          DataType: "String",
          StringValue: "email",
        },
      },
      TopicArn: sc.vars.snsTopicArn,
    });
    expect(command.input.Message).toContain(TestData.user.email);
    expect(command.input.Message).toContain(TestData.user.firstName);
    expect(command.input.Message).toContain(
      `notification@${sc.vars.env}.pchujoy.app`,
    );
    expect(command.input.Message).toContain("welcome");

    // Expect invoice creation event to be sent
    expect(sqsMock).toHaveBeenCalledTimes(1);
    const input = sqsMock.mock.calls[0][0].input as SendMessageCommandInput;

    expect(input.QueueUrl).toBe(sc.vars.fifoSqsQueueUrl);
    expect(input.MessageAttributes).toEqual({
      method: { DataType: "String", StringValue: "POST" },
      path: {
        DataType: "String",
        StringValue: "/invoice/create",
      },
      origin: { DataType: "String", StringValue: "core-backend" },
    });
    expect(input.MessageBody).toContain("PERSON");
    expect(input.MessageBody).toContain(storedTransactions[0].id.toString());
  });

  test("fail charge yape and remove recurrence", async () => {
    const sqsMock = jest.fn();
    sc.sqs.send = sqsMock;

    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    // Mock charging recurrence
    const recurrence = await recurrenceService.create({
      id: 10 ** 6,
      userId: TestData.user.id,
      type: Enums.RecurrenceType.Values.MANUAL,
      planId: TestData.plan.id,
      renewalDate: new Date(),
      startDate: new Date(),
      status: "CREATING",
    });

    // Mock ongoing transaction
    const transaction = await transactionService.create({
      ...TestData.transaction,
      channel: "YAPE",
    });

    // Mock culqi response
    mockCulqiChargeError();

    // Do request
    const requestBody: ppe.ChargeRequest = {
      productId: -1,
      source: {
        source: "YAPE",
        token: randomUUID(),
      },
      variant: {
        variant: "RECURRENCE_CREATION",
        planId: TestData.plan.id,
        transactionId: transaction.id,
      },
      amount: TestData.product.amount,
      currency: TestData.product.currency,
      userId: TestData.user.id,
      userUpdate: {
        city: "Mordor",
      },
      invoiceDestination: "PERSON",
    };
    const response = await request(app)
      .post("/payment-provider/charge")
      .send(requestBody);

    // Check http response
    expect(response.status).toBe(400);
    const errorBody: HttpApiError = response.body;
    expect(errorBody.code).toEqual("CulqiError");

    // Check a new transaction has been created
    const storedTransactions = await transactionService.getTransactions({
      userId: TestData.user.id,
    });
    expect(storedTransactions?.length).toEqual(1);
    expect(storedTransactions?.[0].state).toEqual("FAIL");

    // Check provider event has been created
    const providerEvents = await paymentProviderEventService.get(
      "userId",
      TestData.user.id,
    );
    expect(providerEvents?.transactionId).toEqual(transaction.id);
    expect(providerEvents?.state).toEqual("FAIL");

    // Check a recurrent payment has been deleted
    const storedRecurrence = await recurrenceService.get("id", recurrence.id);
    expect(storedRecurrence).toBeUndefined();
  });
});
