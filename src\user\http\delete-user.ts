import { Middleware } from "@koa/router";
import { AuthorizedContextState, Auth } from "@mainframe-peru/common-core";
import { user } from "@mainframe-peru/types";
import { userService } from "../service";

export const deleteUserEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  user.DeleteUserResponse
> = async ({ request, response }) => {
  const params = user.DeleteUserRequestSchema.parse(request.query);

  const result = await userService.delete(params.id);

  response.body = user.DeleteUserResponseSchema.parse(result);
};
