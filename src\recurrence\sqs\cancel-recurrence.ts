import { Middleware } from "@koa/router";
import { recurrence } from "@mainframe-peru/types";
import { recurrenceService } from "../../recurrence/service";

export const cancelRecurrence: Middleware<unknown, unknown, null> = async (
  ctx,
) => {
  const body = ctx.request.body as recurrence.CancelRecurrenceRequest;
  ctx.response.body = null;

  const recurrence = await recurrenceService.getActiveRecurrence(
    Number(body.userId),
  );

  if (!recurrence) return;

  await recurrenceService.update(recurrence.id, {
    status: "INACTIVE",
    endDate: new Date(),
  });
};
