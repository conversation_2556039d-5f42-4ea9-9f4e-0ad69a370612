import { formatDateLocale, formatDateTimeLocale } from "../src/helpers";

describe("helpers", () => {
  test("formatDateLocale", () => {
    for (let i = 2; i < 32; i++) {
      for (let j = 0; j < 24; j += 2) {
        const d1 = new Date(Date.UTC(2024, 11, i, j));
        const formatted = formatDateLocale(d1);
        const expectedDay = (j < 5 ? i - 1 : i).toString().padStart(2, "0");
        expect(formatted).toEqual(`${expectedDay}/12/2024`);
      }
    }
  });

  test("formatDateTimeLocale", () => {
    for (let i = 2; i < 32; i++) {
      for (let j = 0; j < 24; j += 2) {
        const d1 = new Date(Date.UTC(2024, 11, i, j));
        const formatted = formatDateTimeLocale(d1);
        const expectedDay = (j < 5 ? i - 1 : i).toString().padStart(2, "0");
        let hour = (24 + j - 5) % 24;
        hour = hour > 12 ? hour - 12 : hour;
        expect(formatted).toContain(
          `${expectedDay}/12/2024, ${hour.toString().padStart(2, "0")}:00:00`,
        );
      }
    }
  });
});
