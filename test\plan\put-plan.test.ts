import { plan } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { planService } from "../../src/plan";
import { PlanEntity } from "../../src/plan/repository";
import { productService } from "../../src/product/service";
import { TestData, officerAuthToken } from "../common";

describe("create plan tests", () => {
  test("create a plan", async () => {
    await influencerService.create(TestData.influencer);
    await productService.create(TestData.product);

    const requestBody: plan.CreatePlanRequest = {
      influencerId: TestData.influencer.id,
      name: "test 1",
      active: true,
      productId: TestData.product.id,
      frequency: 30,
    };

    const response = await request(app)
      .post("/live/plan")
      .send(requestBody)
      .set("Cookie", `session=${await officerAuthToken}`);

    const entity = (await planService.get(
      "id",
      response.body.id,
    )) as PlanEntity;

    expect(response.status).toBe(200);
    expect(entity).toEqual(
      expect.objectContaining({
        id: response.body.id,
        influencerId: requestBody.influencerId,
        name: requestBody.name,
        productId: requestBody.productId,
        createdAt: expect.any(Date),
      }),
    );
  });
});
