import { Middleware } from "@koa/router";
import { AppError } from "@mainframe-peru/common-core";
import { businessPromotion as bp } from "@mainframe-peru/types";
import { businessService } from "../../business/service";
import { businessPromotionService } from "../service";

export const getBusinessPromotionEndpoint: Middleware<
  unknown,
  unknown,
  bp.GetBusinessPromotionResponse
> = async ({ request, response }) => {
  const params = bp.GetBusinessPromotionRequestSchema.parse(request.query);
  const businessPromotion = await businessPromotionService.get("id", params.id);

  if (!businessPromotion) {
    throw new AppError({
      code: "BusinessPromotionNotFound",
      message: "Could not find business promotion",
      statusCode: "NOT_FOUND",
      logLevel: "INFO",
    });
  }

  const business = await businessService.get(
    "id",
    businessPromotion.businessId,
  );
  if (!business) {
    throw new AppError({
      code: "BusinessNotFound",
      message: "No se encontró el business solicitado",
      statusCode: "NOT_FOUND",
    });
  }

  response.set("Cache-Control", "s-maxage=1800");
  response.body = businessPromotionService.getPublic({
    business,
    promotion: businessPromotion,
  });
};
