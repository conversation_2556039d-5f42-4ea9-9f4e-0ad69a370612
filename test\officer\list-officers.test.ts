import { officer } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { officerService } from "../../src/officer";
import { officerAuthToken } from "../common";

describe("list officers tests", () => {
  test("Lists officers", async () => {
    await officerService.create({
      id: 2,
      email: "<EMAIL>",
      hash: "123123123123123123",
      firstName: "<PERSON>",
      lastName: "Tester",
      policies: {
        officer: 1,
        transaction: 3,
      },
    });
    await officerService.create({
      id: 3,
      email: "<EMAIL>",
      hash: "777773123123123123",
      firstName: "<PERSON>",
      lastName: "Tester",
      policies: {
        officer: 2,
        transaction: 7,
      },
    });

    const response = await request(app)
      .get("/live/officer/list-officers")
      .set("Cookie", `session=${await officerAuthToken}`);
    const responseBody = response.body as officer.ListOfficersResponse;

    expect(response.status).toBe(200);
    expect(responseBody.length).toBeGreaterThanOrEqual(2);
  });
});
