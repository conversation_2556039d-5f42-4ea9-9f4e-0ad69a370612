import { admin, common } from "@mainframe-peru/types";
import request from "supertest";
import { adminService } from "../../src/admin";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken, officerAuthToken } from "../common";
import { Policies, adminPoliciesConstant } from "@mainframe-peru/common-core";

describe("update admin tests", () => {
  test("update a existing admin", async () => {
    await influencerService.create(TestData.influencer);
    await adminService.create(TestData.admin);

    const updateRequest: admin.UpdateAdminRequest = {
      id: TestData.admin.id,
      email: "<EMAIL>",
      firstName: crypto.randomUUID(),
      lastName: crypto.randomUUID(),
      policies: Policies.mask(
        {
          admin: {
            DELETE_ADMIN: true,
          },
        },
        adminPoliciesConstant,
      ),
    };

    const response = await request(app)
      .put("/live/admin")
      .send(updateRequest)
      .set("Cookie", `session=${await officerAuthToken}`);

    const resBody: admin.UpdateAdminResponse = response.body;
    expect(response.statusCode).toEqual(200);

    const admin = await adminService.get("id", resBody.id);
    expect(admin).toEqual(
      expect.objectContaining({
        id: resBody.id,
        email: updateRequest.email,
        firstName: updateRequest.firstName,
        lastName: updateRequest.lastName,
        policies: updateRequest.policies,
      }),
    );
  });

  test("fail update on admin from other influencer", async () => {
    await influencerService.create({
      ...TestData.influencer,
      id: "other",
    });
    await adminService.create({
      ...TestData.admin,
      influencerId: "other",
    });

    const updateRequest: admin.UpdateAdminRequest = {
      id: TestData.admin.id,
      email: "<EMAIL>",
    };

    const response = await request(app)
      .put("/live/admin")
      .send(updateRequest)
      .set("Cookie", `session=${await adminAuthToken}`);

    const resBody: common.HttpApiError = response.body;
    expect(response.statusCode).toEqual(404);
    expect(resBody.code).toEqual("NotFound");
  });
});
