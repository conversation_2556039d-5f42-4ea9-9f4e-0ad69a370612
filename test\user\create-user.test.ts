import { user } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { userService } from "../../src/user";
import { TestData, officerAuthToken } from "../common";
import { mockReCaptchaResponse } from "../mocks";

jest.mock("node-fetch");

describe("create user tests", () => {
  test("Create a new user", async () => {
    await influencerService.create(TestData.influencer);

    const testEntity: user.CreateUserRequest = {
      influencerId: TestData.influencer.id,
      email: "  <EMAIL>   ",
      password: "1234567890",
      firstName: "Generic",
      lastName: "Name",
      phone: "928192929",
      gender: Enums.Gender.Enum.M,
      birthDate: new Date(),
      documentType: Enums.DocumentType.Enum.DNI,
      documentValue: "7777777",
    };

    // Creating the user
    const response = await request(app)
      .post("/live/user")
      .send(testEntity)
      .set("Cookie", `session=${await officerAuthToken}`);
    const responseCreation: user.CreateUserResponse = response.body;

    expect(response.statusCode).toEqual(200);

    const user = await userService.get("id", responseCreation.id);
    // Verficar que el email del usuario creado este en minusculas y sin espacios.
    expect(user?.email).toEqual("<EMAIL>");
    expect(user).toEqual(
      expect.objectContaining({
        id: responseCreation.id,
        firstName: testEntity.firstName,
        lastName: testEntity.lastName,
        phone: testEntity.phone,
        gender: testEntity.gender,
        documentType: testEntity.documentType,
        documentValue: testEntity.documentValue,
      }),
    );
  });

  test("Create a new user with reCAPTCHA", async () => {
    mockReCaptchaResponse();
    await influencerService.create(TestData.influencer);

    const testEntity: user.CreateUserRequest = {
      influencerId: TestData.influencer.id,
      email: "<EMAIL>",
      password: "1234567890",
      firstName: "Generic",
      lastName: "Name",
      phone: "928192929",
      gender: Enums.Gender.Enum.M,
      birthDate: new Date(),
      documentType: Enums.DocumentType.Enum.DNI,
      documentValue: "7777777",
      reCaptchaToken: "123123123",
    };

    // Creating the user
    const response = await request(app).post("/live/user").send(testEntity);
    const responseCreation: user.CreateUserResponse = response.body;

    expect(response.statusCode).toEqual(200);

    const user = await userService.get("id", responseCreation.id);

    expect(user).toEqual(
      expect.objectContaining({
        id: responseCreation.id,
        firstName: testEntity.firstName,
        lastName: testEntity.lastName,
        phone: testEntity.phone,
        gender: testEntity.gender,
        documentType: testEntity.documentType,
        documentValue: testEntity.documentValue,
      }),
    );
  });
});
