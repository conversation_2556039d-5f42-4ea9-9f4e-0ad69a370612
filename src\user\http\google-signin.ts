import { Middleware } from "@koa/router";
import { AppError, getJWTSetCookieHeader } from "@mainframe-peru/common-core";
import { user } from "@mainframe-peru/types";
import { sc } from "../../services";
import { userService } from "../service";
import { recurrenceService } from "../../recurrence";
import { transformNullToUndefined } from "../../helpers";

export const googleSignInEndpoint: Middleware<
  unknown,
  unknown,
  user.LoginUserResponse
> = async ({ request, response }) => {
  const body = user.UserGoogleSignInSchema.parse(request.body);

  const entity = await userService.authenticateGoogleUser(
    body.influencerId,
    body.idToken,
  );
  if (!entity) {
    throw new AppError({
      logLevel: "NONE",
      code: "FailedLogin",
      message: "Usuario no válido",
      statusCode: "UNAUTHORIZED",
    });
  }

  const activeRecurrence = await recurrenceService.getActiveRecurrence(
    entity.id,
  );

  response.set(
    "Set-Cookie",
    await getJWTSetCookieHeader({
      type: "user",
      duration: 2 * 60,
      key: sc.vars.keys.user.private,
      payload: {
        id: entity.id,
        influencerId: body.influencerId,
        email: entity.email,
        firstName: entity.firstName,
        lastName: entity.lastName,
        policies: entity.policies as Record<string, number>,
      },
    }),
  );

  response.body = user.LoginUserResponseSchema.parse({
    ...transformNullToUndefined(entity),
    activeRecurrence: !!activeRecurrence,
  });
};
