import { admin as adminT } from "@mainframe-peru/types";
import request from "supertest";
import { adminService } from "../../src/admin";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer";
import { TestData, officerAuthToken } from "../common";

describe("delete admin tests", () => {
  test("deletes an existing admin by id", async () => {
    await influencerService.create(TestData.influencer);
    const admin = await adminService.create({
      email: "<EMAIL>",
      influencerId: TestData.influencer.id,
    });

    const queryParams: adminT.DeleteAdminRequest = {
      id: admin.id,
    };

    const response = await request(app)
      .delete("/live/admin")
      .query(queryParams)
      .set("Cookie", `session=${await officerAuthToken}`);

    expect(response.status).toBe(200);

    const admin2 = await adminService.get("id", admin.id);

    expect(admin2).toBeUndefined();
  });
});
