import { Middleware } from "@koa/router";
import { AppError, AuthOfficer } from "@mainframe-peru/common-core";
import { influencer } from "@mainframe-peru/types";
import { influencerService } from "../service";
import { InfluencerEntity } from "../repository";

type AuthorizedContextState = {
  auth: AuthOfficer | undefined;
};

export const listInfluencer: Middleware<
  AuthorizedContextState,
  unknown,
  influencer.ListInfluencerResponse | influencer.PublicListInfluencerResponse
> = async ({ response, state }) => {
  const entities = await influencerService.list();

  if (!entities) {
    throw new AppError({
      code: "NoItems",
      message: "No se encontraron influencers.",
      statusCode: "NOT_FOUND",
    });
  }

  const items = entities as InfluencerEntity[];

  if (state.auth) {
    response.body = influencer.ListInfluencerResponseSchema.parse(items);
  } else {
    response.body = influencer.PublicListInfluencerResponseSchema.parse(
      items.map((i) => ({
        id: i.id,
        name: i.name,
      })) || [],
    );
  }
};
