import { TransactionEntity } from "../../transaction/repository";
import { UserEntity } from "../../user/repository";
import { InvoiceDestination } from "@mainframe-peru/types/build/common";
import { formatDateLocale } from "../../helpers";
import { transactionDetailService } from "../../transaction-detail/service";

export type Voucher = {
  operacion: string;
  tipo_de_comprobante: number;
  serie: string;
  numero: number;
  codigo_unico: string;
  sunat_transaction: number;
  cliente_tipo_de_documento: number;
  cliente_numero_de_documento: string;
  cliente_denominacion: string;
  cliente_direccion: string;
  cliente_email: string;
  fecha_de_emision: string;
  tipo_de_cambio?: string;
  porcentaje_de_igv: number;
  descuento_global: string;
  total: string;
  detraccion: boolean;
  enviar_automaticamente_a_la_sunat: boolean;
  enviar_automaticamente_al_cliente: boolean;
  moneda?: number;
  total_inafecta?: string;
  total_gravada?: number;
  total_igv?: number;
  items: VoucherItem[];
};

export type VoucherItem = {
  valor_unitario: number;
  precio_unitario: number;
  subtotal: number;
  tipo_de_igv: number;
  igv: number;
  total: number;
  anticipo_regularizacion: boolean;
  anticipo_documento_serie: string;
  anticipo_documento_numero: string;
};

const dollarChange = 3.78;
const igvPercent = 18.0;

export class VoucherFactory {
  static async createVoucher(
    transaction: TransactionEntity,
    user: UserEntity,
    invoiceDestination: InvoiceDestination,
  ): Promise<Voucher> {
    const client = this.getClientDetail(
      transaction,
      user,
      invoiceDestination === "COMPANY"
        ? user.companyId || undefined
        : undefined,
    );
    const specificDataByCurrency = this.getSpecificDataByCurrency(transaction);
    const items = await this.getVoucherItems(transaction);

    return {
      operacion: "generar_comprobante",
      tipo_de_comprobante: invoiceDestination == "PERSON" ? 2 : 1,
      serie: invoiceDestination == "PERSON" ? "BBB8" : "FFF8", // TODO: Pull from .env?
      numero: 0,
      codigo_unico: crypto.randomUUID(),
      sunat_transaction: 1,
      cliente_tipo_de_documento: client.cliente_tipo_de_documento,
      cliente_numero_de_documento: client.cliente_numero_de_documento,
      cliente_denominacion: client.cliente_denominacion,
      cliente_direccion: client.cliente_direccion,
      cliente_email: client.cliente_email,
      fecha_de_emision: formatDateLocale(new Date()),
      porcentaje_de_igv: igvPercent,
      descuento_global: "",
      total: transaction.amount,
      detraccion: false,
      enviar_automaticamente_a_la_sunat: false,
      enviar_automaticamente_al_cliente: false,
      ...specificDataByCurrency,
      items,
    };
  }

  private static getClientDetail(
    transaction: TransactionEntity,
    user: UserEntity,
    companyId?: string,
  ) {
    let clienteDenominacion = `${user.firstName} ${user.lastName}`.trim();
    let typeDocument = 1;
    let numDocument = user.documentValue || "";

    if (companyId) {
      typeDocument = 6;
      numDocument = companyId;
    } else {
      if (
        (numDocument === "00000000" && user.documentType === "DNI") ||
        numDocument.length > 15 ||
        (numDocument.length !== 8 && user.documentType === "DNI")
      ) {
        clienteDenominacion = "público general";
        typeDocument = 0;
        numDocument = "00000000";
      }

      if (user.country !== "PE" || user.documentType !== "DNI") {
        typeDocument = 0;
      }
    }

    if (transaction.currency === "USD") {
      typeDocument = 0;
    }

    return {
      cliente_tipo_de_documento: typeDocument,
      cliente_numero_de_documento: numDocument,
      cliente_denominacion: clienteDenominacion,
      cliente_direccion: user.line1 || "",
      cliente_email: user.email,
    };
  }

  private static getSpecificDataByCurrency(transaction: TransactionEntity) {
    let data = {};

    if (transaction.currency === "PEN") {
      data = {
        moneda: 1,
        sunat_transaction: 1,
        tipo_de_cambio: "",
        total_inafecta: "",
        total_gravada: this.calculateBasePrice(Number(transaction.amount)),
        total_igv: this.calculateIGV(Number(transaction.amount)),
      };
    }

    if (transaction.currency === "USD") {
      data = {
        moneda: 2,
        sunat_transaction: 2,
        tipo_de_cambio: dollarChange,
        total_inafecta: transaction.amount,
        total_gravada: "",
        total_igv: "",
      };
    }

    return data;
  }

  private static async getVoucherItems(t: TransactionEntity) {
    const transactionDetails = await transactionDetailService.listWithProducts(
      t.id,
    );

    const dataItems = [];
    for (const { product, transaction_detail: td } of transactionDetails) {
      const unitPrice = Number(td.amount) / td.quantity;
      const priceBase = this.calculateBasePrice(unitPrice);
      const priceIgv = this.calculateIGV(unitPrice);

      const item = {
        unidad_de_medida: "ZZ",
        codigo: product.sunatCode, // TODO: Pull from database (SUNAT table)
        codigo_producto_sunat: "90150000",
        descripcion: "Servicio",
        cantidad: td.quantity,
        anticipo_regularizacion: false,
        anticipo_documento_serie: "",
        anticipo_documento_numero: "",
        ...(t.currency === "USD"
          ? {
              valor_unitario: unitPrice,
              precio_unitario: unitPrice,
              subtotal: unitPrice * td.quantity,
              igv: 0,
              total: Number(td.amount),
              tipo_de_igv: 16,
            }
          : {
              valor_unitario: priceBase,
              precio_unitario: unitPrice,
              subtotal: priceBase * td.quantity,
              igv: priceIgv,
              total: Number(td.amount),
              tipo_de_igv: 1,
            }),
      };

      dataItems.push(item);
    }

    return dataItems;
  }

  private static calculateBasePrice(amount: number): number {
    const divisor = 1 + igvPercent / 100;
    return this.roundToTwoDecimals(Number(amount) / divisor);
  }

  private static calculateIGV(amount: number): number {
    const basePrice = this.calculateBasePrice(amount);
    return this.roundToTwoDecimals(basePrice * (igvPercent / 100));
  }

  private static roundToTwoDecimals(value: number): number {
    return Math.round(value * 100) / 100;
  }
}
