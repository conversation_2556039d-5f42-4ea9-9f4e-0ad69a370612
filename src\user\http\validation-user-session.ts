import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";

export const validateUserSessionEndpoint: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  unknown
> = async ({ state, response }) => {
  if (state.auth.iss != "mainframe:user") {
    throw new AppError({
      code: "InvalidSession",
      message: "La sesión solicitada no es válida",
      statusCode: "FORBIDDEN",
    });
  }
  response.status = 200;
};
