import { Policies, createJWT } from "@mainframe-peru/common-core";
import { userPoliciesConstant } from "@mainframe-peru/common-core/build/policies/lib/modules/user";
import { common, complaint as comp } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import { AdminEntity } from "../src/admin/repository";
import { CardEntity } from "../src/card/repository";
import { InfluencerEntity } from "../src/influencer/repository";
import { InvoiceEntity } from "../src/invoice/repository";
import { OfficerEntity } from "../src/officer/repository";
import { PaymentProviderEventEntity } from "../src/payment-provider-event/repository";
import { PlanEntity } from "../src/plan/repository";
import { RecurrenceEntity } from "../src/recurrence/repository";
import { sc } from "../src/services";
import { TransactionDetailEntity } from "../src/transaction-detail/repository";
import { TransactionEntity } from "../src/transaction/repository";
import { UserEntity } from "../src/user/repository";
import { EventEntity } from "../src/event/repository";
import { BusinessEntity } from "../src/business/repository";
import { BusinessPromotionEntity } from "../src/business-promotion/repository";
import { BusinessPromotionCodeEntity } from "../src/business-promotion-code/repository";
import { UserEventParticipationEntity } from "../src/user-event-participation/repository";
import { ProductEntity } from "../src/product/repository";

const officer: Readonly<OfficerEntity> = {
  id: 4352617,
  email: "<EMAIL>",
  firstName: "officer first",
  lastName: "officer last",
  hash: "",
  createdAt: new Date(),
  policies: {},
};

const influencer: Readonly<InfluencerEntity> = {
  id: "PchujoyTest",
  name: "Bizz name",
  status: common.Enums.InfluencerStatus.Enum.ACTIVE,
  logoUrl: "www",
  transientUsers: false,
  domain: "fcc.pchujoy.app",
  createdAt: new Date(),
  updatedAt: new Date(),
  attributes: null,
  emailsConfiguration: {
    templates: {
      contact: {
        type: "contact",
        subject: "Contacto desde Pchujoy",
        from: "<EMAIL>",
        cc: "<EMAIL>",
        bcc: "<EMAIL>",
        template: "contact",
      },
    },
  },
  providersConfiguration: {
    GOOGLE: {
      name: "Google",
      apiKey: "eyx",
    },
    CULQI: {
      name: "Culqi",
      apiKey: "eyx",
    },
  },
};

const admin: Readonly<AdminEntity> = {
  id: 5843902,
  firstName: "Admin First",
  lastName: "Admin Last",
  updatedAt: new Date(),
  createdAt: new Date(),
  email: "<EMAIL>",
  hash: "",
  influencerId: influencer.id,
  policies: {},
};

const user: Readonly<UserEntity> = {
  id: *********,
  influencerId: influencer.id,
  authenticationType: "EMAIL",
  email: "<EMAIL>",
  firstName: "Sebastián",
  lastName: "Muñoz",
  alias: "OptimistaLima7",
  phone: "*********",
  gender: Enums.Gender.Enum.F,
  createdAt: new Date(),
  updatedAt: new Date(),
  policies: {},
  documentType: Enums.DocumentType.Enum.DNI,
  documentValue: "12345678",
  city: "Lima",
  country: Enums.Country.Enum.PE,
  line1: "test address 1",
  province: "Lima",
  birthDate: new Date(),
  companyId: "*********",
  district: "",
  hash: "",
  line2: "",
  zipCode: "123123",
  attributes: null,
  isPartner: false,
  privacyConfiguration: {
    privateProfile: false,
    hideFace: false,
    allowNews: false,
    allowNotifications: false,
  },
};

const product: Readonly<ProductEntity> = {
  id: 555555,
  amount: "1.00",
  currency: "PEN",
  description: "Extra chance para un evento",
  sunatCode: "PMC-111",
  influencerId: influencer.id,
};

const plan: Readonly<PlanEntity> = {
  id: 8769302,
  influencerId: influencer.id,
  productId: product.id,
  frequency: 30,
  name: "INITIAL PLAN",
  active: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

const card: Readonly<CardEntity> = {
  id: 1982357,
  userId: user.id,
  brand: "Visa",
  default: true,
  number: "4111...1111",
  paymentProvider: Enums.PaymentProvider.Enum.CULQI,
  token: crypto.randomUUID(),
  createdAt: new Date(),
  updatedAt: new Date(),
};

const recurrence: Readonly<RecurrenceEntity> = {
  id: 987612,
  planId: plan.id,
  userId: user.id,
  renewalDate: new Date(2030, 0, 0),
  startDate: new Date(),
  status: Enums.RecurrenceStatus.Enum.ACTIVE,
  invoiceDestination: "PERSON",
  type: Enums.RecurrenceType.Enum.CARD,
  createdAt: new Date(),
  endDate: null,
  updatedAt: new Date(),
};

const transaction: Readonly<TransactionEntity> = {
  id: 8971234,
  publicId: crypto.randomUUID(),
  influencerId: influencer.id,
  userId: user.id,
  state: Enums.TransactionState.Enum.PROCESSING,
  amount: product.amount,
  currency: product.currency,
  type: Enums.TransactionType.Enum.RECURRENCE,
  lastPaymentProviderEventId: 12382183,
  createdAt: new Date(),
  channel: "CARD",
};

const transactionDetail: Readonly<TransactionDetailEntity> = {
  amount: transaction.amount,
  entityId: recurrence.id,
  id: 7008321,
  quantity: 1,
  transactionId: transaction.id,
  productId: product.id,
};

const paymentProviderEvent: Readonly<PaymentProviderEventEntity> = {
  id: 12382183,
  provider: "CULQI",
  externalId: "chr_gggggg",
  responseMessage: "Invalid charge",
  transactionId: transaction.id,
  external: {},
  state: "FAIL",
  type: "CHARGE_CREATION",
  userId: user.id,
  createdAt: new Date(),
};

const invoice: Readonly<InvoiceEntity> = {
  id: 123123,
  transactionId: transaction.id,
  userId: user.id,
  invoiceOrigin: 1,
  voucherType: 1,
  serie: "BBB1",
  number: 1,
  link: "http://example.com",
  acceptedBySunat: 1,
  sunatDescription: "Accepted",
  sunatNote: "Note",
  sunatResponseCode: "0",
  sunatSoapError: "None",
  stringQrCode: "CDR",
  hashCode: "123123",
  payload: {},
  createdAt: new Date(),
  updatedAt: new Date(),
};

const complaint: Readonly<comp.Complaint> = {
  id: 123123,
  influencerId: influencer.id,
  subject: "Some subject",
  description: "Some description",
  email: "<EMAIL>",
  firstName: "Jhon",
  lastName: "Doe",
  documentType: "DNI",
  documentValue: "77464582",
  phone: "902230372",
  ageCategory: "ADULT",
  type: "COMPLAINT",
  status: "OPENED",
};

const event: Readonly<EventEntity> = {
  id: 12312312,
  influencerId: influencer.id,
  name: "First event",
  imageUrl:
    "https://core-backend-s3moduleassetsbucket-4hbydyax4vsz.s3.us-east-1.amazonaws.com/events/d3be9958-b6eb-4cbc-93eb-c04178dd9f23.jpg",
  description: "Evento único y diferente",
  type: "PRIZE",
  status: "ACTIVE",
  startDate: new Date(),
  eventDate: new Date(2050, 0, 0),
  endDate: new Date(2100, 0, 0),
  participationProductId: product.id,
  createdAt: new Date(),
  updatedAt: new Date(),
  participationForm: [
    {
      id: "dni",
      text: "DNI",
      type: "TEXT",
    },
  ],
  eventListUrl: null,
  location: "Lima",
  priority: null,
  inviteLimit: null,
  inviteForm: null,
};

const business: Readonly<BusinessEntity> = {
  id: 999912313,
  name: "trest",
  influencerId: influencer.id,
  imageUrl: crypto.randomUUID(),
  description: "description",
  ruc: "123456789",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const businessPromotion: Readonly<BusinessPromotionEntity> = {
  id: 999991231,
  influencerId: influencer.id,
  status: "ACTIVE",
  businessId: business.id,
  content: crypto.randomUUID(),
  expirationDate: new Date(),
  name: "name",
  type: "PERSONAL",
  value: "value",
  description: "some description",
  priority: null,
  imageUrl: "http://www.google.com/image.png",
  category: "ELECTRONICS",
  group: "HIGHLIGHTED",
  termsAndConditions: "test",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const businessPromotionCode: Readonly<BusinessPromotionCodeEntity> = {
  id: *********,
  promotionId: businessPromotion.id,
  userId: user.id,
  code: crypto.randomUUID(),
  createdAt: new Date(),
};

const userEventParticipation: Readonly<UserEventParticipationEntity> = {
  userId: user.id,
  eventId: event.id,
  quantity: 1,
  participationFormValues: null,
};

export const TestData = {
  influencer,
  admin,
  officer,
  user,
  plan,
  card,
  recurrence,
  transaction,
  transactionDetail,
  paymentProviderEvent,
  invoice,
  complaint,
  event,
  business,
  businessPromotion,
  businessPromotionCode,
  userEventParticipation,
  product,
} as const;

export const officerAuthToken = createJWT(
  "officer",
  {
    id: TestData.officer.id,
    email: TestData.officer.email,
    firstName: TestData.officer.firstName || "",
    lastName: TestData.officer.firstName || "",
    policies: {
      plan: 15,
      card: 15,
      officer: 15,
      user: 15,
      admin: 15,
      influencer: 15,
      transaction: 15,
      payment: 15,
      recurrence: 15,
      complaint: 15,
      event: 31,
    },
  },
  sc.vars.keys.officer.private,
  Math.floor(Date.now() / 1000) + 60, // expires in 60 seconds
);

export const adminAuthToken = createJWT(
  "admin",
  {
    id: TestData.admin.id,
    influencerId: TestData.influencer.id,
    email: TestData.admin.email,
    firstName: TestData.admin.firstName || "",
    lastName: TestData.admin.firstName || "",
    policies: {
      plan: 15,
      card: 15,
      user: 15,
      admin: 15,
      transaction: 15,
      influencer: 15,
      payment: 15,
      recurrence: 15,
      complaint: 15,
      invoice: 3,
      paymentProviderEvent: 15,
      event: 31,
      business: 15,
      businessPromotion: 15,
      businessPromotionCode: 15,
      attributeValue: 15,
    },
  },
  sc.vars.keys.admin.private,
  Math.floor(Date.now() / 1000) + 60, // expires in 60 seconds
);

export const userAuthToken = createJWT(
  "user",
  {
    id: TestData.user.id,
    influencerId: TestData.influencer.id,
    email: TestData.user.email,
    firstName: TestData.user.firstName || "",
    lastName: TestData.user.lastName || "",
    policies: Policies.mask(
      {
        general: {
          REGULAR: true,
          TRANSIENT: true,
        },
      },
      userPoliciesConstant,
    ),
  },
  sc.vars.keys.user.private,
  Math.floor(Date.now() / 1000) + 60, // expires in 60 seconds
);

export const transientUserAuthToken = createJWT(
  "user",
  {
    id: TestData.user.id,
    influencerId: TestData.influencer.id,
    email: TestData.user.email,
    firstName: TestData.user.firstName || "",
    lastName: TestData.user.lastName || "",
    policies: Policies.mask(
      {
        general: {
          TRANSIENT: true,
        },
      },
      userPoliciesConstant,
    ),
  },
  sc.vars.keys.user.private,
  Math.floor(Date.now() / 1000) + 60, // expires in 60 seconds
);
