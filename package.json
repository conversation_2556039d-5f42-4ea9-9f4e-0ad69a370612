{"name": "core-backend", "version": "1.0.0", "description": "", "main": "index.js", "repository": "https://github.com/Mainframe-Peru/core-backend", "scripts": {"build": "tsc", "format": "prettier . --check", "formatfix": "prettier . --write --log-level warn", "lint": "eslint src", "lintfix": "npm run lint -- --fix", "test": "jest --force<PERSON>xit", "clear_jest": "jest --clear<PERSON>ache", "prepare": "husky"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@babel/preset-typescript": "^7.24.7", "@eslint/js": "^9.11.1", "@types/jest": "^29.5.13", "@types/koa": "^2.15.0", "@types/koa__multer": "^2.0.7", "@types/koa__router": "^12.0.4", "@types/node-fetch": "^2.6.11", "@types/pg": "^8.11.10", "@types/supertest": "^6.0.2", "aws-sdk-client-mock": "^4.1.0", "aws-sdk-client-mock-jest": "^4.1.0", "babel-jest": "^29.7.0", "cloudformation-js-yaml-schema": "^0.4.2", "drizzle-kit": "^0.30.4", "eslint": "^9.11.1", "globals": "^15.9.0", "husky": "^9.1.6", "jest": "^29.7.0", "pg-transactional-tests": "^1.2.0", "prettier": "3.3.3", "supertest": "^7.0.0", "typescript": "^5.6.2", "typescript-eslint": "^8.7.0"}, "dependencies": {"@aws-sdk/client-cloudfront": "^3.777.0", "@aws-sdk/client-s3": "^3.717.0", "@aws-sdk/client-scheduler": "^3.830.0", "@aws-sdk/client-secrets-manager": "^3.758.0", "@aws-sdk/client-sns": "^3.693.0", "@aws-sdk/client-sqs": "^3.658.1", "@aws-sdk/lib-storage": "^3.830.0", "@codegenie/serverless-express": "^4.15.0", "@faker-js/faker": "^9.2.0", "@koa/bodyparser": "^5.1.1", "@koa/multer": "^3.0.2", "@koa/router": "^13.1.0", "@mainframe-peru/common-core": "^1.25.4", "@mainframe-peru/types": "^1.81.0", "bcrypt": "^5.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.39.2", "fast-csv": "^5.0.2", "google-auth-library": "^9.15.1", "koa": "^2.15.3", "multer": "^1.4.5-lts.1", "node-fetch": "^2.7.0", "pg": "^8.13.0", "sharp": "^0.34.2", "zod": "^3.23.8"}}