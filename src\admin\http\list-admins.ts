import { Middleware } from "@koa/router";
import { admin } from "@mainframe-peru/types";
import {
  AuthAdmin,
  AuthOfficer,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { adminService } from "../service";
import { ListAdminsRequestSchema } from "@mainframe-peru/types/build/admin";
import { restrictParams } from "../../common";

export const listAdmins: Middleware<
  AuthorizedContextState<AuthOfficer | AuthAdmin>,
  unknown,
  admin.ListAdminsResponse
> = async ({ request, response, state }) => {
  const query = request.query
    ? ListAdminsRequestSchema.parse(request.query)
    : {};

  restrictParams(state.auth, query);

  const entities = await adminService.getAdmins(query);

  response.body = admin.ListAdminsResponseSchema.parse(entities);
};
