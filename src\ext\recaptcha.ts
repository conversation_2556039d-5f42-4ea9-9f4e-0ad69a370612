import fetch, { Response } from "node-fetch";
import { sc } from "../services";
import { AppError } from "@mainframe-peru/common-core";

export type ReCaptchaResponseBody = {
  success: boolean;
  challenge_ts: string; // timestamp of the challenge load (ISO format yyyy-MM-dd'T'HH:mm:ssZZ)
  hostname: string; // the hostname of the site where the reCAPTCHA was solved
  "error-codes"?: (
    | "missing-input-secret" // The secret parameter is missing.
    | "invalid-input-secret" // The secret parameter is invalid or malformed.
    | "missing-input-response" // The response parameter is missing.
    | "invalid-input-response" // The response parameter is invalid or malformed.
    | "bad-request" // The request is invalid or malformed.
    | "timeout-or-duplicate" // The response is no longer valid: either is too old or has been used previously.
  )[];
};

type OkResponse = {
  ok: true;
  json: () => Promise<ReCaptchaResponseBody>;
};

type ErrorResponse = {
  ok: false;
  json(): Promise<unknown>;
};

type AppResponse = (OkResponse | ErrorResponse) & Response;

class ReCaptchaApi {
  async validate(token: string, ip?: string): Promise<void> {
    const params = new URLSearchParams();
    params.append("secret", sc.vars.reCaptchaKey);
    params.append("response", token);
    if (ip) params.append("remoteip", ip);

    const reCaptchaValidation: AppResponse = await fetch(
      "https://www.google.com/recaptcha/api/siteverify",
      {
        method: "POST",
        body: params,
      },
    );

    if (reCaptchaValidation.ok) {
      const reCaptchaBody = await reCaptchaValidation.json();
      if (!reCaptchaBody.success) {
        throw new AppError({
          code: "ReCaptchValidationFailed",
          message: "La validación captch falló",
          statusCode: "BAD_REQUEST",
          logLevel: "INFO",
          data: reCaptchaBody,
        });
      }
    } else {
      throw new AppError({
        code: "NoReCaptchaValidation",
        message: "No se pudo realizar la validación con reCAPTCHA",
        statusCode: "INTERNAL_SERVER_ERROR",
        logLevel: "ERROR",
      });
    }
  }
}

export const reCaptchaApi = new ReCaptchaApi();
