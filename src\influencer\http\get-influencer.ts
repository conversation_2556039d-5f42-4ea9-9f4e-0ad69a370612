import { Middleware } from "@koa/router";
import { AppError, Auth } from "@mainframe-peru/common-core";
import { influencer } from "@mainframe-peru/types";
import { influencerService } from "../service";

type AuthorizedContextState = {
  auth: Auth | undefined;
};

export const getInfluencer: Middleware<
  AuthorizedContextState,
  unknown,
  influencer.GetInfluencerResponse | influencer.PublicGetInfluencerResponse
> = async ({ request, response, state }) => {
  const params = influencer.GetInfluencerRequestSchema.parse(request.query);
  const entity = await influencerService.get("id", params.id);

  if (!entity) {
    throw new AppError({
      code: "InfluencerNotFound",
      message: "No se encontró al influencer solicitado",
      statusCode: "NOT_FOUND",
    });
  }

  if (state.auth) {
    response.body = influencer.GetInfluencerResponseSchema.parse(entity);
  } else {
    response.body = influencer.PublicGetInfluencerResponseSchema.parse(entity);
    response.set("Cache-Control", "s-maxage=1800");
  }
  // Setting providers
  response.body.googleToken = entity.providersConfiguration?.GOOGLE?.apiKey;
};
