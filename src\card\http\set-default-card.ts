import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { card } from "@mainframe-peru/types";
import { cardService } from "../service";

export const setDefaultCard: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  card.SetDefaultCardResponse
> = async ({ response, request, state }) => {
  const body = card.SetDefaultCardRequestSchema.parse(request.body);

  const cards = await cardService.listByUserId(Number(state.auth.id));
  const current = cards.find((c) => c.default);
  if (!current) {
    throw new AppError({
      code: "CardNotFound",
      message: "No se pudo encontrar la tarjeta indicada",
      statusCode: "NOT_FOUND",
      logLevel: "INFO",
    });
  }
  if (current.id === body.id) {
    throw new AppError({
      code: "CardIsDefault",
      message: "La tarjeta indicada ya es la predeterminada",
      statusCode: "BAD_REQUEST",
      logLevel: "INFO",
    });
  }

  await cardService.update(current.id, {
    default: false,
  });
  const c = await cardService.update(body.id, {
    default: true,
  });
  response.body = cardService.toPublic(c);
};
