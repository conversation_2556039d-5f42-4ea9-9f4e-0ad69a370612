import {
  CreateScheduleCommand,
  UpdateScheduleCommand,
} from "@aws-sdk/client-scheduler";
import { event } from "@mainframe-peru/types";
import { EventScheduleInput } from "../schedule-handler";
import { ServiceBase } from "../service-base";
import { sc } from "../services";
import { EventEntity, eventRepository } from "./repository";

class EventService extends ServiceBase<typeof eventRepository, EventEntity> {
  constructor() {
    super(eventRepository);
  }

  async getEvents(filters: event.ListEventsRequest): Promise<EventEntity[]> {
    return await this.repository.findEvents(filters);
  }

  async createOrUpdateEventListGenerationSchedule(
    event: EventEntity,
    operation: "create" | "update",
  ): Promise<void> {
    const input: EventScheduleInput = {
      type: "event-list-generation",
      eventId: event.id,
      influencerId: event.influencerId,
    };
    const Command =
      operation === "create" ? CreateScheduleCommand : UpdateScheduleCommand;
    const command = new Command({
      Name: `event-${event.id}`,
      ScheduleExpression: `at(${event.eventDate?.toISOString().slice(0, 19)})`,
      ScheduleExpressionTimezone: "UTC",
      FlexibleTimeWindow: {
        Mode: "OFF",
      },
      Target: {
        Arn: sc.vars.schedulerTargetArn,
        RoleArn: sc.vars.schedulerTargetRoleArn,
        Input: JSON.stringify(input),
        RetryPolicy: {
          MaximumRetryAttempts: 3,
        },
      },
      ActionAfterCompletion: "DELETE",
    });
    await sc.scheduler.send(command);
  }
}

export const eventService = new EventService();
