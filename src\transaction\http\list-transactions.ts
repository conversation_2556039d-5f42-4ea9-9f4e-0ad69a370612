import { Middleware } from "@koa/router";
import {
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { transaction as transactionT } from "@mainframe-peru/types";
import { transactionService } from "../service";
import { restrictParams, setCsvResponse } from "../../common";
import { formatDateTimeLocale } from "../../helpers";

export const listTransactions: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  transactionT.ListTransactionsResponse
> = async ({ request, response, state }) => {
  const query = request.query
    ? transactionT.ListTransactionsRequestSchema.parse(request.query)
    : {};
  restrictParams(state.auth, query);

  const transactions = await transactionService.getTransactions(query);

  if (request.accepts("text/csv")) {
    const publicResult = transactions.map((t) => ({
      id: t.id,
      publicId: t.publicId,
      amount: t.amount,
      state: t.state,
      currency: t.currency,
      createdAt: formatDateTimeLocale(t.createdAt),
      type: t.type,
      channel: t.channel,
      email: t.email,
      firstName: t.firstName,
      lastName: t.lastName,
      documentType: t.documentType,
      documentValue: t.documentValue,
      phone: t.phone,
      country: t.country,
      city: t.city,
      province: t.province,
      district: t.district,
      zipCode: t.zipCode,
      line1: t.line1,
    }));
    setCsvResponse(publicResult, response, "transactions");
    return;
  }

  response.body = {
    items: transactions.map((t) => ({
      id: t.id,
      publicId: t.publicId,
      amount: t.amount,
      state: t.state,
      currency: t.currency,
      createdAt: t.createdAt as Date,
      type: t.type,
      channel: t.channel,
      user: query.detailed
        ? {
            email: t.email || "",
            firstName: t.firstName || "",
            lastName: t.lastName || "",
            documentType: t.documentType,
            documentValue: t.documentValue,
            phone: t.phone,
            country: t.country,
            city: t.city,
            province: t.province,
            district: t.district,
            zipCode: t.zipCode,
            line1: t.line1,
          }
        : undefined,
    })),
  };
};
