import { businessPromotion as bp } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { businessPromotionService } from "../../src/business-promotion";
import { influencerService } from "../../src/influencer";
import { TestData } from "../common";

describe("get business promotion tests", () => {
  test("get a business promotion", async () => {
    await influencerService.create(TestData.influencer);
    const business = await businessService.create(TestData.business);
    const promotion = await businessPromotionService.create(
      TestData.businessPromotion,
    );

    const query: bp.GetBusinessPromotionRequest = {
      id: promotion.id,
    };

    const response = await request(app)
      .get("/live/business-promotion")
      .query(query);

    const responseBody: bp.GetBusinessPromotionResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody).toEqual({
      id: response.body.id,
      business: {
        id: business.id,
        description: business.description,
        imageUrl: business.imageUrl,
        name: business.name,
      },
      content: promotion.content,
      createdAt: expect.any(String),
      description: promotion.description,
      expirationDate: expect.any(String),
      name: promotion.name,
      type: promotion.type,
      updatedAt: expect.any(String),
      value: promotion.value,
      status: "ACTIVE",
      imageUrl: promotion.imageUrl,
      termsAndConditions: promotion.termsAndConditions,
    });
  });
});
