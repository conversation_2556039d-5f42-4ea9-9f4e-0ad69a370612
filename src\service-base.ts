import { RepositoryBase } from "./repository-base";

export abstract class ServiceBase<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  R extends RepositoryBase<any, any, Entity>,
  Entity extends Record<string, unknown>,
> {
  protected repository: R;

  constructor(repository: R) {
    this.repository = repository;
  }

  async get(
    field: keyof Required<Entity>,
    value: string | number,
  ): Promise<Entity | undefined> {
    return await this.repository.find(field as string, value);
  }

  async list(
    filter?: Parameters<R["list"]>[0],
    page?: Parameters<R["list"]>[1],
  ): Promise<Entity[]> {
    return await this.repository.list(filter, page);
  }

  async create(data: Parameters<R["create"]>[0]): Promise<Entity> {
    return await this.repository.create(data);
  }

  async update(id: number | string, data: Partial<Entity>): Promise<Entity> {
    return await this.repository.update(id, data);
  }

  async delete(id: number | string): Promise<number | string> {
    return await this.repository.delete(id);
  }
}
