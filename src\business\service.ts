import { ServiceBase } from "../service-base";
import { BusinessEntity, businessRepository } from "./repository";

class BusinessService extends ServiceBase<
  typeof businessRepository,
  BusinessEntity
> {
  constructor() {
    super(businessRepository);
  }

  async findBusiness(
    data: Parameters<(typeof businessRepository)["findBusiness"]>[0],
  ): Promise<BusinessEntity[]> {
    return await this.repository.findBusiness(data);
  }
}

export const businessService = new BusinessService();
