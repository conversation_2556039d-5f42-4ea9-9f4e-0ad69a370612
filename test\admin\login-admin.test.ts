import { admin } from "@mainframe-peru/types";
import request from "supertest";
import { adminService } from "../../src/admin";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { TestData } from "../common";

describe("login admin tests", () => {
  test("successfully login an admin", async () => {
    // Initial setup
    const password = "genericpassword";
    await influencerService.create(TestData.influencer);

    const testEntity = await adminService.processCreation({
      influencerId: TestData.influencer.id,
      email: "<EMAIL>",
      password: password,
      firstName: "John",
      lastName: "Tester",
      policies: {
        influencer: 15,
        admin: 15,
        transaction: 15,
      },
    });

    const body: admin.LoginAdminRequest = {
      influencerId: TestData.influencer.id,
      email: testEntity.email as string,
      password: password,
    };

    const response = await request(app).post("/live/admin/login").send(body);

    expect(response.statusCode).toEqual(200);
  });

  test("unsuccessfully login an admin", async () => {
    // Initial setup
    const password = "genericpassword";
    await influencerService.create(TestData.influencer);

    const testEntity = await adminService.processCreation({
      influencerId: TestData.influencer.id,
      email: "<EMAIL>",
      password: password,
      firstName: "John",
      lastName: "Tester",
      policies: {
        influencer: 15,
        admin: 15,
        transaction: 15,
      },
    });

    const body: admin.LoginAdminRequest = {
      influencerId: TestData.influencer.id,
      email: testEntity.email as string,
      password: "anIncorrectOne",
    };

    const response = await request(app).post("/live/admin/login").send(body);

    expect(response.statusCode).toEqual(401);
  });
});
