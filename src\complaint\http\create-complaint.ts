import { AppError } from "@mainframe-peru/common-core";
import { complaint as comp, email } from "@mainframe-peru/types";
import { Middleware } from "koa";
import { emailNotification } from "../../common";
import { reCaptchaApi } from "../../ext";
import { formatDateTimeLocale, transformNullToUndefined } from "../../helpers";
import { influencerService } from "../../influencer";
import { complaintService } from "../service";

export const createComplaint: Middleware<
  unknown,
  unknown,
  comp.CreateComplaintResponse
> = async ({ request, response }) => {
  const body = comp.CreateComplaintRequestSchema.parse(request.body);

  if (!body.reCaptchaToken) {
    throw new AppError({
      code: "MissingReCaptcha",
      message: "User must send reCaptcha",
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  }

  await reCaptchaApi.validate(body.reCaptchaToken, request.ip);
  const complaint = await complaintService.create(body);

  const influencer = await influencerService.get("id", body.influencerId);
  if (influencer) {
    const emailContent: email.NewComplainContent = {
      FirstName: complaint.firstName,
      LastName: complaint.lastName,
      Description: complaint.description,
      Document: complaint.documentValue,
      DocumentType: complaint.documentType,
      Domain: influencer.domain,
      Email: complaint.email,
      IsAdult: complaint.ageCategory === "ADULT" ? "Sí" : "No",
      IsForAdmin: true,
      Phone: complaint.phone,
      Status: complaint.status,
      Subject: complaint.subject,
      Type: complaint.type,
      CreatedAt: formatDateTimeLocale(complaint.createdAt),
    };
    await emailNotification.sendComplaintEmail(
      "<EMAIL>",
      influencer,
      emailContent,
    );
    await emailNotification.sendComplaintEmail(
      complaint.email,
      influencer,
      emailContent,
    );
  }

  response.body = {
    complaint: transformNullToUndefined(complaint),
  };
};
