import { ServiceBase } from "../service-base";
import {
  DetailedTransactionEntity,
  TransactionEntity,
  transactionRepository,
} from "./repository";

class TransactionService extends ServiceBase<
  typeof transactionRepository,
  TransactionEntity
> {
  constructor() {
    super(transactionRepository);
  }

  async getTransactions(
    filter: Parameters<typeof transactionRepository.findTransactions>[0],
  ): Promise<DetailedTransactionEntity[]> {
    return await transactionRepository.findTransactions(filter);
  }

  async getCompleteTransaction(id: number, influencerId: string) {
    return this.repository.getCompleteTransaction(id, influencerId);
  }
}

export const transactionService = new TransactionService();
