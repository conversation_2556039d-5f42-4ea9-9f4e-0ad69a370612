import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { getOfficer } from "./get-officer";
import { listOfficers } from "./list-officers";
import { loginOfficer } from "./login-officer";
import { putOfficer } from "./put-officer";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.get(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      officer: ["READ_OFFICER"],
    },
  }),
  getOfficer,
);
router.put(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      officer: ["PUT_OFFICER"],
    },
  }),
  putOfficer,
);
router.get(
  "/list-officers",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      officer: ["LIST_OFFICERS"],
    },
  }),
  listOfficers,
);

/**
 * Endpoints without authentication
 */
router.post("/login", loginOfficer);

export const officerRouter = router;
