import { InferSelectModel, and, eq } from "drizzle-orm";
import { NTU, transformNullToUndefined } from "../helpers";
import { RepositoryBase } from "../repository-base";
import { planTable, productTable } from "../schema";
import { sc } from "../services";

export type PlanEntity = InferSelectModel<typeof planTable>;

class PlanRepository extends RepositoryBase<typeof planTable> {
  constructor() {
    super(planTable);
  }

  async getByIdAndInfluencer(
    planId: number,
    influencerId?: string,
  ): Promise<NTU<PlanEntity> | undefined> {
    const plans = await (
      await sc.getDB()
    )
      .select()
      .from(this.table)
      .where(
        influencerId
          ? and(
              eq(this.table.id, planId),
              eq(this.table.influencerId, influencerId),
            )
          : eq(this.table.id, planId),
      );
    return plans != undefined && plans.length > 0
      ? transformNullToUndefined(plans[0])
      : undefined;
  }

  async getPlansProduct() {
    const db = await sc.getDB();
    const plans = await db
      .select()
      .from(this.table)
      .innerJoin(productTable, eq(this.table.productId, productTable.id));
    return plans;
  }
}

export const planRepository = new PlanRepository();
