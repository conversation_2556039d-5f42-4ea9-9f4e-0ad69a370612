import { Middleware } from "@koa/router";
import {
  AppError,
  comparePassword,
  createJWT,
  officerPoliciesConstant,
  Policies,
} from "@mainframe-peru/common-core";
import { officer } from "@mainframe-peru/types";
import { sc } from "../../services";
import { officerService } from "../service";
import { OfficerEntity } from "../repository";

export const loginOfficer: Middleware<
  unknown,
  unknown,
  officer.LoginOfficerResponse
> = async ({ request, response }) => {
  const body = officer.LoginOfficerRequestSchema.parse(request.body);
  const servicerResponse = await officerService.get("email", body.email);

  const entity = servicerResponse as OfficerEntity;

  const result = await comparePassword(body.password, entity.hash);

  if (!result) {
    throw new AppError({
      logLevel: "NONE",
      code: "FailedLogin",
      message: "Contraseña incorrecta",
      statusCode: "UNAUTHORIZED",
    });
  }

  // Successful login
  const exp = Math.floor(Date.now() / 1000) + 2 * 60 * 60; // expires in 2 hours
  const jwt = await createJWT(
    "officer",
    {
      id: entity.id,
      email: entity.email,
      firstName: entity.firstName,
      lastName: entity.lastName,
      policies: entity.policies as Record<string, number>,
    },
    sc.vars.keys.officer.private,
    exp,
  );
  response.set(
    "Set-Cookie",
    `session=${jwt}; Max-Age=${exp}; Secure; SameSite=Strict; Path=/; HttpOnly`,
  );

  response.body = officer.LoginOfficerResponseSchema.parse({
    ...entity,
    policies: officer.PoliciesSchema.parse(
      Policies.unmask(
        entity.policies as Record<string, number>,
        officerPoliciesConstant,
      ),
    ),
  });
};
