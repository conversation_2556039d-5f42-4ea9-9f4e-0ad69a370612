import { and, eq, InferSelectModel, SQL } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { complaintTable } from "../schema";
import { complaint as comp } from "@mainframe-peru/types";
import { sc } from "../services";
import { SupportStatus } from "@mainframe-peru/types/build/common";

export type ComplaintEntity = InferSelectModel<typeof complaintTable>;
class ComplaintRepository extends RepositoryBase<typeof complaintTable> {
  constructor() {
    super(complaintTable);
  }

  async findComplaints(
    filters: comp.ListComplaintsRequest & { influencerId: string },
  ): Promise<ComplaintEntity[]> {
    const { influencerId } = this.table;
    const { status, type } = filters;
    const conditions: SQL[] = [];

    if (status) {
      conditions.push(eq(this.table.status, status));
    }

    if (type) {
      conditions.push(eq(this.table.type, type));
    }

    return await (
      await sc.getDB()
    )
      .select()
      .from(this.table)
      .where(and(eq(influencerId, filters.influencerId), ...conditions));
  }

  async updateIssue(
    issueId: number,
    status: SupportStatus,
  ): Promise<ComplaintEntity[]> {
    const db = await sc.getDB();
    return await db
      .update(this.table)
      .set({
        status,
      })
      .where(and(eq(this.table.id, issueId)))
      .returning();
  }
}

export const complaintRepository = new ComplaintRepository();
