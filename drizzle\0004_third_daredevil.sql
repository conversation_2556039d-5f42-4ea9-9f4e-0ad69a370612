DO $$ BEGIN
 CREATE TYPE "core_backend"."user_attribute_type" AS ENUM('TEXT', 'MULTI', 'SINGLE');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."attribute" (
	"id" text PRIMARY KEY NOT NULL,
	"influencer_id" text NOT NULL,
	"description" text,
	"text" text NOT NULL,
	"type" "core_backend"."user_attribute_type" NOT NULL,
	"options" jsonb
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."user_attribute_value" (
	"id" serial PRIMARY KEY NOT NULL,
	"attribute_id" text NOT NULL,
	"user_id" integer NOT NULL,
	"value" text NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."attribute" ADD CONSTRAINT "attribute_influencer_id_influencer_id_fk" FOREIGN KEY ("influencer_id") REFERENCES "core_backend"."influencer"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."user_attribute_value" ADD CONSTRAINT "user_attribute_value_attribute_id_attribute_id_fk" FOREIGN KEY ("attribute_id") REFERENCES "core_backend"."attribute"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."user_attribute_value" ADD CONSTRAINT "user_attribute_value_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
