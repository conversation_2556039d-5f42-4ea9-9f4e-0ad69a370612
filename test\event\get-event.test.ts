import { event } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { eventService } from "../../src/event";
import { influencerService } from "../../src/influencer";
import { productService } from "../../src/product/service";
import { TestData } from "../common";

describe("get event tests", () => {
  test("gets an existing event by id", async () => {
    await influencerService.create(TestData.influencer);
    await productService.create(TestData.product);
    const testEntity = await eventService.create(TestData.event);

    const queryParams: event.GetEventRequest = {
      id: testEntity.id,
    };

    const response = await request(app).get("/live/event").query(queryParams);

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      id: testEntity.id,
      influencerId: TestData.influencer.id,
      name: TestData.event.name,
      imageUrl: expect.any(String),
      description: TestData.event.description,
      status: TestData.event.status,
      type: TestData.event.type,
      participationForm: TestData.event.participationForm,
      startDate: (TestData.event.startDate as Date).toISOString(),
      eventDate: (TestData.event.eventDate as Date).toISOString(),
      endDate: (TestData.event.endDate as Date).toISOString(),
      createdAt: (TestData.event.createdAt as Date).toISOString(),
      updatedAt: (TestData.event.updatedAt as Date).toISOString(),
      location: TestData.event.location,
    });
  });
});
