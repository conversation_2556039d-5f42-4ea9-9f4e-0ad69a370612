import { common, user } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { userService } from "../../src/user";
import { TestData } from "../common";

describe("login user tests", () => {
  test("successfully login a user", async () => {
    // Initial setup
    const password = "genericpassword";
    await influencerService.create(TestData.influencer);
    const testEntity = await userService.processCreation({
      password,
      email: "<EMAIL>",
      firstName: "Generic",
      lastName: "Name",
      phone: "928192929",
      gender: common.Enums.Gender.Values.M,
      birthDate: new Date(),
      documentType: "DNI",
      documentValue: "7777777",
      influencerId: TestData.influencer.id,
    });

    const body: user.LoginUserRequest = {
      influencerId: TestData.influencer.id,
      email: "   <EMAIL>    ",
      password: password,
    };

    const response = await request(app).post("/live/user/login").send(body);

    expect(response.statusCode).toEqual(200);

    const jwt = response.header["set-cookie"][0].split("; ")[0].substring(8);
    const payload = jwt.split(".")[1];
    const parsed = JSON.parse(Buffer.from(payload, "base64").toString("utf-8"));
    expect(parsed).toEqual(
      expect.objectContaining({
        id: testEntity.id,
        influencerId: testEntity.influencerId,
        email: testEntity.email,
        firstName: testEntity.firstName,
        lastName: testEntity.lastName,
        policies: testEntity.policies,
        iss: "mainframe:user",
      }),
    );
  });

  test("unsuccessfully login a user", async () => {
    // Initial setup
    const password = "genericpassword";

    await influencerService.create(TestData.influencer);
    const testEntity = await userService.processCreation({
      password,
      email: "<EMAIL>",
      firstName: "Generic",
      lastName: "Name",
      phone: "928192929",
      gender: common.Enums.Gender.Values.M,
      birthDate: new Date(),
      documentType: "DNI",
      documentValue: "7777777",
      influencerId: TestData.influencer.id,
    });

    const body: user.LoginUserRequest = {
      influencerId: TestData.influencer.id,
      email: testEntity.email as string,
      password: "invalidPasswordLol",
    };

    const response = await request(app).post("/live/user/login").send(body);

    expect(response.statusCode).toEqual(401);
  });
});
