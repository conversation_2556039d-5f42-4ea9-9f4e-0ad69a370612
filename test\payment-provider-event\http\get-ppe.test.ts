import { ppe } from "@mainframe-peru/types";
import { influencerService } from "../../../src/influencer/service";
import { paymentProviderEventService } from "../../../src/payment-provider-event";
import { transactionService } from "../../../src/transaction";
import { userService } from "../../../src/user";
import { TestData, adminAuthToken } from "../../common";
import request from "supertest";
import { app } from "../../../src/api-handler";

describe("get provider payment event", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await transactionService.create(TestData.transaction);
  });

  test("get ppe", async () => {
    const createdPPE = await paymentProviderEventService.create({
      userId: TestData.user.id,
      transactionId: TestData.transaction.id,
      state: "FAIL",
      provider: "CULQI",
      responseMessage: "this payment failed",
      type: "CHARGE_CREATION",
    });
    const query: ppe.GetPaymentProviderEventRequest = {
      transactionId: TestData.transaction.id,
    };

    const response = await request(app)
      .get("/live/payment-provider-event")
      .query(query)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      id: expect.any(Number),
      state: createdPPE.state,
      provider: createdPPE.provider,
      type: createdPPE.type,
      createdAt: createdPPE.createdAt.toISOString(),
      message: createdPPE.responseMessage,
    });
  });
});
