import { email, ext } from "@mainframe-peru/types";
import { zendeskApi } from "../ext/zendesk";
import { contact } from "@mainframe-peru/types/build";
import { userService } from "../user";
import { recurrenceService } from "../recurrence";
import { emailNotification } from "../common";
import { influencerService } from "../influencer";
import { UserEntity } from "../user/repository";

class ContactService {
  constructor() {}

  async sendContact(params: contact.ContactRequest) {
    const influencer = await influencerService.get("id", params.influencerId);
    if (influencer) {
      const emailContent: email.ContactEmailContent = {
        Name: params.name,
        Email: params.email,
        Status: params.status,
        Message: params.message,
        Subject: params.motive,
      };
      await emailNotification.sendContactEmail(
        params.email,
        influencer,
        emailContent,
      );
    }
  }

  async sendSupport(params: contact.SupportRequest) {
    let user: UserEntity | undefined;
    if (params.userId) {
      user = await userService.get("id", params.userId);
      if (user) {
        params.name = user.firstName + " " + user.lastName;
        params.email = user.email;
        params.phone = user.phone || params.phone;
        params.documentValue = user.documentValue || params.documentValue;
      }
    }
    await this.zendeskSupport(params, user);

    const influencer = await influencerService.get("id", params.influencerId);
    if (influencer) {
      const emailContent: email.SupportEmailContent = {
        Name: params.name,
        Email: params.email,
        Date: params.operationDate,
        Type: params.supportType,
        Subject: params.motive,
        Message: params.message,
      };

      await emailNotification.sendSupportEmail(
        params.email,
        influencer,
        emailContent,
        params.imageUrl,
      );
    }
  }

  async zendeskSupport(
    params: contact.SupportRequest,
    user: UserEntity | undefined,
  ) {
    const request: ext.zendesk.CreateOrUpdateCustomerRequest = {
      user: {
        name: params.name,
        email: params.email,
        phone: params.phone,
        alias: "",
        skip_verify_email: true,
        role: "end-user",
        user_fields: {
          subscription_period: undefined,
          last_time_paid: undefined,
          date_end: undefined,
        },
      },
    };
    if (params.userId) {
      if (user) {
        request.user.name = user.firstName + " " + user.lastName;
        request.user.email = user.email;
        request.user.phone = user.phone || "";
        request.user.alias = user.alias;

        const recurrence = await recurrenceService.getActiveRecurrence(user.id);
        if (recurrence) {
          request.user.user_fields.subscription_period =
            recurrence.planId.toString();
          request.user.user_fields.last_time_paid = recurrence.renewalDate;
          request.user.user_fields.date_end = recurrence.endDate;
        }
      }
    }
    const influencer = await influencerService.get("id", params.influencerId);
    if (influencer?.providersConfiguration?.ZENDESK) {
      await zendeskApi.createOrUpdateUser(
        request,
        influencer.providersConfiguration.ZENDESK,
      );
    }
  }
}

export const contactService = new ContactService();
