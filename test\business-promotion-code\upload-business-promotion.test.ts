import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { businessPromotionService } from "../../src/business-promotion";
import { businessPromotionCodeService } from "../../src/business-promotion-code";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken } from "../common";
import { faker } from "@faker-js/faker";

describe("upload business promotion codes tests", () => {
  test("upload a 45 business promotion codes", async () => {
    await influencerService.create(TestData.influencer);
    await businessService.create(TestData.business);
    await businessPromotionService.create(TestData.businessPromotion);

    const body: bpc.UploadPromotionCodesRequest = {
      promotionId: TestData.businessPromotion.id,
      codes: faker.helpers.multiple(() => faker.string.alphanumeric(6), {
        count: 45,
      }),
    };

    const response = await request(app)
      .post("/live/business-promotion-code/upload")
      .send(body)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);

    const created = await businessPromotionCodeService.list({
      promotionId: TestData.businessPromotion.id,
    });
    expect(created.length).toEqual(45);
  });
});
