import { card } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import { randomUUID } from "node:crypto";
import request from "supertest";
import { app } from "../../src/api-handler";
import { cardService } from "../../src/card/service";
import { influencerService } from "../../src/influencer/service";
import { userService } from "../../src/user";
import {
  TestData,
  adminAuthToken,
  officerAuthToken,
  userAuthToken,
} from "../common";

describe("payment.list-card", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);

    for (const i of [0, 1]) {
      await cardService.create({
        id: i + 10 ** 6,
        userId: TestData.user.id,
        brand: "Visa",
        default: i === 0,
        number: "45578806****6275",
        paymentProvider: Enums.PaymentProvider.Enum.CULQI,
        token: randomUUID(),
      });
    }
  });
  test("list user cards (as user)", async () => {
    const response = await request(app)
      .get("/live/card")
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    const body = response.body as card.ListUserCardsResponse;
    expect(body.cards.length).toEqual(2);
    for (const c of body.cards) {
      expect(c.number).toEqual("************6275");
    }
  });

  test("list user cards (as admin)", async () => {
    const response = await request(app)
      .get("/live/card")
      .query({ userId: TestData.user.id })
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    const body = response.body as card.ListUserCardsResponse;
    expect(body.cards.length).toEqual(2);
  });

  test("list user cards (as officer)", async () => {
    const response = await request(app)
      .get("/live/card")
      .query({ userId: TestData.user.id })
      .set("Cookie", `session=${await officerAuthToken}`);

    expect(response.status).toBe(200);
    const body = response.body as card.ListUserCardsResponse;
    expect(body.cards.length).toEqual(2);
  });
});
