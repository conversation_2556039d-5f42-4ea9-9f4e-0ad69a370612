import { Enums } from "@mainframe-peru/types/build/common";
import { add } from "date-fns";
import request from "supertest";
import { influencerService } from "../../../src/influencer/service";
import { planService } from "../../../src/plan";
import { recurrenceService } from "../../../src/recurrence/service";
import { app } from "../../../src/sqs-handler";
import { userService } from "../../../src/user";
import { TestData } from "../../common";
import { productService } from "../../../src/product/service";

describe("sqs.recurrence.cancel-recurrence", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await productService.create(TestData.product);
    await planService.create(TestData.plan);
  });

  test("perform recurrence cancelation", async () => {
    const recurrence = await recurrenceService.create({
      userId: TestData.user.id,
      type: Enums.RecurrenceType.Enum.CARD,
      planId: TestData.plan.id,
      renewalDate: add(new Date(), { weeks: 2 }),
      startDate: new Date(),
      status: "ACTIVE",
    });

    const response = await request(app)
      .post("/recurrence/cancellation")
      .send({ userId: TestData.user.id });

    expect(response.status).toBe(204);

    const storedRecurrence = await recurrenceService.get("id", recurrence.id);
    expect(storedRecurrence?.status).toEqual("INACTIVE");
    expect(storedRecurrence?.endDate).toEqual(expect.any(Date));
  });
});
