import { InferSelectModel } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { influencerTable } from "../schema";

export type InfluencerEntity = InferSelectModel<typeof influencerTable>;

class InfluencerRepository extends RepositoryBase<
  typeof influencerTable,
  string
> {
  constructor() {
    super(influencerTable);
  }
}

export const influencerRepository = new InfluencerRepository();
