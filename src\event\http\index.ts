import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { createEventEndpoint } from "./create-event";
import { getEventEndpoint } from "./get-event";
import { invalidateCacheEndpoint } from "./invalidate-cache";
import { listEventsEndpoint } from "./list-events";
import { updateEventEndpoint } from "./update-event";

const router = new Router();
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.get("/", getEventEndpoint);

router.post(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      event: ["CREATE_EVENT"],
    },
    admin: {
      event: ["CREATE_EVENT"],
    },
  }),
  createEventEndpoint,
);

router.put(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      event: ["UPDATE_EVENT"],
    },
    admin: {
      event: ["UPDATE_EVENT"],
    },
  }),
  updateEventEndpoint,
);

router.get(
  "/list-events",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      event: ["LIST_EVENTS"],
    },
    admin: {
      event: ["LIST_EVENTS"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  listEventsEndpoint,
);

router.post(
  "/invalidate-cache",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      event: ["UPDATE_EVENT"],
    },
  }),
  invalidateCacheEndpoint,
);

// Public
router.get("/public-list-events", listEventsEndpoint);

export const eventRouter = router;
