import { Middleware } from "@koa/router";
import {
  AppError,
  Auth,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { plan } from "@mainframe-peru/types";
import { planService } from "../service";
import { restrictParams } from "../../common";
import { productService } from "../../product/service";

export const getPlanEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  plan.GetPlanResponse
> = async ({ request, response, state }) => {
  const params = plan.GetPlanRequestSchema.parse(request.query);
  restrictParams(state.auth, params);

  const entity = await planService.getByIdAndInfluencer(
    params.id,
    params.influencerId,
  );

  if (!entity) {
    throw new AppError({
      code: "PlanNotFound",
      message: "No se encontró el plan solicitado",
      statusCode: "NOT_FOUND",
    });
  }

  const product = await productService.get("id", entity.productId);
  if (!product) {
    throw new AppError({
      code: "ProductNotFound",
      message: "Product not found",
      logLevel: "CRITICAL",
      statusCode: "INTERNAL_SERVER_ERROR",
    });
  }

  response.body = plan.GetPlanResponseSchema.parse({
    ...entity,
    amount: product.amount,
    currency: product.currency,
  });
};
