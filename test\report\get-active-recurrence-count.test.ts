import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer";
import { planService } from "../../src/plan";
import { recurrenceService } from "../../src/recurrence";
import { userService } from "../../src/user";
import { TestData, adminAuthToken } from "../common";
import { productService } from "../../src/product/service";

describe("get active recurrence count", () => {
  test("endpoint", async () => {
    await influencerService.create(TestData.influencer);
    await productService.create(TestData.product);
    const p1 = await planService.create({
      ...TestData.plan,
      id: undefined,
      name: "p1 plan",
    });
    const p2 = await planService.create({
      ...TestData.plan,
      id: undefined,
      name: "p2 plan",
    });
    for (let i = 0; i < 50; i++) {
      const u = await userService.create({
        ...TestData.user,
        id: undefined,
      });
      await recurrenceService.create({
        ...TestData.recurrence,
        id: undefined,
        userId: u.id,
        planId: i % 3 === 0 ? p1.id : p2.id,
      });
    }

    const response = await request(app)
      .get("/live/report/recurrence-by-plan")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      total: 50,
      plan: [
        {
          id: p1.id,
          count: 17,
          name: "p1 plan",
        },
        {
          id: p2.id,
          count: 33,
          name: "p2 plan",
        },
      ],
    });
  });
});
