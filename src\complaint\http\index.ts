import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { createComplaint } from "./create-complaint";
import { listComplaints } from "./list-complaints";
import { updateComplaint } from "./update-complaint";

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

export const complaintRouter = new Router();

complaintRouter.get(
  "/list-issues",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      complaint: ["LIST_ISSUES"],
    },
    admin: {
      complaint: ["LIST_ISSUES"],
    },
  }),
  listComplaints,
);

complaintRouter.put(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      complaint: ["PUT_ISSUE"],
    },
    admin: {
      complaint: ["PUT_ISSUE"],
    },
  }),
  updateComplaint,
);

/**
 * Endpoints without authentication
 */
complaintRouter.post("/", createComplaint);
