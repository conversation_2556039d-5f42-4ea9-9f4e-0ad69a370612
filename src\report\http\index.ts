import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { getTransactionsCountByStateEndpoint } from "./get-transactions-count-by-state";
import { listSubscribers } from "./list-subscribers";
import { getActiveRecurrenceCountEndpoint } from "./get-active-recurrence";
import { getUserCountEndpoint } from "./get-total-users";
import { listEventParticipants } from "./list-event-participants";
import { generateEventCsv } from "./generate-event-csv";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.use(authMiddleware);

router.get(
  "/transactions-count-by-state",
  getClientPoliciesValidationMiddleware({
    officer: {
      transaction: ["LIST_TRANSACTIONS"],
    },
    admin: {
      transaction: ["LIST_TRANSACTIONS"],
    },
  }),
  getTransactionsCountByStateEndpoint,
);

router.get(
  "/list-subscribers",
  getClientPoliciesValidationMiddleware({
    officer: {
      user: ["LIST_USERS"],
    },
    admin: {
      user: ["LIST_USERS"],
    },
  }),
  listSubscribers,
);

router.get(
  "/recurrence-by-plan",
  getClientPoliciesValidationMiddleware({
    officer: {
      recurrence: ["LIST_RECURRENCES"],
    },
    admin: {
      recurrence: ["LIST_RECURRENCES"],
    },
  }),
  getActiveRecurrenceCountEndpoint,
);

router.get(
  "/user-count",
  getClientPoliciesValidationMiddleware({
    officer: {
      user: ["LIST_USERS"],
    },
    admin: {
      user: ["LIST_USERS"],
    },
  }),
  getUserCountEndpoint,
);

router.post(
  "/event-participants/csv",
  getClientPoliciesValidationMiddleware({
    officer: {
      user: ["LIST_USERS"],
      event: ["GET_EVENT"],
    },
    admin: {
      user: ["LIST_USERS"],
      event: ["GET_EVENT"],
    },
  }),
  generateEventCsv,
);

router.get(
  "/event-participants",
  getClientPoliciesValidationMiddleware({
    officer: {
      user: ["LIST_USERS"],
      event: ["GET_EVENT"],
    },
    admin: {
      user: ["LIST_USERS"],
      event: ["GET_EVENT"],
    },
  }),
  listEventParticipants,
);

export const reportRouter = router;
