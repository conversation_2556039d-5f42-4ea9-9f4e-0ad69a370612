CREATE SCHEMA "core_backend";
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."currency" AS ENUM('PEN', 'USD');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."document_type" AS ENUM('CE', 'DNI', 'PS', 'OT');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."gender" AS ENUM('F', 'M', 'NB', 'NO');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."influencer_status" AS ENUM('ACTIVE', 'INACTIVE');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."invoice_destination" AS ENUM('PERSON', 'COMPANY');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."payment_provider" AS ENUM('CULQI', 'PAYPAL');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."payment_provider_event_state" AS ENUM('FAIL', 'SUCCESS');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."payment_provider_event_type" AS ENUM('USER_CREATION', 'CARD_CREATION', 'CHARGE_CREATION');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."recurrence_status" AS ENUM('CREATING', 'ACTIVE', 'INACTIVE');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."recurrence_type" AS ENUM('CARD', 'MANUAL');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."transaction_state" AS ENUM('PROCESSING', 'FAIL', 'SUCCESS');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."transaction_type" AS ENUM('RECURRENCE', 'PURCHASE');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."admin" (
	"id" serial PRIMARY KEY NOT NULL,
	"influencer_id" text NOT NULL,
	"email" text NOT NULL,
	"first_name" text,
	"last_name" text,
	"hash" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"policies" jsonb DEFAULT '{}'::jsonb
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."card" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"default" boolean NOT NULL,
	"payment_provider" "core_backend"."payment_provider" NOT NULL,
	"token" text NOT NULL,
	"number" text NOT NULL,
	"brand" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."influencer" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"status" "core_backend"."influencer_status" DEFAULT 'ACTIVE' NOT NULL,
	"logo_url" text NOT NULL,
	"transient_users" boolean DEFAULT false NOT NULL,
	"domain" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."invoice" (
	"id" serial PRIMARY KEY NOT NULL,
	"transaction_id" integer NOT NULL,
	"user_id" integer NOT NULL,
	"invoice_origin" integer NOT NULL,
	"voucher_type" integer NOT NULL,
	"serie" text NOT NULL,
	"number" integer NOT NULL,
	"link" text NOT NULL,
	"accepted_by_sunat" integer NOT NULL,
	"sunat_description" text NOT NULL,
	"sunat_note" text,
	"sunat_responsecode" text NOT NULL,
	"sunat_soap_error" text,
	"string_qr_code" text NOT NULL,
	"hash_code" text NOT NULL,
	"payload" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."officer" (
	"id" serial PRIMARY KEY NOT NULL,
	"email" text NOT NULL,
	"first_name" text NOT NULL,
	"last_name" text NOT NULL,
	"hash" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"policies" jsonb DEFAULT '{}'::jsonb NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."payment_provider_event" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"transaction_id" integer,
	"state" "core_backend"."payment_provider_event_state" NOT NULL,
	"provider" "core_backend"."payment_provider",
	"type" "core_backend"."payment_provider_event_type",
	"external_id" text,
	"response_message" text,
	"external" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."plan" (
	"id" serial PRIMARY KEY NOT NULL,
	"influencer_id" text NOT NULL,
	"code" text NOT NULL,
	"name" text NOT NULL,
	"active" boolean DEFAULT true,
	"amount" numeric NOT NULL,
	"currency" "core_backend"."currency" NOT NULL,
	"frequency" integer NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."recurrence" (
	"id" serial PRIMARY KEY NOT NULL,
	"type" "core_backend"."recurrence_type" NOT NULL,
	"status" "core_backend"."recurrence_status" NOT NULL,
	"user_id" integer NOT NULL,
	"plan_id" integer NOT NULL,
	"start_date" timestamp DEFAULT now() NOT NULL,
	"renewal_date" timestamp NOT NULL,
	"end_date" timestamp,
	"invoice_destination" "core_backend"."invoice_destination" DEFAULT 'PERSON' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."transaction_detail" (
	"id" serial PRIMARY KEY NOT NULL,
	"transaction_id" integer NOT NULL,
	"amount" numeric NOT NULL,
	"entity_id" integer NOT NULL,
	"quantity" integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."transaction" (
	"id" serial PRIMARY KEY NOT NULL,
	"public_id" uuid NOT NULL,
	"user_id" integer NOT NULL,
	"influencer_id" text NOT NULL,
	"type" "core_backend"."transaction_type" NOT NULL,
	"state" "core_backend"."transaction_state" NOT NULL,
	"amount" numeric NOT NULL,
	"currency" "core_backend"."currency" NOT NULL,
	"last_payment_provider_event_id" integer,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."user_payment_provider" (
	"user_id" integer NOT NULL,
	"payment_provider" "core_backend"."payment_provider" NOT NULL,
	"value" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."user" (
	"id" serial PRIMARY KEY NOT NULL,
	"influencer_id" text NOT NULL,
	"email" text NOT NULL,
	"first_name" text NOT NULL,
	"last_name" text NOT NULL,
	"alias" text NOT NULL,
	"hash" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"policies" jsonb DEFAULT '{}'::jsonb,
	"phone" text,
	"gender" "core_backend"."gender" DEFAULT 'NO',
	"birth_date" timestamp,
	"document_type" "core_backend"."document_type",
	"document_value" text,
	"company_id" text,
	"country" text,
	"city" text,
	"province" text,
	"district" text,
	"zip_code" text,
	"line1" text,
	"line2" text
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."admin" ADD CONSTRAINT "admin_influencer_id_influencer_id_fk" FOREIGN KEY ("influencer_id") REFERENCES "core_backend"."influencer"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."card" ADD CONSTRAINT "card_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."invoice" ADD CONSTRAINT "invoice_transaction_id_transaction_id_fk" FOREIGN KEY ("transaction_id") REFERENCES "core_backend"."transaction"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."invoice" ADD CONSTRAINT "invoice_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."payment_provider_event" ADD CONSTRAINT "payment_provider_event_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."payment_provider_event" ADD CONSTRAINT "payment_provider_event_transaction_id_transaction_id_fk" FOREIGN KEY ("transaction_id") REFERENCES "core_backend"."transaction"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."plan" ADD CONSTRAINT "plan_influencer_id_influencer_id_fk" FOREIGN KEY ("influencer_id") REFERENCES "core_backend"."influencer"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."recurrence" ADD CONSTRAINT "recurrence_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."recurrence" ADD CONSTRAINT "recurrence_plan_id_plan_id_fk" FOREIGN KEY ("plan_id") REFERENCES "core_backend"."plan"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."transaction_detail" ADD CONSTRAINT "transaction_detail_transaction_id_transaction_id_fk" FOREIGN KEY ("transaction_id") REFERENCES "core_backend"."transaction"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."transaction" ADD CONSTRAINT "transaction_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."transaction" ADD CONSTRAINT "transaction_influencer_id_influencer_id_fk" FOREIGN KEY ("influencer_id") REFERENCES "core_backend"."influencer"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."user_payment_provider" ADD CONSTRAINT "user_payment_provider_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."user" ADD CONSTRAINT "user_influencer_id_influencer_id_fk" FOREIGN KEY ("influencer_id") REFERENCES "core_backend"."influencer"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "transaction_public_id_index" ON "core_backend"."transaction" USING btree ("public_id");