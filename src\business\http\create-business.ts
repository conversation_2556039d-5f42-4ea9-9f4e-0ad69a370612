import { Middleware } from "@koa/router";
import {
  AppError,
  AuthAdmin,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { business as bu } from "@mainframe-peru/types";
import { isImageExtensionValid, transformNullToUndefined } from "../../helpers";
import { sc } from "../../services";
import { storageService } from "../../storage/service";
import { businessService } from "../service";

export const createBusinessEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  bu.CreateBusinessResponse
> = async ({ request, response, state }) => {
  const body = bu.CreateBusinessRequestSchema.parse(request.body);

  let imageUrl: undefined | string = undefined;
  if (request.file) {
    if (!isImageExtensionValid(request.file.mimetype)) {
      throw new AppError({
        code: "InvalidImage",
        message: "La imagen no tiene una extensión válida.",
        logLevel: "ERROR",
        statusCode: "BAD_REQUEST",
      });
    }
    imageUrl = await storageService.uploadFile({
      bucket: sc.vars.modulesStorageBucket,
      folder: "business",
      fileName: crypto.randomUUID(),
      buffer: request.file.buffer,
      mimetype: request.file.mimetype,
      metadata: {
        name: body.name,
        influencerId: state.auth.influencerId,
      },
    });
  }

  const business = await businessService.create({
    influencerId: state.auth.influencerId,
    name: body.name,
    description: body.description,
    imageUrl,
    ruc: body.ruc,
  });

  response.body = transformNullToUndefined({
    id: business.id,
    name: business.name,
    description: business.description || undefined,
    imageUrl,
    ruc: business.ruc || undefined,
    createdAt: business.createdAt,
    updatedAt: business.updatedAt,
  });
};
