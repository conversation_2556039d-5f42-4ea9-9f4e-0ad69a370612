import { AuthAdmin, AuthorizedContextState } from "@mainframe-peru/common-core";
import { complaint as comp } from "@mainframe-peru/types";
import { Middleware } from "koa";
import { complaintService } from "../service";

export const listComplaints: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  comp.ListComplaintsResponse
> = async ({ request, response, state }) => {
  const data = comp.ListComplaintsRequestSchema.parse(request.query);

  const complaints = await complaintService.getComplaints({
    ...data,
    influencerId: state.auth.influencerId,
  });

  response.body = {
    complaints,
  };
};
