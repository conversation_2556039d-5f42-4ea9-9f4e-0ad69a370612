import { common, user } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { attributeService } from "../../src/attribute";
import { influencerService } from "../../src/influencer/service";
import { userService } from "../../src/user";
import { TestData, userAuthToken } from "../common";

describe("update user attributes", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
  });
  test("fill multi attribute", async () => {
    const f1 = await attributeService.create({
      id: "field1",
      influencerId: TestData.influencer.id,
      entity: "USER",
      text: "example",
      type: "MULTI",
      options: [
        {
          id: "uni",
          value: "universitario",
        },
        {
          id: "ali",
          value: "alianza",
        },
      ],
    });
    // Test incorrect value
    const requestBody: user.UpdateUserAttributesRequest = {
      attributes: {
        [f1.id]: ["spr"],
      },
    };
    const response = await request(app)
      .post("/live/user/attributes")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(400);
    const body = response.body as common.HttpApiError;
    expect(body.code).toEqual("ValidationFailed");

    // Test correct value
    const requestBody2: user.UpdateUserAttributesRequest = {
      attributes: {
        [f1.id]: ["uni"],
      },
    };
    const response2 = await request(app)
      .post("/live/user/attributes")
      .send(requestBody2)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response2.status).toBe(200);
    const body2 = response2.body as user.UpdateUserAttributesResponse;
    expect(body2.attributes).toEqual({
      [f1.id]: ["uni"],
    });

    const av = await userService.get("id", TestData.user.id);
    expect(av?.attributes).toEqual({
      field1: ["uni"],
    });
  });

  test("fill multi attribute with empty value", async () => {
    const f1 = await attributeService.create({
      id: "field1",
      influencerId: TestData.influencer.id,
      entity: "USER",
      text: "example",
      type: "MULTI",
      options: [
        {
          id: "uni",
          value: "universitario",
        },
        {
          id: "ali",
          value: "alianza",
        },
      ],
    });

    const requestBody2: user.UpdateUserAttributesRequest = {
      attributes: {
        [f1.id]: [],
      },
    };
    const response2 = await request(app)
      .post("/live/user/attributes")
      .send(requestBody2)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response2.status).toBe(200);
    const body2 = response2.body as user.UpdateUserAttributesResponse;
    expect(body2.attributes).toEqual({
      [f1.id]: [],
    });

    const av = await userService.get("id", TestData.user.id);
    expect(av?.attributes).toEqual({
      [f1.id]: [],
    });
  });

  test("fill single attribute", async () => {
    const f1 = await attributeService.create({
      id: "field1",
      influencerId: TestData.influencer.id,
      entity: "USER",
      text: "example",
      type: "SINGLE",
      options: [
        {
          id: "uni",
          value: "universitario",
        },
        {
          id: "ali",
          value: "alianza",
        },
      ],
    });
    // Test incorrect value
    const requestBody: user.UpdateUserAttributesRequest = {
      attributes: {
        [f1.id]: "spr",
      },
    };
    const response = await request(app)
      .post("/live/user/attributes")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(400);
    const body = response.body as common.HttpApiError;
    expect(body.code).toEqual("ValidationFailed");

    // Test correct value
    const requestBody2: user.UpdateUserAttributesRequest = {
      attributes: {
        [f1.id]: "uni",
      },
    };
    const response2 = await request(app)
      .post("/live/user/attributes")
      .send(requestBody2)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response2.status).toBe(200);
    const body2 = response2.body as user.UpdateUserAttributesResponse;
    expect(body2.attributes).toEqual({
      [f1.id]: "uni",
    });

    const av = await userService.get("id", TestData.user.id);
    expect(av?.attributes?.field1).toEqual("uni");
  });

  test("fill text attribute", async () => {
    const f1 = await attributeService.create({
      id: "field1",
      influencerId: TestData.influencer.id,
      entity: "USER",
      text: "example",
      type: "TEXT",
    });

    // Test correct value
    const requestBody: user.UpdateUserAttributesRequest = {
      attributes: {
        [f1.id]: "lorem ipsum",
      },
    };
    const response = await request(app)
      .post("/live/user/attributes")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    const body = response.body as user.UpdateUserAttributesResponse;
    expect(body.attributes).toEqual(requestBody.attributes);

    const av = await userService.get("id", TestData.user.id);
    expect(av?.attributes?.field1).toEqual("lorem ipsum");
  });
});
