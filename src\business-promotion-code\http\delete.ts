import { Middleware } from "@koa/router";
import {
  AppError,
  AuthAdmin,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import { businessPromotionService } from "../../business-promotion/service";
import { businessPromotionCodeService } from "../service";

export const deleteBusinessPromotionCodeEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  null
> = async ({ request, response, state }) => {
  const body = bpc.DeleteBusinessPromotionCodeRequestSchema.parse(request.body);

  let promotionId;
  if (body.all) {
    promotionId = body.promotionId;
  } else {
    const code = await businessPromotionCodeService.get("id", body.id);
    if (!code) {
      throw new AppError({
        code: "BusinessPromotionCodeNotFound",
        message: "Could not find business promotion code",
        statusCode: "NOT_FOUND",
        logLevel: "INFO",
      });
    }
    promotionId = code.promotionId;
  }

  await businessPromotionService.getSecure({
    id: promotionId,
    influencerId: state.auth.influencerId,
  });

  await businessPromotionCodeService.deleteCodes(body);
  response.body = null;
};
