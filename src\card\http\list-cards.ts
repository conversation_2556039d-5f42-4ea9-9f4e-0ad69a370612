import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { card } from "@mainframe-peru/types";
import { cardService } from "../service";

export const listUserCards: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  card.ListUserCardsResponse
> = async ({ request, response, state }) => {
  let cards;
  if (state.auth.iss === "mainframe:user") {
    cards = await cardService.listByUserId(state.auth.id);
  } else {
    const query = card.ListUserCardsRequestSchema.parse(request.query);
    cards = await cardService.listByUserId(query.userId);
  }

  response.body = {
    cards: cards.map((c) => cardService.toPublic(c)),
  };
};
