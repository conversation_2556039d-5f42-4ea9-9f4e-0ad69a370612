import { AppError, Policies, hashPassword } from "@mainframe-peru/common-core";
import { userPoliciesConstant } from "@mainframe-peru/common-core/build/policies/lib/modules/user";
import { common, user } from "@mainframe-peru/types";
import { NTU, generateRandomAlias } from "../helpers";
import { influencerService } from "../influencer";
import { ServiceBaseWithAttributes } from "../service-attribute-base";
import { DetailedUserEntity, UserEntity, userRepository } from "./repository";
import { OAuth2Client } from "google-auth-library";

type RequiredNonNullableObject<T extends object> = {
  [P in keyof Required<T>]: NonNullable<T[P]>;
};

export type CompleteUser = RequiredNonNullableObject<UserEntity>;

type GoogleUser = {
  email: string;
  firstName: string;
  lastName: string;
};

class UserService extends ServiceBaseWithAttributes<
  typeof userRepository,
  UserEntity
> {
  constructor() {
    super(userRepository);
  }

  get attributeEntity(): common.AttributeEntity {
    return "USER";
  }

  // Initializes the creation process, processing the password and converting it to the hash
  async processCreation(data: user.CreateUserRequest): Promise<UserEntity> {
    const influencer = await influencerService.get("id", data.influencerId);
    if (!influencer) {
      throw new AppError({
        code: "InfluencerNotFound",
        message: "No se pudo encontrar al influencer indicado",
        logLevel: "INFO",
        statusCode: "BAD_REQUEST",
      });
    }

    const user = await this.repository.findUser(data.influencerId, data.email);
    if (user) {
      if (user.authenticationType !== "EMAIL") {
        throw new AppError({
          code: "InvalidAuthenticationMethod",
          message:
            "Un usuario con el correo proporcionado ya está registrado con otro método de autenticación.",
          logLevel: "INFO",
          statusCode: "BAD_REQUEST",
        });
      }
      throw new AppError({
        code: "UserExists",
        message: "Un usuario con el correo proporcionado ya existe",
        logLevel: "INFO",
        statusCode: "BAD_REQUEST",
      });
    }

    const newUser = await this.create({
      ...data,
      policies: Policies.mask(
        {
          general: {
            REGULAR: !influencer.transientUsers,
            TRANSIENT: true,
          },
        },
        userPoliciesConstant,
      ),
      alias: generateRandomAlias(),
      hash: data.password ? await hashPassword(data.password) : undefined,
    });
    return newUser;
  }

  async updatePassword(userId: number, newPassword: string) {
    await this.repository.update(userId, {
      hash: await hashPassword(newPassword),
    });
  }

  // Gets admin by influencerId and email (useful for login)
  async getUser(
    influencerId: string,
    email: string,
  ): Promise<UserEntity | undefined> {
    const result = await this.repository.findUser(influencerId, email);
    return result;
  }

  async getUsers(
    filter: Parameters<typeof userRepository.findUsers>[0],
  ): Promise<NTU<DetailedUserEntity>[]> {
    return await this.repository.findUsers(filter);
  }

  async checkUserForCharge(userId: number): Promise<CompleteUser> {
    const user = await this.get("id", userId);
    if (!user) {
      throw new AppError({
        code: "UserNotFound",
        message: "Sesión inválida",
        logLevel: "ERROR",
        statusCode: "FORBIDDEN",
      });
    }
    if (
      !user.country ||
      !user.city ||
      !user.line1 ||
      !user.email ||
      !user.firstName ||
      !user.lastName ||
      !user.phone
    ) {
      throw new AppError({
        code: "UserMissingParameters",
        message:
          "User must have address, email, first name, last name and phone",
        logLevel: "ERROR",
      });
    }
    return user as CompleteUser;
  }

  async verifyGoogleUser(
    googleToken: string,
    idToken: string,
  ): Promise<GoogleUser | undefined> {
    const client = new OAuth2Client(googleToken);

    const ticket = await client.verifyIdToken({
      idToken,
      audience: googleToken,
    });

    const payload = ticket.getPayload();
    if (!payload || !payload.email) {
      return undefined;
    }

    return {
      email: payload.email,
      firstName: payload.given_name || "",
      lastName: payload.family_name || "",
    };
  }

  async authenticateGoogleUser(
    influencerId: string,
    idToken: string,
  ): Promise<UserEntity> {
    const influencer = await influencerService.get("id", influencerId);
    if (!influencer || !influencer.providersConfiguration?.GOOGLE?.apiKey) {
      throw new AppError({
        code: "InfluencerNotFound",
        message: "Invalid influencer",
        logLevel: "ERROR",
      });
    }

    const googleUser = await this.verifyGoogleUser(
      influencer.providersConfiguration?.GOOGLE?.apiKey,
      idToken,
    );
    if (!googleUser) {
      throw new AppError({
        code: "InvalidGoogleUser",
        message: "Google doesn't recognize the user",
        logLevel: "ERROR",
      });
    }

    let user = await this.getUser(influencerId, googleUser.email);
    if (!user) {
      user = await this.repository.create({
        influencerId,
        email: googleUser.email,
        authenticationType: "GOOGLE",
        firstName: googleUser.firstName,
        lastName: googleUser.lastName,
        alias: generateRandomAlias(),
        policies: Policies.mask(
          {
            general: {
              REGULAR: !influencer.transientUsers,
              TRANSIENT: true,
            },
          },
          userPoliciesConstant,
        ),
      });
    }

    return user;
  }
}

export const userService = new UserService();
