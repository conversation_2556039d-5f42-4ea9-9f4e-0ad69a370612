import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { card as ct } from "@mainframe-peru/types";
import { paymentProviderEventService } from "../../payment-provider-event/service";
import { userService } from "../../user";
import { cardService } from "../service";

export const addCard: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  ct.AddCardOnUserResponse
> = async ({ request, response, state }) => {
  const body = ct.AddCardOnUserRequestSchema.parse(request.body);

  const userId = Number(state.auth.id);
  const user = await userService.get("id", userId);
  if (!user) {
    throw new AppError({
      code: "UserNotFound",
      message: "Sesión inválida",
      logLevel: "ERROR",
      statusCode: "FORBIDDEN",
    });
  }

  const culqiCard = await paymentProviderEventService.executeCardCreation(
    userId,
    body,
    user.influencerId,
  );
  const userCards = await cardService.listByUserId(userId);
  const defaultCard = userCards.find((c) => c.default);
  if (defaultCard) {
    await cardService.update(defaultCard.id, { default: false });
  }
  const card = await cardService.create({
    brand: culqiCard.source.iin.card_brand,
    default: true,
    number: culqiCard.source.card_number,
    paymentProvider: "CULQI",
    token: culqiCard.id,
    userId: userId,
  });
  response.body = cardService.toPublic(card);
};
