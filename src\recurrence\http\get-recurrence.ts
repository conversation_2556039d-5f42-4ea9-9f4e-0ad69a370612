import { Middleware } from "@koa/router";
import {
  AppError,
  Auth,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { recurrence as recurrenceT } from "@mainframe-peru/types";
import { planService } from "../../plan";
import { recurrenceService } from "../service";
import { restrictParams } from "../../common";
import { productService } from "../../product/service";

export const getRecurrence: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  recurrenceT.GetRecurrenceResponse
> = async ({ request, response, state }) => {
  const query = recurrenceT.GetRecurrenceRequestSchema.parse(request.query);
  restrictParams(state.auth, query);
  if (!query.userId) {
    throw new AppError({
      code: "UserIdIsRequired",
      message: "Se requiere un userId",
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  }
  const recurrence = await recurrenceService.getActiveRecurrence(
    query.userId,
    query.influencerId,
  );

  if (!recurrence) {
    throw new AppError({
      code: "RecurrenceNotFound",
      message: "Este usuario no tiene un pago recurrente",
      statusCode: "NOT_FOUND",
      logLevel: "DEBUG",
    });
  }

  const plan = await planService.get("id", recurrence.planId);

  if (plan === undefined) {
    throw new AppError({
      code: "PlanNotFound",
      message: "No se pudo encontrar el plan en el pago recurrente",
      logLevel: "CRITICAL",
      statusCode: "INTERNAL_SERVER_ERROR",
    });
  }

  const product = await productService.get("id", plan.productId);

  if (!product) {
    throw new AppError({
      code: "ProductNotFound",
      message: "Product not found",
      logLevel: "CRITICAL",
      statusCode: "INTERNAL_SERVER_ERROR",
    });
  }

  response.body = {
    plan: {
      amount: product.amount,
      currency: product.currency,
      frequency: plan.frequency,
      name: plan.name,
    },
    type: recurrence.type,
    status: recurrence.status,
    renewalDate: recurrence.renewalDate,
    startDate: recurrence.startDate,
    createdAt: recurrence.createdAt,
    updatedAt: recurrence.updatedAt,
    endDate: recurrence.endDate || undefined,
  };
};
