import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer";
import { TestData } from "../common";
import { mockReCaptchaResponse } from "../mocks";
import { mockClient } from "aws-sdk-client-mock";
import "aws-sdk-client-mock-jest";
import { PublishCommand, SNSClient } from "@aws-sdk/client-sns";

jest.mock("node-fetch");
describe("Send contact message tests", () => {
  test("successfully send contact message test", async () => {
    mockReCaptchaResponse();
    const snsMock = mockClient(SNSClient);

    await influencerService.create(TestData.influencer);

    const response = await request(app)
      .post("/live/contact/contact")
      .send({
        influencerId: TestData.influencer.id,
        name: "<PERSON>",
        email: "<EMAIL>",
        status: "Miembro",
        motive: "I hate",
        message: "I don't like this system",
        reCaptchaToken: "123123123",
      })
      .set("Accept", "application/json");

    expect(response.statusCode).toEqual(200);
    expect(snsMock).toHaveReceivedCommandTimes(PublishCommand, 1);
    expect(snsMock).toHaveReceivedCommandWith(PublishCommand, {
      Message: expect.stringContaining("Miembro"),
      MessageAttributes: {
        service: {
          DataType: "String",
          StringValue: "email",
        },
      },
    });
  });
});
