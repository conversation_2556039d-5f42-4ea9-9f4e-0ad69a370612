import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { mockClient } from "aws-sdk-client-mock";
import "aws-sdk-client-mock-jest";
import request from "supertest";
import { app } from "../../src/api-handler";
import { Enums } from "@mainframe-peru/types/build/common";
import { adminAuthToken } from "../common";
import { readFile } from "fs/promises";

describe("upload file tests", () => {
  test("upload a new file", async () => {
    const s3Mock = mockClient(S3Client);

    s3Mock.on(PutObjectCommand).resolves({});

    const testImage = await readFile("test/storage/test_image.jpg");
    const response = await request(app)
      .post("/live/storage")
      .field("folderName", Enums.StorageFolderName.Values.events)
      .field("metadata", JSON.stringify({ influencerId: "pchujoy" }))
      .attach("file", testImage, "test-image.jpg")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(s3Mock).toHaveReceivedCommandTimes(PutObjectCommand, 1);
    expect(response.body).toEqual({
      imageUrl: expect.any(String),
    });
  });

  test("replace a file", async () => {
    const s3Mock = mockClient(S3Client);

    s3Mock.on(PutObjectCommand).resolves({});

    const testImage = await readFile("test/storage/test_image.jpg");
    const id = crypto.randomUUID();
    const response = await request(app)
      .post("/live/storage")
      .field("id", id)
      .field("folderName", Enums.StorageFolderName.Values.events)
      .field("metadata", JSON.stringify({ influencerId: "pchujoy" }))
      .attach("file", testImage, "test-image.jpg")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(s3Mock).toHaveReceivedCommandTimes(PutObjectCommand, 1);
    expect(response.body).toEqual({
      imageUrl: expect.stringContaining(id),
    });
  });

  test("store a pdf file", async () => {
    const s3Mock = mockClient(S3Client);

    s3Mock.on(PutObjectCommand).resolves({});

    const testImage = await readFile("test/storage/sample.pdf");
    const id = crypto.randomUUID();
    const response = await request(app)
      .post("/live/storage")
      .field("id", id)
      .field("folderName", Enums.StorageFolderName.Values.events)
      .field("metadata", JSON.stringify({ influencerId: "pchujoy" }))
      .attach("file", testImage, "test-image.jpg")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(s3Mock).toHaveReceivedCommandTimes(PutObjectCommand, 1);
    expect(response.body).toEqual({
      imageUrl: expect.stringContaining(id),
    });
  });
});
