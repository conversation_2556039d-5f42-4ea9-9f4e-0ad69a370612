import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer";
import { planService } from "../../src/plan";
import { userService } from "../../src/user";
import { TestData, userAuthToken } from "../common";
import { eventService } from "../../src/event";
import { userEventParticipationService } from "../../src/user-event-participation";
import { productService } from "../../src/product/service";
import { userEventInviteService } from "../../src/user-event-invite";

describe("get total user event participation count", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await productService.create(TestData.product);
    await planService.create(TestData.plan);
    await eventService.create(TestData.event);
  });

  test("no participation because no recurrence or participation entry", async () => {
    const response = await request(app)
      .get("/live/user-event-participation")
      .query({
        eventId: TestData.event.id,
        userId: TestData.user.id,
      })
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(
      expect.objectContaining({
        userId: TestData.user.id,
        quantity: 0,
      }),
    );
  });

  test("gets the user participation", async () => {
    await userEventParticipationService.create({
      ...TestData.userEventParticipation,
      participationFormValues: {
        dni: "test,",
      },
    });
    const response = await request(app)
      .get("/live/user-event-participation")
      .query({
        eventId: TestData.event.id,
        userId: TestData.user.id,
      })
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      eventId: TestData.event.id,
      userId: TestData.user.id,
      quantity: 1,
      invites: [],
      participationFormValues: {
        dni: "test,",
      },
    });
  });

  test("gets the user participation with invites", async () => {
    await userEventParticipationService.create(TestData.userEventParticipation);
    const invite = await userEventInviteService.create({
      id: crypto.randomUUID(),
      eventId: TestData.event.id,
      userId: TestData.user.id,
      email: "<EMAIL>",
    });
    const response = await request(app)
      .get("/live/user-event-participation")
      .query({
        eventId: TestData.event.id,
        userId: TestData.user.id,
      })
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      eventId: TestData.event.id,
      userId: TestData.user.id,
      quantity: 1,
      invites: [
        {
          email: "<EMAIL>",
          id: invite.id,
          status: "PENDING",
        },
      ],
    });
  });
});
