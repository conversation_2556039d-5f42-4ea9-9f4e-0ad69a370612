import { transaction } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { paymentProviderEventService } from "../../src/payment-provider-event/service";
import { transactionService } from "../../src/transaction";
import { userService } from "../../src/user";
import { TestData, transientUserAuthToken } from "../common";

describe("get public transaction tests", () => {
  test("publicly gets an existing transaction by public id", async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    const transactionEntity = await transactionService.create(
      TestData.transaction,
    );
    await paymentProviderEventService.create(TestData.paymentProviderEvent);

    const queryParams: transaction.PublicGetTransactionRequest = {
      publicId: transactionEntity.publicId,
    };

    const response = await request(app)
      .get("/live/transaction/public")
      .query(queryParams)
      .set("Cookie", `session=${await transientUserAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual(
      expect.objectContaining({
        id: transactionEntity.id,
        type: transactionEntity.type,
        state: transactionEntity.state,
        amount: transactionEntity.amount,
        currency: transactionEntity.currency,
        createdAt: transactionEntity.createdAt.toISOString(),
      }),
    );
  });
});
