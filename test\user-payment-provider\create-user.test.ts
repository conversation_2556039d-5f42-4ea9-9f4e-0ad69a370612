import fetch from "node-fetch";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { userService } from "../../src/user";
import { userPaymentProviderService } from "../../src/user-payment-provider";
import { TestData, userAuthToken } from "../common";
import { mockCulqiCreateCustomer } from "../mocks";

jest.mock("node-fetch");

describe("payment.create-user", () => {
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
  test("add user on culqi", async () => {
    await influencerService.create(TestData.influencer);

    const user = await userService.create(TestData.user);

    const culqiCreateCustomerResponse = mockCulqiCreateCustomer();

    const response = await request(app)
      .post("/live/user-payment-provider")
      .set("<PERSON>ie", `session=${await userAuthToken}`);

    expect(response.status).toBe(204);
    expect(mockFetch).toHaveBeenCalledWith(
      "https://api.culqi.com/v2/customers",
      {
        body: JSON.stringify({
          email: user.email,
          address: user.line1,
          address_city: user.city,
          country_code: user.country,
          first_name: user.firstName,
          last_name: user.lastName,
          metadata: {
            userId: user.id,
          },
          phone_number: user.phone,
        }),
        headers: {
          Authorization: "Bearer eyx",
          "Content-Type": "application/json",
        },
        method: "POST",
      },
    );

    const saved = await userPaymentProviderService.get(
      "userId",
      TestData.user.id,
    );
    expect(saved?.value).toEqual(culqiCreateCustomerResponse.id);

    const response2 = await request(app)
      .post("/live/user-payment-provider")
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response2.status).toBe(204);
    expect(mockFetch).toHaveBeenCalledTimes(1);
  });
});
