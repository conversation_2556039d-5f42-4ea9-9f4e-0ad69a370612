import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { getInfluencer } from "./get-influencer";
import { createInfluencerEndpoint } from "./create-influencer";
import { listInfluencer } from "./list-influencers";
import { updateAttributesEndpoint } from "./update-attributes";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

// Influencer
router.get(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      influencer: ["READ_INFLUENCER"],
    },
    admin: {
      influencer: ["READ_INFLUENCER"],
    },
  }),
  getInfluencer,
);
router.post(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      influencer: ["PUT_INFLUENCER"],
    },
  }),
  createInfluencerEndpoint,
);
router.get(
  "/list-influencers",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      influencer: ["LIST_INFLUENCERS"],
    },
  }),
  listInfluencer,
);

router.post(
  "/attributes",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      influencer: ["PUT_INFLUENCER"],
    },
    admin: {
      influencer: ["UPDATE_INFLUENCER"],
    },
  }),
  updateAttributesEndpoint,
);

/**
 * Endpoints without authentication
 */
router.get("/public-list-influencers", listInfluencer);

router.get("/public", getInfluencer);

export const influencerRouter = router;
