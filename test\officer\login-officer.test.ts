import { hashPassword } from "@mainframe-peru/common-core";
import { officer } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { officerService } from "../../src/officer";

describe("login officer tests", () => {
  test("successfully login an officer", async () => {
    // Initial setup
    const password = "genericpassword";
    const hash = await hashPassword(password);
    const testEntity = await officerService.create({
      id: 3,
      email: "<EMAIL>",
      hash: hash,
      firstName: "Emily",
      lastName: "Tester",
      policies: {
        officer: 1,
        transaction: 3,
      },
    });

    const body: officer.LoginOfficerRequest = {
      email: testEntity.email,
      password: password,
    };

    const response = await request(app)
      .post("/live/officer/login")
      .send(body)
      .set("Origin", "https://admin.dev.pchujoy.app");

    expect(response.statusCode).toEqual(200);
    expect(response.body).toEqual(
      expect.objectContaining({
        firstName: testEntity.firstName,
        lastName: testEntity.lastName,
        policies: expect.objectContaining({
          officer: expect.objectContaining({
            PUT_OFFICER: true,
          }),
          transaction: expect.objectContaining({
            PUT_TRANSACTION: true,
            READ_TRANSACTION: true,
          }),
        }),
      }),
    );
    expect(response.header["set-cookie"][0]).toContain("Secure");
  });

  test("unsuccessfully login an officer", async () => {
    // Initial setup
    const password = "genericpassword";
    const hash = await hashPassword(password);
    const testEntity = await officerService.create({
      id: 4,
      email: "<EMAIL>",
      hash: hash,
      firstName: "Emily",
      lastName: "Tester",
      policies: {},
    });

    const body: officer.LoginOfficerRequest = {
      email: testEntity.email,
      password: "invalidpassword",
    };

    const response = await request(app).post("/live/officer/login").send(body);

    expect(response.statusCode).toEqual(401);
    expect(response.body.code).toBe("FailedLogin");
  });
});
