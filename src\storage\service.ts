import { PutObjectCommand } from "@aws-sdk/client-s3";
import { sc } from "../services";

class StorageService {
  constructor() {}

  async uploadFile(params: {
    bucket: string;
    folder?: string;
    fileName: string;
    buffer: Buffer;
    mimetype: string;
    metadata?: Record<string, string>;
  }): Promise<string> {
    const key = params.folder
      ? `${params.folder}/${params.fileName}`
      : params.fileName;
    const command = new PutObjectCommand({
      Bucket: params.bucket,
      Key: key,
      Body: params.buffer,
      ContentType: params.mimetype,
      Metadata: params.metadata,
      ACL: "public-read",
    });
    await sc.s3.send(command);
    return `https://${params.bucket}.s3.us-east-1.amazonaws.com/${key}`;
  }
}

export const storageService = new StorageService();
