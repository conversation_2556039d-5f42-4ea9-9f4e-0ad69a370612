import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { cancelRecurrence } from "./cancel-recurrence";
import { getRecurrence } from "./get-recurrence";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.get(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      recurrence: ["READ_RECURRENCE"],
    },
    admin: {
      recurrence: ["READ_RECURRENCE"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  getRecurrence,
);
router.delete(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      recurrence: ["DELETE_RECURRENCE"],
    },
    admin: {
      recurrence: ["DELETE_RECURRENCE"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  cancelRecurrence,
);

export const recurrenceRouter = router;
