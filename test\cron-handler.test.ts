import { SendMessageCommand } from "@aws-sdk/client-sqs";
import { ppe, recurrence } from "@mainframe-peru/types";
import { type ScheduledEvent } from "aws-lambda";
import { handler } from "../src/cron-handler";
import { influencerService } from "../src/influencer/service";
import { planService } from "../src/plan";
import { recurrenceService } from "../src/recurrence";
import { sc } from "../src/services";
import { userService } from "../src/user";
import { TestData } from "./common";
import { productService } from "../src/product/service";

describe("cron-handler", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await productService.create(TestData.product);
    await planService.create(TestData.plan);
  });

  const scheduledEvent: ScheduledEvent = {
    "detail-type": "Scheduled Event",
    account: "",
    detail: "",
    id: "",
    region: "",
    resources: [""],
    source: "",
    time: "",
    version: "",
  };

  test("automatic card renewal", async () => {
    const sqsMock = jest.fn();
    sc.sqs.send = sqsMock;

    await recurrenceService.create({
      ...TestData.recurrence,
      id: TestData.recurrence.id + 1,
      type: "CARD",
      endDate: undefined,
      renewalDate: new Date(2020, 0, 1),
    });
    await recurrenceService.create({
      ...TestData.recurrence,
      type: "CARD",
      endDate: undefined,
      renewalDate: new Date(),
    });

    await handler(scheduledEvent);

    expect(sqsMock).toHaveBeenCalledTimes(2);
    const command = sqsMock.mock.lastCall[0] as SendMessageCommand;
    expect(command.input).toEqual({
      MessageAttributes: {
        method: {
          DataType: "String",
          StringValue: "POST",
        },
        origin: {
          DataType: "String",
          StringValue: "core-backend",
        },
        path: {
          DataType: "String",
          StringValue: "/payment-provider/charge",
        },
      },
      MessageBody: JSON.stringify({
        amount: TestData.product.amount,
        currency: TestData.product.currency,
        invoiceDestination: "PERSON",
        userId: TestData.user.id,
        productId: TestData.product.id,
        variant: {
          variant: "RECURRENCE_RENEWAL",
          planId: TestData.plan.id,
          recurrenceId: TestData.recurrence.id,
        },
        source: {
          source: "CARD",
        },
      } as ppe.ChargeRequest),
      QueueUrl: "mainframe",
    });
  });

  test("automatic card cancellation", async () => {
    const sqsMock = jest.fn();
    sc.sqs.send = sqsMock;

    await recurrenceService.create({
      ...TestData.recurrence,
      type: "CARD",
      endDate: new Date(),
      renewalDate: new Date(),
    });

    await handler(scheduledEvent);

    expect(sqsMock).toHaveBeenCalledTimes(1);
    const command = sqsMock.mock.lastCall[0] as SendMessageCommand;
    expect(command.input).toEqual({
      MessageAttributes: {
        method: {
          DataType: "String",
          StringValue: "POST",
        },
        origin: {
          DataType: "String",
          StringValue: "core-backend",
        },
        path: {
          DataType: "String",
          StringValue: "/recurrence/cancellation",
        },
      },
      MessageBody: JSON.stringify({
        userId: TestData.user.id,
      } as recurrence.CancelRecurrenceRequest),
      QueueUrl: "mainframe",
    });
  });

  test("automatic yape cancellation", async () => {
    const sqsMock = jest.fn();
    sc.sqs.send = sqsMock;

    await recurrenceService.create({
      ...TestData.recurrence,
      type: "MANUAL",
      renewalDate: new Date(),
    });

    await handler(scheduledEvent);

    expect(sqsMock).toHaveBeenCalledTimes(1);
    const command = sqsMock.mock.lastCall[0] as SendMessageCommand;
    expect(command.input).toEqual({
      MessageAttributes: {
        method: {
          DataType: "String",
          StringValue: "POST",
        },
        origin: {
          DataType: "String",
          StringValue: "core-backend",
        },
        path: {
          DataType: "String",
          StringValue: "/recurrence/cancellation",
        },
      },
      MessageBody: JSON.stringify({
        userId: TestData.user.id,
      } as recurrence.CancelRecurrenceRequest),
      QueueUrl: "mainframe",
    });
  });
});
