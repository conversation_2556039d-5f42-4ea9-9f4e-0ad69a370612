import { Middleware } from "@koa/router";
import {
  AppError,
  AuthAdmin,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { invoice as it } from "@mainframe-peru/types";
import { emailNotification } from "../../common";
import { formatDateLocale } from "../../helpers";
import { transactionService } from "../../transaction";
import { userService } from "../../user";
import { invoiceService } from "../service";

export const sendInvoiceEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  null
> = async ({ request, response, state }) => {
  const body = it.http.SendInvoiceRequestSchema.parse(request.body);

  const transaction = await transactionService.get("id", body.transactionId);
  if (!transaction) {
    throw new AppError({
      code: "TransactionNotFound",
      message: "Could not find invoice",
      logLevel: "INFO",
      statusCode: "NOT_FOUND",
    });
  }

  const invoice = await invoiceService.get("transactionId", body.transactionId);
  if (!invoice) {
    throw new AppError({
      code: "InvoiceNotFound",
      message: "Could not find invoice",
      logLevel: "INFO",
      statusCode: "NOT_FOUND",
    });
  }
  const user = await userService.get("id", invoice.userId);
  if (!user || state.auth.influencerId !== user.influencerId) {
    throw new AppError({
      code: "InvoiceNotFound",
      message: "Could not find invoice",
      logLevel: "INFO",
      statusCode: "NOT_FOUND",
    });
  }

  await emailNotification.sendInvoiceEmail(user.email, {
    CreatedAt: formatDateLocale(invoice.createdAt),
    Name: `${user.firstName} ${user.lastName}`,
    ReceiptId: `${invoice.serie} - ${invoice.number.toString().padStart(6, "0")}`,
    Total: `${transaction.amount} ${transaction.currency}`,
    Url: invoice.link,
  });

  response.body = null;
};
