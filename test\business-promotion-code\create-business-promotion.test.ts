import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { businessPromotionService } from "../../src/business-promotion";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken } from "../common";
import { businessPromotionCodeService } from "../../src/business-promotion-code";

describe("create business promotion code tests", () => {
  test("create a business promotion code", async () => {
    await influencerService.create(TestData.influencer);
    await businessService.create(TestData.business);
    await businessPromotionService.create(TestData.businessPromotion);

    const body: bpc.CreateBusinessPromotionCodeRequest = {
      code: "ABC123",
      promotionId: TestData.businessPromotion.id,
    };
    const response = await request(app)
      .post("/live/business-promotion-code")
      .send(body)
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody: bpc.CreateBusinessPromotionCodeResponse = response.body;

    expect(response.status).toBe(200);
    expect(responseBody.code).toEqual(body.code);

    const entity = await businessPromotionCodeService.get("code", body.code);
    expect(entity).toEqual({
      id: expect.any(Number),
      code: body.code,
      promotionId: TestData.businessPromotion.id,
      createdAt: expect.any(Date),
      userId: undefined,
    });
  });
});
