import request from "supertest";
import { influencerService } from "../../../src/influencer";
import { invoiceService } from "../../../src/invoice";
import { app } from "../../../src/api-handler";
import { userService } from "../../../src/user";
import { transactionService } from "../../../src/transaction";
import { adminAuthToken, TestData, userAuthToken } from "../../common";
import { common, invoice as it } from "@mainframe-peru/types";

describe("get invoice tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
  });

  test("user should get an invoice successfully", async () => {
    // Create transaction first
    const transaction = await transactionService.create({
      ...TestData.transaction,
      state: "SUCCESS",
    });

    // Create invoice without hardcoded ID
    const invoice = await invoiceService.create({
      ...TestData.invoice,
      transactionId: transaction.id,
    });

    const response = await request(app)
      .get(`/live/invoice?transactionId=${transaction.id}`)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    const responseBody: it.http.GetInvoiceResponse = response.body;
    expect(responseBody).toEqual({
      id: invoice.id,
      transactionId: transaction.id,
      link: invoice.link,
    });
  });

  test("admin should get an invoice successfully", async () => {
    // Create transaction first
    const transaction = await transactionService.create({
      ...TestData.transaction,
      state: "SUCCESS",
    });

    // Create invoice without hardcoded ID
    const invoice = await invoiceService.create({
      ...TestData.invoice,
      transactionId: transaction.id,
    });

    const response = await request(app)
      .get(`/live/invoice?transactionId=${transaction.id}`)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    const responseBody: it.http.GetInvoiceResponse = response.body;
    expect(responseBody).toEqual({
      id: invoice.id,
      transactionId: transaction.id,
      link: invoice.link,
    });
  });

  test("should fail to get an invoice that doesn't exist", async () => {
    const response = await request(app)
      .get("/live/invoice?transactionId=999999")
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(404);
    const errorBody = response.body as common.HttpApiError;
    expect(errorBody.code).toEqual("InvoiceNotFound");
  });

  test("should fail to get an invoice that belongs to another user", async () => {
    // Create another user with different influencer
    const anotherInfluencer = await influencerService.create({
      ...TestData.influencer,
      id: "another-influencer",
    });

    const anotherUser = await userService.create({
      ...TestData.user,
      id: 999,
      email: "<EMAIL>",
      influencerId: anotherInfluencer.id,
    });

    // Create transaction for the other user
    const transaction = await transactionService.create({
      ...TestData.transaction,
      userId: anotherUser.id,
      state: "SUCCESS",
    });

    // Create invoice for the other user
    const invoice = await invoiceService.create({
      ...TestData.invoice,
      userId: anotherUser.id,
      transactionId: transaction.id,
    });

    const response = await request(app)
      .get(`/live/invoice?transactionId=${invoice.id}`)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(404);
    const errorBody = response.body as common.HttpApiError;
    expect(errorBody.code).toEqual("InvoiceNotFound");
  });

  test("should fail without authentication", async () => {
    // Create transaction first
    const transaction = await transactionService.create({
      ...TestData.transaction,
      state: "SUCCESS",
    });

    const invoice = await invoiceService.create({
      ...TestData.invoice,
      transactionId: transaction.id,
    });

    const response = await request(app).get(`/live/invoice?id=${invoice.id}`);

    expect(response.status).toBe(400);
  });
});
