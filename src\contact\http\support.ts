import { contact } from "@mainframe-peru/types";
import { Middleware } from "koa";
import { AppError } from "@mainframe-peru/common-core";
import { reCaptchaApi } from "../../ext";
import { contactService } from "../service";

export const sendSupportEndpoint: Middleware<
  unknown,
  unknown,
  boolean
> = async ({ request, response }) => {
  const body = contact.SupportRequestSchema.parse(request.body);
  if (!body.reCaptchaToken) {
    throw new AppError({
      code: "MissingReCaptcha",
      message: "User must send reCaptcha",
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  }

  await reCaptchaApi.validate(body.reCaptchaToken, request.ip);
  await contactService.sendSupport(body);

  response.body = true;
};
