import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { getAdmin } from "./get-admin";
import { listAdmins } from "./list-admins";
import { loginAdmin } from "./login-admin";
import { createAdminEndpoint } from "./create-admin";
import { deleteAdminEndpoint } from "./delete-admin";
import { updateAdminEndpoint } from "./update-admin";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

// Admin
router.get(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      admin: ["READ_ADMIN"],
    },
    admin: {
      admin: ["READ_ADMIN"],
    },
  }),
  getAdmin,
);
router.post(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      admin: ["PUT_ADMIN"],
    },
    admin: {
      admin: ["PUT_ADMIN"],
    },
  }),
  createAdminEndpoint,
);
router.put(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      admin: ["PUT_ADMIN"],
    },
    admin: {
      admin: ["PUT_ADMIN"],
    },
  }),
  updateAdminEndpoint,
);
router.get(
  "/list-admins",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      admin: ["LIST_ADMINS"],
    },
    admin: {
      admin: ["LIST_ADMINS"],
    },
  }),
  listAdmins,
);

router.delete(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      user: ["DELETE_USER"],
    },
    admin: {
      user: ["DELETE_USER"],
    },
  }),
  deleteAdminEndpoint,
);

/**
 * Endpoints without authentication
 */
router.post("/login", loginAdmin);

export const adminRouter = router;
