import {
  AppError,
  AuthAdmin,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { businessPromotion as bp } from "@mainframe-peru/types";
import { Middleware } from "koa";
import { businessService } from "../../business/service";
import { businessPromotionService } from "../service";

export const updateBusinessPromotionEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  bp.UpdateBusinessPromotionResponse
> = async ({ request, response, state }) => {
  const data = bp.UpdateBusinessPromotionRequestSchema.parse(request.body);

  const entity = await businessPromotionService.getSecure({
    id: data.id,
    influencerId: state.auth.influencerId,
  });

  const business = await businessService.get("id", entity.businessId);
  if (!business) {
    throw new AppError({
      code: "BusinessNotFound",
      message: "No se pudo encontrar el business solicitado",
      statusCode: "NOT_FOUND",
    });
  }

  const updated = await businessPromotionService.update(data.id, {
    content: data.content,
    description: data.description,
    value: data.value,
    expirationDate: data.expirationDate
      ? new Date(data.expirationDate)
      : undefined,
    status: data.status,
    name: data.name,
    imageUrl: data.imageUrl,
    termsAndConditions: data.termsAndConditions,
    category: data.category,
    group: data.group,
  });

  response.body = businessPromotionService.getPublic({
    business,
    promotion: updated,
  });
};
