import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer";
import { userService } from "../../src/user";
import { TestData, adminAuthToken } from "../common";

describe("get user count", () => {
  test("endpoint", async () => {
    await influencerService.create(TestData.influencer);
    for (let i = 0; i < 24; i++) {
      await userService.create({
        ...TestData.user,
        id: undefined,
      });
    }

    const response = await request(app)
      .get("/live/report/user-count")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      total: 24,
    });
  });
});
