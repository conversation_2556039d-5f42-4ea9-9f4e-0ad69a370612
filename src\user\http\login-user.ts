import { Middleware } from "@koa/router";
import {
  AppError,
  comparePassword,
  getJWTSetCookieHeader,
} from "@mainframe-peru/common-core";
import { user } from "@mainframe-peru/types";
import { sc } from "../../services";
import { userService } from "../service";
import { recurrenceService } from "../../recurrence";

export const loginUserEndpoint: Middleware<
  unknown,
  unknown,
  user.LoginUserResponse
> = async ({ request, response }) => {
  const body = user.LoginUserRequestSchema.parse(request.body);
  const entity = await userService.getUser(body.influencerId, body.email);

  if (!entity) {
    throw new AppError({
      logLevel: "NONE",
      code: "FailedLogin",
      message: "Usuario no existe",
      statusCode: "UNAUTHORIZED",
    });
  }

  if (entity.authenticationType !== "EMAIL") {
    throw new AppError({
      code: "InvalidAuthenticationMethod",
      message:
        "Un usuario con el correo proporcionado ya está registrado con otro método de autenticación.",
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  }

  const result = await comparePassword(body.password, entity.hash as string);

  // Successful login
  if (!result) {
    throw new AppError({
      logLevel: "NONE",
      code: "FailedLogin",
      message: "Contraseña incorrecta",
      statusCode: "UNAUTHORIZED",
    });
  }

  const activeRecurrence = await recurrenceService.getActiveRecurrence(
    entity.id,
  );

  response.set(
    "Set-Cookie",
    await getJWTSetCookieHeader({
      type: "user",
      duration: 2 * 60,
      key: sc.vars.keys.user.private,
      payload: {
        id: entity.id,
        influencerId: body.influencerId,
        email: entity.email,
        firstName: entity.firstName,
        lastName: entity.lastName,
        policies: entity.policies as Record<string, number>,
      },
    }),
  );
  response.body = user.LoginUserResponseSchema.parse({
    ...entity,
    activeRecurrence: !!activeRecurrence,
  });
};
