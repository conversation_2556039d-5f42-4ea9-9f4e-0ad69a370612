import request from "supertest";
import { app } from "../../src/api-handler";
import { TestData, userAuthToken } from "../common";
import { Policies, createJWT } from "@mainframe-peru/common-core";
import { sc } from "../../src/services";
import { userPoliciesConstant } from "@mainframe-peru/common-core/build/policies/lib/modules/user";

describe("validate session tests", () => {
  test("validates session in cookies", async () => {
    const response = await request(app)
      .get("/live/user/validate-session")
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
  });

  test("invalid session analysis", async () => {
    const response = await request(app)
      .get("/live/user/validate-session")
      .set("Cookie", `session=`);

    expect(response.status).toBe(400);
  });

  test("expired session analysis", async () => {
    const response = await request(app)
      .get("/live/user/validate-session")
      .set(
        "Cookie",
        `session=${await createJWT(
          "user",
          {
            id: TestData.user.id,
            influencerId: TestData.influencer.id,
            email: TestData.user.email,
            firstName: TestData.user.firstName || "",
            lastName: TestData.user.lastName || "",
            policies: Policies.mask(
              {
                general: {
                  REGULAR: true,
                },
              },
              userPoliciesConstant,
            ),
          },
          sc.vars.keys.user.private,
          Math.floor(Date.now() / 1000) - 10, // expires in 60 seconds
        )}`,
      );

    expect(response.status).toBe(401);
  });
});
