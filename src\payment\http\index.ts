import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { executePayment } from "./execute";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.post(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    user: {
      general: ["TRANSIENT"],
    },
  }),
  executePayment,
);

export const paymentRouter = router;
