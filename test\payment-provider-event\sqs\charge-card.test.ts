import { PublishCommand } from "@aws-sdk/client-sns";
import { SendMessageCommandInput } from "@aws-sdk/client-sqs";
import { common, ppe } from "@mainframe-peru/types";
import { Enums, HttpApiError } from "@mainframe-peru/types/build/common";
import { add, sub } from "date-fns";
import fetch from "node-fetch";
import { randomUUID } from "node:crypto";
import request from "supertest";
import { cardService } from "../../../src/card/service";
import { influencerService } from "../../../src/influencer/service";
import { paymentProviderEventService } from "../../../src/payment-provider-event/service";
import { planService } from "../../../src/plan";
import { productService } from "../../../src/product/service";
import { recurrenceService } from "../../../src/recurrence/service";
import { sc } from "../../../src/services";
import { app } from "../../../src/sqs-handler";
import { transactionService } from "../../../src/transaction/service";
import { userService } from "../../../src/user";
import { userPaymentProviderService } from "../../../src/user-payment-provider";
import { TestData } from "../../common";
import {
  mockCulqiCharge,
  mockCulqiCharge3DSResponse,
  mockCulqiChargeError,
} from "../../mocks";

jest.mock("node-fetch");
export const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe("charge-card", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await productService.create(TestData.product);
    await planService.create(TestData.plan);

    await userPaymentProviderService.create({
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      userId: TestData.user.id,
      value: "test",
    });
  });

  test("charge card and create recurrence", async () => {
    const sqsMock = jest.fn();
    sc.sqs.send = sqsMock;

    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    const card = await cardService.create({
      userId: TestData.user.id,
      brand: "Visa",
      default: true,
      number: "4111...1111",
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      token: randomUUID(),
    });
    const recurrence = await recurrenceService.create({
      ...TestData.recurrence,
      status: "CREATING",
    });

    // Mock ongoing transaction
    const transaction = await transactionService.create(TestData.transaction);

    // Mock culqi response
    mockCulqiCharge();

    // Do request
    const requestBody: ppe.ChargeRequest = {
      source: {
        source: "CARD",
        cardId: card.id,
      },
      variant: {
        variant: "RECURRENCE_CREATION",
        planId: TestData.plan.id,
        transactionId: transaction.id,
      },
      productId: TestData.product.id,
      amount: TestData.product.amount,
      currency: TestData.product.currency,
      userId: TestData.user.id,
      userUpdate: {
        city: "Mordor",
      },
      invoiceDestination: "PERSON",
    };
    const response = await request(app)
      .post("/payment-provider/charge")
      .send(requestBody);

    // Check http response
    expect(response.status).toBe(204);
    expect(response.body).toEqual({});

    // Check a new transaction has been created
    const storedTransactions = await transactionService.getTransactions({
      userId: TestData.user.id,
    });
    expect(storedTransactions?.length).toEqual(1);
    expect(storedTransactions?.[0]).toEqual({
      createdAt: expect.any(Date),
      amount: requestBody.amount,
      currency: requestBody.currency,
      id: expect.any(Number),
      publicId: expect.any(String),
      influencerId: TestData.influencer.id,
      userId: TestData.user.id,
      type: common.Enums.TransactionType.Values.RECURRENCE,
      state: common.Enums.TransactionState.Values.SUCCESS,
      channel: "CARD",
    });

    // Check provider event has been created
    const providerEvents = await paymentProviderEventService.get(
      "userId",
      TestData.user.id,
    );
    expect(providerEvents?.transactionId).toEqual(transaction.id);

    const triggerDate = add(recurrence.renewalDate, {
      days: TestData.plan.frequency,
    });
    // Check a recurrent payment has been created
    const storedRecurrence = await recurrenceService.getActiveRecurrence(
      TestData.user.id,
    );

    expect(storedRecurrence).toEqual({
      createdAt: expect.any(Date),
      userId: TestData.user.id,
      id: expect.any(Number),
      planId: TestData.plan.id,
      type: Enums.RecurrenceType.Values.CARD,
      status: Enums.RecurrenceStatus.Values.ACTIVE,
      invoiceDestination: Enums.InvoiceDestination.Values.PERSON,
      renewalDate: expect.any(Date),
      startDate: expect.any(Date),
      updatedAt: expect.any(Date),
      endDate: undefined,
    });
    const normalizeDate = (date: Date) => new Date(date.setHours(0, 0, 0, 0));
    const expectedRenewalDate = normalizeDate(triggerDate);
    expect(normalizeDate(storedRecurrence?.renewalDate as Date)).toEqual(
      expectedRenewalDate,
    );

    // Expect user to be updated
    const user = await userService.get("id", TestData.user.id);
    expect(user?.city).toEqual("Mordor");

    // Expect email to be sent
    expect(snsMock).toHaveBeenCalledTimes(1);
    const command = snsMock.mock.lastCall[0] as PublishCommand;
    expect(command.input).toEqual({
      Message: expect.any(String),
      MessageAttributes: {
        service: {
          DataType: "String",
          StringValue: "email",
        },
      },
      TopicArn: sc.vars.snsTopicArn,
    });
    expect(command.input.Message).toContain(TestData.user.email);
    expect(command.input.Message).toContain(TestData.user.firstName);
    expect(command.input.Message).toContain(
      `${TestData.influencer.name} <notification@${sc.vars.env}.pchujoy.app>`,
    );
    expect(command.input.Message).toContain("welcome");

    // Expect invoice creation event to be sent
    expect(sqsMock).toHaveBeenCalledTimes(1);
    const input = sqsMock.mock.calls[0][0].input as SendMessageCommandInput;

    expect(input.QueueUrl).toBe(sc.vars.fifoSqsQueueUrl);
    expect(input.MessageAttributes).toEqual({
      method: { DataType: "String", StringValue: "POST" },
      path: {
        DataType: "String",
        StringValue: "/invoice/create",
      },
      origin: { DataType: "String", StringValue: "core-backend" },
    });
    expect(input.MessageBody).toContain("PERSON");
    expect(input.MessageBody).toContain(storedTransactions[0].id.toString());
  });

  test("charge card with error", async () => {
    const sqsMock = jest.fn();
    sc.sqs.send = sqsMock;

    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    const card = await cardService.create({
      userId: TestData.user.id,
      brand: "Visa",
      default: true,
      number: "4111...1111",
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      token: randomUUID(),
    });
    const recurrence = await recurrenceService.create({
      ...TestData.recurrence,
      status: "CREATING",
    });

    // Mock ongoing transaction
    const transaction = await transactionService.create(TestData.transaction);

    // Mock culqi response
    mockCulqiChargeError();

    // Do request
    const requestBody: ppe.ChargeRequest = {
      source: {
        source: "CARD",
        cardId: card.id,
      },
      variant: {
        variant: "RECURRENCE_CREATION",
        planId: TestData.plan.id,
        transactionId: transaction.id,
      },
      productId: TestData.product.id,
      amount: TestData.product.amount,
      currency: TestData.product.currency,
      userId: TestData.user.id,
      userUpdate: {
        city: "Mordor",
      },
      invoiceDestination: "PERSON",
    };
    const response = await request(app)
      .post("/payment-provider/charge")
      .send(requestBody);

    // Check http response
    expect(response.status).toBe(400);
    const errorBody: HttpApiError = response.body;
    expect(errorBody.code).toEqual("CulqiError");

    // Check a new transaction has been created
    const storedTransactions = await transactionService.getTransactions({
      userId: TestData.user.id,
    });
    expect(storedTransactions?.length).toEqual(1);
    expect(storedTransactions?.[0]).toEqual({
      createdAt: expect.any(Date),
      amount: requestBody.amount,
      currency: requestBody.currency,
      id: expect.any(Number),
      publicId: expect.any(String),
      influencerId: TestData.influencer.id,
      userId: TestData.user.id,
      type: common.Enums.TransactionType.Values.RECURRENCE,
      state: common.Enums.TransactionState.Values.FAIL,
      channel: "CARD",
    });

    // Check provider event has been created
    const providerEvents = await paymentProviderEventService.get(
      "userId",
      TestData.user.id,
    );
    expect(providerEvents?.transactionId).toEqual(transaction.id);

    // Check a recurrent payment has been created
    const storedRecurrence = await recurrenceService.get("id", recurrence.id);
    expect(storedRecurrence).toBeUndefined();
  });

  test("auto renewal charge card success", async () => {
    const sqsMock = jest.fn();
    sc.sqs.send = sqsMock;

    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    const card = await cardService.create({
      userId: TestData.user.id,
      brand: "Visa",
      default: true,
      number: "4111...1111",
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      token: randomUUID(),
    });
    await recurrenceService.create(TestData.recurrence);

    // Mock culqi response
    mockCulqiCharge();

    // Do request
    const requestBody: ppe.ChargeRequest = {
      source: {
        source: "CARD",
        cardId: card.id,
      },
      variant: {
        variant: "RECURRENCE_RENEWAL",
        planId: TestData.plan.id,
        recurrenceId: TestData.recurrence.id,
      },
      productId: TestData.product.id,
      amount: TestData.product.amount,
      currency: TestData.product.currency,
      userId: TestData.user.id,
      userUpdate: {
        city: "Mordor",
      },
      invoiceDestination: "PERSON",
    };
    const response = await request(app)
      .post("/payment-provider/charge")
      .send(requestBody);

    // Check http response
    expect(response.status).toBe(204);
    expect(response.body).toEqual({});

    // Check a new transaction has been created
    const storedTransactions = await transactionService.getTransactions({
      userId: TestData.user.id,
    });
    expect(storedTransactions?.length).toEqual(1);
    expect(storedTransactions?.[0]).toEqual({
      createdAt: expect.any(Date),
      amount: requestBody.amount,
      currency: requestBody.currency,
      id: expect.any(Number),
      publicId: expect.any(String),
      influencerId: TestData.influencer.id,
      userId: TestData.user.id,
      type: common.Enums.TransactionType.Values.RECURRENCE,
      state: common.Enums.TransactionState.Values.SUCCESS,
      channel: "CARD",
    });

    // Check provider event has been created
    const providerEvents = await paymentProviderEventService.get(
      "userId",
      TestData.user.id,
    );
    expect(providerEvents?.transactionId).toEqual(storedTransactions[0].id);
  });

  test("auto renewal charge card error", async () => {
    const sqsMock = jest.fn();
    sc.sqs.send = sqsMock;

    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    const card = await cardService.create({
      userId: TestData.user.id,
      brand: "Visa",
      default: true,
      number: "4111...1111",
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      token: randomUUID(),
    });
    await recurrenceService.create(TestData.recurrence);

    // Mock culqi response
    const culqiErrorResponse = mockCulqiChargeError();

    // Do request
    const requestBody: ppe.ChargeRequest = {
      source: {
        source: "CARD",
        cardId: card.id,
      },
      variant: {
        variant: "RECURRENCE_RENEWAL",
        planId: TestData.plan.id,
        recurrenceId: TestData.recurrence.id,
      },
      productId: TestData.product.id,
      amount: TestData.product.amount,
      currency: TestData.product.currency,
      userId: TestData.user.id,
      userUpdate: {
        city: "Mordor",
      },
      invoiceDestination: "PERSON",
    };
    const response = await request(app)
      .post("/payment-provider/charge")
      .set("retry-count", "5")
      .send(requestBody);

    // Check http response
    expect(response.status).toBe(400);
    const errorBody: HttpApiError = response.body;
    expect(errorBody.code).toEqual("CulqiError");
    expect(response.get("retry")).toEqual("allow");

    // Check recurrence has been cancelled
    const storedRecurrence = await recurrenceService.get(
      "id",
      TestData.recurrence.id,
    );
    expect(storedRecurrence?.status).toEqual("PAUSED");
    expect(storedRecurrence?.endDate).toBeUndefined();

    // Check a new transaction has been created
    const storedTransactions = await transactionService.getTransactions({
      userId: TestData.user.id,
    });
    expect(storedTransactions?.length).toEqual(1);
    expect(storedTransactions?.[0]).toEqual({
      createdAt: expect.any(Date),
      amount: requestBody.amount,
      currency: requestBody.currency,
      id: expect.any(Number),
      publicId: expect.any(String),
      influencerId: TestData.influencer.id,
      userId: TestData.user.id,
      type: common.Enums.TransactionType.Values.RECURRENCE,
      state: common.Enums.TransactionState.Values.FAIL,
      channel: "CARD",
    });

    // Check provider event has been created
    const providerEvents = await paymentProviderEventService.get(
      "userId",
      TestData.user.id,
    );
    expect(providerEvents?.transactionId).toEqual(storedTransactions[0].id);
    expect(providerEvents?.state).toEqual("FAIL");
    expect(providerEvents?.responseMessage).toEqual(
      culqiErrorResponse.user_message,
    );
    expect(providerEvents?.externalId).toEqual(culqiErrorResponse.charge_id);
  });

  test("auto renewal charge card error with more than 2 days", async () => {
    const sqsMock = jest.fn();
    sc.sqs.send = sqsMock;

    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    const card = await cardService.create({
      userId: TestData.user.id,
      brand: "Visa",
      default: true,
      number: "4111...1111",
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      token: randomUUID(),
    });
    await recurrenceService.create({
      ...TestData.recurrence,
      renewalDate: sub(new Date(), { days: 3 }),
    });

    // Mock culqi response
    const culqiErrorResponse = mockCulqiChargeError();

    // Do request
    const requestBody: ppe.ChargeRequest = {
      source: {
        source: "CARD",
        cardId: card.id,
      },
      variant: {
        variant: "RECURRENCE_RENEWAL",
        planId: TestData.plan.id,
        recurrenceId: TestData.recurrence.id,
      },
      productId: TestData.product.id,
      amount: TestData.product.amount,
      currency: TestData.product.currency,
      userId: TestData.user.id,
      userUpdate: {
        city: "Mordor",
      },
      invoiceDestination: "PERSON",
    };
    const response = await request(app)
      .post("/payment-provider/charge")
      .set("retry-count", "5")
      .send(requestBody);

    // Check http response
    expect(response.status).toBe(400);
    const errorBody: HttpApiError = response.body;
    expect(errorBody.code).toEqual("CulqiError");
    expect(response.get("retry")).toEqual("allow");

    // Check recurrence has been cancelled
    const storedRecurrence = await recurrenceService.get(
      "id",
      TestData.recurrence.id,
    );
    expect(storedRecurrence?.status).toEqual("INACTIVE");
    expect(
      new Date().getTime() - (storedRecurrence?.endDate?.getTime() || 0),
    ).toBeLessThan(5000);

    // Check a new transaction has been created
    const storedTransactions = await transactionService.getTransactions({
      userId: TestData.user.id,
    });
    expect(storedTransactions?.length).toEqual(1);
    expect(storedTransactions?.[0]).toEqual({
      createdAt: expect.any(Date),
      amount: requestBody.amount,
      currency: requestBody.currency,
      id: expect.any(Number),
      publicId: expect.any(String),
      influencerId: TestData.influencer.id,
      userId: TestData.user.id,
      type: common.Enums.TransactionType.Values.RECURRENCE,
      state: common.Enums.TransactionState.Values.FAIL,
      channel: "CARD",
    });

    // Check provider event has been created
    const providerEvents = await paymentProviderEventService.get(
      "userId",
      TestData.user.id,
    );
    expect(providerEvents?.transactionId).toEqual(storedTransactions[0].id);
    expect(providerEvents?.state).toEqual("FAIL");
    expect(providerEvents?.responseMessage).toEqual(
      culqiErrorResponse.user_message,
    );
    expect(providerEvents?.externalId).toEqual(culqiErrorResponse.charge_id);
  });

  test("auto renewal charge card 3DS error", async () => {
    const sqsMock = jest.fn();
    sc.sqs.send = sqsMock;

    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    const card = await cardService.create({
      userId: TestData.user.id,
      brand: "Visa",
      default: true,
      number: "4111...1111",
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      token: randomUUID(),
    });
    await recurrenceService.create({
      ...TestData.recurrence,
      renewalDate: new Date(),
    });

    // Mock culqi response
    const culqiErrorResponse = mockCulqiCharge3DSResponse();

    // Do request
    const requestBody: ppe.ChargeRequest = {
      source: {
        source: "CARD",
        cardId: card.id,
      },
      variant: {
        variant: "RECURRENCE_RENEWAL",
        planId: TestData.plan.id,
        recurrenceId: TestData.recurrence.id,
      },
      productId: TestData.product.id,
      amount: TestData.product.amount,
      currency: TestData.product.currency,
      userId: TestData.user.id,
      userUpdate: {
        city: "Mordor",
      },
      invoiceDestination: "PERSON",
    };
    const response = await request(app)
      .post("/payment-provider/charge")
      .set("retry-count", "5")
      .send(requestBody);

    // Check http response
    expect(response.status).toBe(400);
    const errorBody: HttpApiError = response.body;
    expect(errorBody.code).toEqual("3DSAuthenticationRequired");
    expect(response.get("retry")).toBeUndefined();

    // Check recurrence has been cancelled
    const storedRecurrence = await recurrenceService.get(
      "id",
      TestData.recurrence.id,
    );
    expect(storedRecurrence?.status).toEqual("PAUSED");
    expect(storedRecurrence?.endDate).toBeUndefined();

    // Check a new transaction has been created
    const storedTransactions = await transactionService.getTransactions({
      userId: TestData.user.id,
    });
    expect(storedTransactions?.length).toEqual(1);
    expect(storedTransactions?.[0]).toEqual({
      createdAt: expect.any(Date),
      amount: requestBody.amount,
      currency: requestBody.currency,
      id: expect.any(Number),
      publicId: expect.any(String),
      influencerId: TestData.influencer.id,
      userId: TestData.user.id,
      type: common.Enums.TransactionType.Values.RECURRENCE,
      state: common.Enums.TransactionState.Values.FAIL,
      channel: "CARD",
    });

    // Check provider event has been created
    const providerEvents = await paymentProviderEventService.get(
      "userId",
      TestData.user.id,
    );
    expect(providerEvents?.transactionId).toEqual(storedTransactions[0].id);
    expect(providerEvents?.state).toEqual("FAIL");
    expect(providerEvents?.responseMessage).toEqual(
      culqiErrorResponse.user_message,
    );
    expect(providerEvents?.externalId).toBeUndefined();
  });
});
