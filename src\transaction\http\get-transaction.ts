import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { transaction as transactionT } from "@mainframe-peru/types";
import { transactionService } from "../service";

export const getTransaction: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  transactionT.GetTransactionResponse
> = async ({ params, response }) => {
  const query = transactionT.GetTransactionRequestSchema.parse(params);
  const transaction = await transactionService.get("id", query.id);

  if (!transaction) {
    throw new AppError({
      code: "TransactionNotFound",
      message: "No se pudo encontrar la transacción indicada.",
      statusCode: "NOT_FOUND",
      logLevel: "INFO",
    });
  }

  response.body = {
    id: transaction.id,
    publicId: transaction.publicId,
    state: transaction.state,
    amount: transaction.amount,
    currency: transaction.currency,
    type: transaction.type,
    createdAt: transaction.createdAt as Date,
  };
};
