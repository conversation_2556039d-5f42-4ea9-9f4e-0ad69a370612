import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer";
import { TestData } from "../common";
import { complaintService } from "../../src/complaint/service";
import { mockReCaptchaResponse } from "../mocks";
import { mockClient } from "aws-sdk-client-mock";
import "aws-sdk-client-mock-jest";
import { PublishCommand, SNSClient } from "@aws-sdk/client-sns";

jest.mock("node-fetch");
describe("Create complaint tests", () => {
  test("successfully create complaint test", async () => {
    mockReCaptchaResponse();
    const snsMock = mockClient(SNSClient);

    await influencerService.create(TestData.influencer);

    const response = await request(app)
      .post("/live/complaint/")
      .send({
        ...TestData.complaint,
        city: "Lima",
        purchaseId: "123456789",
        reCaptchaToken: "123123123",
      })
      .set("Accept", "application/json");

    expect(response.statusCode).toEqual(200);
    const complaint = await complaintService.get(
      "id",
      response.body.complaint.id,
    );

    expect(response.statusCode).toEqual(200);
    expect(complaint).toEqual({
      id: expect.any(Number),
      ageCategory: "ADULT",
      documentType: "DNI",
      documentValue: "77464582",
      email: "<EMAIL>",
      firstName: "Jhon",
      lastName: "Doe",
      phone: "902230372",
      description: "Some description",
      influencerId: "PchujoyTest",
      status: "OPENED",
      subject: "Some subject",
      type: "COMPLAINT",
      city: "Lima",
      purchaseId: "123456789",
      createdAt: expect.any(Date),
      updatedAt: expect.any(Date),
    });
    expect(snsMock).toHaveReceivedCommandTimes(PublishCommand, 2);
    expect(snsMock).toHaveReceivedCommandWith(PublishCommand, {
      Message: expect.stringContaining(TestData.complaint.email),
      MessageAttributes: {
        service: {
          DataType: "String",
          StringValue: "email",
        },
      },
    });
    expect(snsMock).toHaveReceivedCommandWith(PublishCommand, {
      Message: expect.stringContaining("<EMAIL>"),
      MessageAttributes: {
        service: {
          DataType: "String",
          StringValue: "email",
        },
      },
    });
  });

  test("failed creating complaint test", async () => {
    await influencerService.create(TestData.influencer);

    const badComplaint = {
      influencerId: "fcc",
      subject: "Some subject",
      description: "Some description",
      customerEmail: "<EMAIL>",
      customerFirstName: "Jhon",
      customerLastName: "Doe",
      type: "COMPLAINT",
      status: "OPENED",
    };

    const response = await request(app)
      .post("/live/complaint/")
      .send(badComplaint)
      .set("Accept", "application/json");

    expect(response.statusCode).toEqual(403);
  });
});
