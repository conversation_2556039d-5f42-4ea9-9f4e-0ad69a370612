import { common } from "@mainframe-peru/types";
import { attributeService } from "./attribute";
import { ValidateValuesInput } from "./attribute/service";
import { RepositoryBase } from "./repository-base";
import { ServiceBase } from "./service-base";

export type ProcessAttributeUpdateInput = {
  entityId: number | string;
  influencerId: string;
  values: ValidateValuesInput["values"];
};

export abstract class ServiceBaseWithAttributes<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  R extends RepositoryBase<any, any, Entity>,
  Entity extends {
    id: string | number;
    attributes: common.AttributeValues | null;
  },
> extends ServiceBase<R, Entity> {
  abstract get attributeEntity(): common.AttributeEntity;

  async processAttributeUpdate(
    input: ProcessAttributeUpdateInput,
  ): Promise<common.AttributeValues> {
    const { entityId, influencerId, values } = input;

    // Get influencer entity attributes to validate
    const attributes = await attributeService.list({
      entity: this.attributeEntity,
      influencerId,
    });

    // Validate received values against attributes
    const parsedAttributes = attributeService.parseValues({
      attributes,
      values,
    });

    // Check saved values to know if insert or update
    const record = await this.get("id", entityId);
    const attributesValues = record?.attributes || {};

    await this.update(entityId, {
      attributes: {
        ...attributesValues,
        ...parsedAttributes,
      },
    } as Entity);

    return parsedAttributes;
  }
}
