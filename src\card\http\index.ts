import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { addCard } from "./add-card";
import { deleteUserCard } from "./delete-card";
import { listUserCards } from "./list-cards";
import { setDefaultCard } from "./set-default-card";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.use(authMiddleware);

router.post(
  "/",
  getClientPoliciesValidationMiddleware({
    officer: {
      card: ["PUT_CARD"],
    },
    admin: {
      card: ["PUT_CARD"],
    },
    user: {
      general: ["TRANSIENT"],
    },
  }),
  addCard,
);
router.get(
  "/",
  getClientPoliciesValidationMiddleware({
    officer: {
      card: ["LIST_CARDS"],
    },
    admin: {
      card: ["LIST_CARDS"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  listUserCards,
);
router.post(
  "/delete",
  getClientPoliciesValidationMiddleware({
    officer: {
      card: ["DELETE_CARD"],
    },
    admin: {
      card: ["DELETE_CARD"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  deleteUserCard,
);
router.post(
  "/default",
  getClientPoliciesValidationMiddleware({
    officer: {
      card: ["PUT_CARD"],
    },
    admin: {
      card: ["PUT_CARD"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  setDefaultCard,
);

export const cardRouter = router;
