import {
  SendMessageCommand,
  SendMessageCommandOutput,
} from "@aws-sdk/client-sqs";
import { sc } from "../services";
import { ppe } from "@mainframe-peru/types";

export const paymentProviderQueue = {
  async sendCharge(
    request: ppe.ChargeRequest,
  ): Promise<SendMessageCommandOutput> {
    const command = new SendMessageCommand({
      QueueUrl: sc.vars.sqsQueueUrl,
      MessageBody: JSON.stringify(request),
      MessageAttributes: {
        path: {
          DataType: "String",
          StringValue: "/payment-provider/charge",
        },
        method: {
          DataType: "String",
          StringValue: "POST",
        },
        origin: {
          DataType: "String",
          StringValue: "core-backend",
        },
      },
    });
    return await sc.sqs.send(command);
  },
};
