import { faker } from "@faker-js/faker";

// 🚬
// Null-To-Undefined (NTU)
export type NTU<T> =
  T extends Array<unknown>
    ? NTU<T[number]>[]
    : T extends Record<string, unknown>
      ? {
          [P in keyof T]: NTU<T[P]>;
        }
      : T extends null
        ? undefined
        : T;

export function transformNullToUndefined<T>(data: T): NTU<T> {
  if (Array.isArray(data)) {
    return data.map(transformNullToUndefined) as unknown as NTU<T>;
  } else if (data instanceof Date) {
    // Return date objects as-is to avoid conversion issues
    return data as NTU<T>;
  } else if (data && typeof data === "object") {
    const result: Record<string, any> = {}; // eslint-disable-line @typescript-eslint/no-explicit-any
    for (const [key, value] of Object.entries(data)) {
      result[key] =
        value === null ? undefined : transformNullToUndefined(value);
    }
    return result as NTU<T>;
  }
  return data as NTU<T>;
}

export function capitalizeFirstLetter(word: string): string {
  return word.charAt(0).toUpperCase() + word.slice(1);
}

export function generateRandomAlias(): string {
  return (
    capitalizeFirstLetter(faker.word.adjective()) +
    capitalizeFirstLetter(faker.location.city()) +
    faker.number.int({ min: 1, max: 99 })
  )
    .replaceAll(" ", "")
    .replaceAll("-", "");
}

const esPeDateFormat = new Intl.DateTimeFormat("es-PE", {
  day: "2-digit",
  month: "2-digit",
  year: "numeric",
  timeZone: "America/Lima",
});

export function formatDateLocale(date: Date): string {
  return esPeDateFormat.format(date);
}

const esPeDateTimeFormat = new Intl.DateTimeFormat("es-PE", {
  day: "2-digit",
  month: "2-digit",
  year: "numeric",
  hour: "2-digit",
  minute: "2-digit",
  second: "2-digit",
  timeZone: "America/Lima",
});

export function formatDateTimeLocale(date?: Date): string {
  if (!date) return "";
  return esPeDateTimeFormat.format(date);
}

export function isImageExtensionValid(mimeType: string): boolean {
  if (
    mimeType === "image/jpeg" ||
    mimeType === "image/png" ||
    mimeType === "image/webp"
  ) {
    return true;
  }
  return false;
}
