import { Enums } from "@mainframe-peru/types/build/common";
import request from "supertest";
import { influencerService } from "../../../src/influencer";
import { invoiceService } from "../../../src/invoice";
import { planService } from "../../../src/plan";
import { app } from "../../../src/sqs-handler";
import { transactionService } from "../../../src/transaction";
import { userService } from "../../../src/user";
import { userPaymentProviderService } from "../../../src/user-payment-provider";
import { TestData } from "../../common";
import { mockNubefactResponse } from "../../mocks";
import { common } from "@mainframe-peru/types";
import { sc } from "../../../src/services";
import { PublishCommand } from "@aws-sdk/client-sns";
import { productService } from "../../../src/product/service";

jest.mock("node-fetch");

describe("create invoice tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await productService.create(TestData.product);

    await userPaymentProviderService.create({
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      userId: TestData.user.id,
      value: "test",
    });
    mockNubefactResponse();
  });

  test("should fail to create an invoice for a transaction with an invalid state", async () => {
    await planService.create(TestData.plan);

    const transaction = await transactionService.create({
      ...TestData.transaction,
      state: "PROCESSING",
    });

    const response = await request(app)
      .post("/invoice/create")
      .send({ transactionId: transaction.id });

    expect(response.status).toBe(400);
    const errorBody = response.body as common.HttpApiError;
    expect(errorBody.code).toEqual("InvalidStateTransaction");

    const storedInvoice = await invoiceService.get(
      "transactionId",
      transaction.id,
    );

    expect(storedInvoice).not.toBeDefined();
  });

  test("should create an invoice", async () => {
    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    await planService.create(TestData.plan);

    const transaction = await transactionService.create({
      ...TestData.transaction,
      currency: "PEN",
      state: "SUCCESS",
    });

    const response = await request(app)
      .post("/invoice/create")
      .send({ transactionId: transaction.id });

    expect(response.status).toBe(204);

    const storedInvoice = await invoiceService.get(
      "transactionId",
      transaction.id,
    );

    expect(storedInvoice).toBeDefined();

    // Expect email to be sent
    expect(snsMock).toHaveBeenCalledTimes(1);
    const command = snsMock.mock.lastCall[0] as PublishCommand;
    expect(command.input).toEqual({
      Message: expect.any(String),
      MessageAttributes: {
        service: {
          DataType: "String",
          StringValue: "email",
        },
      },
      TopicArn: sc.vars.snsTopicArn,
    });
    expect(command.input.Message).toContain(TestData.user.email);
    expect(command.input.Message).toContain(TestData.user.firstName);
    expect(command.input.Message).toContain(
      `Notificaciones PChuJoy <notifications@${sc.vars.env}.pchujoy.app>`,
    );
    expect(command.input.Message).toContain("invoice");
  });

  test("should create an invoice with USD currency", async () => {
    const snsMock = jest.fn();
    sc.sns.send = snsMock;
    const product = await productService.create({
      ...TestData.product,
      id: undefined,
      currency: "USD",
    });
    await planService.create({
      ...TestData.plan,
      productId: product.id,
    });
    await userService.update(TestData.user.id, {
      country: "US",
    });

    const transaction = await transactionService.create({
      ...TestData.transaction,
      currency: "USD",
      state: "SUCCESS",
    });

    const response = await request(app)
      .post("/invoice/create")
      .send({ transactionId: transaction.id });

    expect(response.status).toBe(204);

    const storedInvoice = await invoiceService.get(
      "transactionId",
      transaction.id,
    );

    expect(storedInvoice).toBeDefined();

    // Expect email to be sent
    expect(snsMock).toHaveBeenCalledTimes(1);
  });
});
