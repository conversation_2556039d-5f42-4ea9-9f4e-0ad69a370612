import { InferSelectModel, SQL, and, eq, ilike, sql } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { businessPromotionTable, businessTable } from "../schema";
import { businessPromotion as bp } from "@mainframe-peru/types";
import { sc } from "../services";
import { BusinessEntity } from "../business/repository";

export type BusinessPromotionEntity = InferSelectModel<
  typeof businessPromotionTable
>;

export type ListInput = bp.ListBusinessPromotionRequest & {
  influencerId: string;
};

class BusinessPromotionRepository extends RepositoryBase<
  typeof businessPromotionTable
> {
  constructor() {
    super(businessPromotionTable);
  }

  async listPromotions(filter: ListInput): Promise<
    {
      business: BusinessEntity;
      business_promotion: BusinessPromotionEntity;
    }[]
  > {
    const db = await sc.getDB();

    const conditions: SQL[] = [
      eq(this.table.influencerId, filter.influencerId),
    ];
    if (filter.businessId)
      conditions.push(eq(this.table.businessId, filter.businessId));
    if (filter.status) conditions.push(eq(this.table.status, filter.status));
    if (filter.type) conditions.push(eq(this.table.type, filter.type));
    if (filter.expirationDate)
      conditions.push(
        sql`${filter.expirationDate} = (${this.table.expirationDate} - interval '5 hours')::date`,
      );
    if (filter.name)
      conditions.push(ilike(this.table.name, `%${filter.name}%`));
    if (filter.description)
      conditions.push(ilike(this.table.description, `%${filter.description}%`));
    if (filter.value)
      conditions.push(ilike(this.table.value, `%${filter.value}%`));
    if (filter.content)
      conditions.push(ilike(this.table.content, `%${filter.content}%`));
    if (filter.category)
      conditions.push(eq(this.table.category, filter.category));
    if (filter.group) conditions.push(eq(this.table.group, filter.group));
    return db
      .select()
      .from(this.table)
      .innerJoin(businessTable, eq(this.table.businessId, businessTable.id))
      .where(and(...conditions))
      .orderBy(this.table.priority, this.table.name);
  }
}

export const businessPromotionRepository = new BusinessPromotionRepository();
