import { user } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { userService } from "../../src/user";
import { TestData, transientUserAuthToken } from "../common";

describe("update user tests", () => {
  test("update user", async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);

    const requestBody: user.UpdateUserRequest = {
      firstName: "Generic",
      lastName: "Name",
      phone: "928192929",
      birthDate: new Date(),
      gender: Enums.Gender.Enum.M,
      documentType: Enums.DocumentType.Enum.DNI,
      documentValue: "7777777",
      privacyConfiguration: {
        privateProfile: true,
        hideFace: true,
        allowNews: false,
        allowNotifications: false,
      },
    };

    // Update the user
    const response = await request(app)
      .put("/live/user")
      .send(requestBody)
      .set("Cookie", `session=${await transientUserAuthToken}`);

    const responseCreation: user.CreateUserResponse = response.body;
    expect(response.statusCode).toEqual(200);

    const user = await userService.get("id", responseCreation.id);

    expect(user).toEqual(expect.objectContaining(requestBody));
  });

  test("update user (with auth)", async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);

    const requestBody: user.UpdateUserRequest = {
      firstName: "Generic",
      privacyConfiguration: {
        privateProfile: false,
        allowNews: true,
      },
    };

    // Update the user
    const response = await request(app)
      .put("/live/user")
      .send(requestBody)
      .auth(await transientUserAuthToken, { type: "bearer" });

    const responseCreation: user.CreateUserResponse = response.body;
    expect(response.statusCode).toEqual(200);

    const user = await userService.get("id", responseCreation.id);

    expect(user).toEqual(expect.objectContaining(requestBody));
  });
});
