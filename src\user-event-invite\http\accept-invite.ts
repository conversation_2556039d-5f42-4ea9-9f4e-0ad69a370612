import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { userEventInvite } from "@mainframe-peru/types";
import { userEventInviteService } from "../service";

export const acceptInviteEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  userEventInvite.AcceptUserEventInviteResponse
> = async ({ request, response }) => {
  const body = userEventInvite.AcceptUserEventInviteRequestSchema.parse(
    request.body,
  );

  await userEventInviteService.acceptInvite({
    inviteId: body.id,
    participationFormValues: body.participationFormValues,
  });

  response.body = {
    success: true,
  };
};
