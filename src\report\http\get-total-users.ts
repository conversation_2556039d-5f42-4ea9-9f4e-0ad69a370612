import { Middleware } from "@koa/router";
import { AuthAdmin, AuthorizedContextState } from "@mainframe-peru/common-core";
import { report } from "@mainframe-peru/types";
import { reportService } from "../service";

export const getUserCountEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  report.GetUserResponseCount
> = async ({ response, state }) => {
  const total = await reportService.getTotalUsers(state.auth.influencerId);
  response.set("Cache-Control", "s-maxage=1800");
  response.body = {
    total,
  };
};
