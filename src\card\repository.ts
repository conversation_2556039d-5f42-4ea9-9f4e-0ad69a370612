import { and, eq, InferSelectModel } from "drizzle-orm";
import { transformNullToUndefined } from "../helpers";
import { RepositoryBase } from "../repository-base";
import { cardTable } from "../schema";
import { sc } from "../services";

export type CardEntity = InferSelectModel<typeof cardTable>;

class CardRepository extends RepositoryBase<typeof cardTable> {
  constructor() {
    super(cardTable);
  }

  async findByUserId(userId: number): Promise<CardEntity[]> {
    return transformNullToUndefined(
      await (await sc.getDB())
        .select()
        .from(this.table)
        .where(eq(this.table.userId, userId)),
    );
  }

  async getDefaultUserCard(userId: number): Promise<CardEntity | undefined> {
    const db = await sc.getDB();
    const cards = await db
      .select()
      .from(this.table)
      .where(and(eq(this.table.userId, userId), eq(this.table.default, true)));
    return cards.length === 0 ? undefined : transformNullToUndefined(cards[0]);
  }
}

export const cardRepository = new CardRepository();
