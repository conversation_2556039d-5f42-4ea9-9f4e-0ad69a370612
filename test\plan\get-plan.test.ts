import { plan } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { planService } from "../../src/plan";
import { TestData, userAuthToken } from "../common";
import { productService } from "../../src/product/service";

describe("get plan tests", () => {
  test("gets an existing plan by id", async () => {
    await influencerService.create(TestData.influencer);
    await productService.create(TestData.product);
    const testEntity = await planService.create(TestData.plan);

    const queryParams: plan.GetPlanRequest = {
      id: testEntity.id,
    };

    const response = await request(app)
      .get("/live/plan")
      .query(queryParams)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual(
      expect.objectContaining({
        id: testEntity.id,
        name: testEntity.name,
        active: testEntity.active,
        amount: TestData.product.amount,
        currency: TestData.product.currency,
        frequency: testEntity.frequency,
      }),
    );
  });
});
