import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { report } from "@mainframe-peru/types";
import { restrictParams } from "../../common";
import { reportService } from "../service";

export const getTransactionsCountByStateEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  report.TransactionCountByStateResponse
> = async ({ request, response, state }) => {
  const query = request.query
    ? report.TransactionCountByStateRequestSchema.parse(request.query)
    : {};

  restrictParams(state.auth, query);

  const transactions = await reportService.getTransactionsCountByState(query);
  response.set("Cache-Control", "s-maxage=1800");
  response.body =
    report.TransactionCountByStateResponseSchema.parse(transactions);
};
