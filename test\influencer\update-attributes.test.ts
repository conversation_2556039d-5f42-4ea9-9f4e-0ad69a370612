import { influencer } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { attributeService } from "../../src/attribute";
import { influencerService } from "../../src/influencer/service";
import { TestData, adminAuthToken } from "../common";

jest.mock("@aws-sdk/client-cloudfront");

describe("update user attributes", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
  });

  test("fill image attribute for influencer", async () => {
    const f1 = await attributeService.create({
      id: "field1",
      influencerId: TestData.influencer.id,
      entity: "INFLUENCER",
      text: "example",
      type: "FILE",
    });
    const url = "https://.s3.us-east-1.amazonaws.com/attributes/test";

    const requestBody: influencer.UpdateInfluencerAttributesRequest = {
      attributes: {
        [f1.id]: url,
      },
    };
    const response = await request(app)
      .post("/live/influencer/attributes")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    const body = response.body as influencer.UpdateInfluencerAttributesResponse;
    expect(body.attributes).toEqual({
      [f1.id]: url,
    });

    const av = await influencerService.get("id", TestData.influencer.id);
    expect(av?.attributes?.field1).toEqual(url);
  });
});
