import { InferSelectModel, and, eq, isNull, or } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { businessPromotionCodeTable } from "../schema";
import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import { sc } from "../services";

export type BusinessPromotionCodeEntity = InferSelectModel<
  typeof businessPromotionCodeTable
>;

export type InsertMultipleInput = {
  codes: string[];
  promotionId: number;
};

class BusinessPromotionCodeRepository extends RepositoryBase<
  typeof businessPromotionCodeTable
> {
  constructor() {
    super(businessPromotionCodeTable);
  }

  async deleteCodes(
    input: bpc.DeleteBusinessPromotionCodeRequest,
  ): Promise<void> {
    const db = await sc.getDB();
    if (input.all) {
      await db
        .delete(this.table)
        .where(eq(this.table.promotionId, input.promotionId));
    } else {
      await db.delete(this.table).where(eq(this.table.id, input.id));
    }
  }

  async insertMultiple(input: InsertMultipleInput): Promise<void> {
    const db = await sc.getDB();
    await db.insert(this.table).values(
      input.codes.map((c) => ({
        code: c,
        promotionId: input.promotionId,
      })),
    );
  }

  async getByUser(
    input: bpc.GetBusinessPromotionCodeRequest,
  ): Promise<BusinessPromotionCodeEntity | undefined> {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .where(
        and(
          eq(this.table.promotionId, input.promotionId),
          eq(this.table.userId, input.userId as number),
        ),
      )
      .limit(1);

    return result[0];
  }

  async getUserOrAvailableCode(input: {
    promotionId: number;
    userId: number;
  }): Promise<BusinessPromotionCodeEntity | undefined> {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .where(
        and(
          eq(this.table.promotionId, input.promotionId),
          or(isNull(this.table.userId), eq(this.table.userId, input.userId)),
        ),
      )
      .limit(1);

    return result[0];
  }
}

export const businessPromotionCodeRepository =
  new BusinessPromotionCodeRepository();
