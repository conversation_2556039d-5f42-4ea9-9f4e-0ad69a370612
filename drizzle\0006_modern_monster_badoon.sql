ALTER TABLE "core_backend"."attribute" ADD COLUMN "created_at" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "core_backend"."attribute" ADD COLUMN "updated_at" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "core_backend"."user_attribute_value" ADD COLUMN "created_at" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "core_backend"."user_attribute_value" ADD COLUMN "updated_at" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "core_backend"."user_payment_provider" ADD COLUMN "created_at" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "core_backend"."user_payment_provider" ADD COLUMN "updated_at" timestamp DEFAULT now() NOT NULL;

-- add updated at trigger on all tables with updated_at column

create or replace function update_row_trigger()
returns trigger as $$
begin
	new.updated_at = now();
 	return new;
end;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_admin_updated_at BEFORE UPDATE ON core_backend.admin FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
CREATE TRIGGER update_attribute_updated_at BEFORE UPDATE ON core_backend.attribute FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
CREATE TRIGGER update_business_updated_at BEFORE UPDATE ON core_backend.business FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
CREATE TRIGGER update_business_promotion_updated_at BEFORE UPDATE ON core_backend.business_promotion FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
CREATE TRIGGER update_card_updated_at BEFORE UPDATE ON core_backend.card FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
CREATE TRIGGER update_complaint_updated_at BEFORE UPDATE ON core_backend.complaint FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
CREATE TRIGGER update_event_updated_at BEFORE UPDATE ON core_backend.event FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
CREATE TRIGGER update_influencer_updated_at BEFORE UPDATE ON core_backend.influencer FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
CREATE TRIGGER update_invoice_updated_at BEFORE UPDATE ON core_backend.invoice FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
CREATE TRIGGER update_plan_updated_at BEFORE UPDATE ON core_backend.plan FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
CREATE TRIGGER update_recurrence_updated_at BEFORE UPDATE ON core_backend.recurrence FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
CREATE TRIGGER update_user_updated_at BEFORE UPDATE ON core_backend.user FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
CREATE TRIGGER update_user_attribute_value_updated_at BEFORE UPDATE ON core_backend.user_attribute_value FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
CREATE TRIGGER update_user_payment_provider_updated_at BEFORE UPDATE ON core_backend.user_payment_provider FOR EACH ROW EXECUTE PROCEDURE  update_row_trigger();
