import { AppError } from "@mainframe-peru/common-core";
import { BusinessEntity } from "../business/repository";
import { ServiceBase } from "../service-base";
import {
  BusinessPromotionEntity,
  businessPromotionRepository,
} from "./repository";
import { businessPromotion as bp } from "@mainframe-peru/types";

class BusinessPromotionService extends ServiceBase<
  typeof businessPromotionRepository,
  BusinessPromotionEntity
> {
  constructor() {
    super(businessPromotionRepository);
  }

  listPromotions: (typeof businessPromotionRepository)["listPromotions"] = (
    f,
  ) => this.repository.listPromotions(f);

  async getSecure(input: {
    id: number;
    influencerId: string;
  }): Promise<BusinessPromotionEntity> {
    const promotion = await this.get("id", input.id);
    if (!promotion || promotion.influencerId !== input.influencerId) {
      throw new AppError({
        code: "BusinessPromotionNotFound",
        message: "Could not find business promotion",
        statusCode: "NOT_FOUND",
        logLevel: "INFO",
      });
    }
    return promotion;
  }

  getPublic(input: {
    promotion: BusinessPromotionEntity;
    business: BusinessEntity;
  }): bp.PublicBusinessPromotionBase {
    const { promotion, business } = input;
    return {
      id: promotion.id,
      business: {
        id: business.id,
        name: business.name,
        description: business.description || undefined,
        imageUrl: business.imageUrl || undefined,
      },
      name: promotion.name,
      type: promotion.type,
      value: promotion.value,
      content: promotion.content,
      description: promotion.description || undefined,
      expirationDate: promotion.expirationDate,
      createdAt: promotion.createdAt,
      updatedAt: promotion.updatedAt,
      status: promotion.status,
      imageUrl: promotion.imageUrl || undefined,
      termsAndConditions: promotion.termsAndConditions || undefined,
    };
  }
}

export const businessPromotionService = new BusinessPromotionService();
