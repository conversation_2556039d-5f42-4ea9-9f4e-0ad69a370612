import { complaint } from "@mainframe-peru/types";
import { influencerService } from "../../src/influencer";
import { adminAuthToken, TestData } from "../common";
import { complaintService } from "../../src/complaint/service";
import request from "supertest";
import { app } from "../../src/api-handler";

describe("Update complaint test", () => {
  test("Successfully update issue", async () => {
    await influencerService.create(TestData.influencer);
    await complaintService.create(TestData.complaint);

    const updateComplaintRequest: complaint.UpdateComplaintRequest = {
      id: TestData.complaint.id,
      status: "CLOSED",
    };

    const response = await request(app)
      .put("/live/complaint/")
      .send(updateComplaintRequest)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    const complaint = await complaintService.get(
      "id",
      response.body.complaint.id,
    );

    expect(response.statusCode).toEqual(200);
    expect(complaint?.status).toEqual("CLOSED");
  });
});
