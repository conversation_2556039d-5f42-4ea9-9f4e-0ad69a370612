import { Middleware } from "@koa/router";
import {
  AuthAdmin,
  AuthorizedContextState,
  createJWT,
} from "@mainframe-peru/common-core";
import { emailNotification } from "../../common";
import { userService } from "../service";
import { OnboardFreeUsersRequestSchema } from "@mainframe-peru/types/build/user";
import { sc } from "../../services";
import { influencerService } from "../../influencer";
import { generateRandomAlias } from "../../helpers";
import { recurrenceService } from "../../recurrence";

export const onboardFreeUsersEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  null
> = async ({ response, request, state }) => {
  response.body = null;
  const body = OnboardFreeUsersRequestSchema.parse(request.body);

  const influencer = await influencerService.get("id", state.auth.influencerId);
  if (!influencer) return;

  const expirationTime = Math.floor(Date.now() / 1000) + 60 * 60000000;

  for (const newUser of body) {
    const user = await userService.create({
      influencerId: influencer.id,
      email: newUser.email,
      firstName: newUser.firstName,
      lastName: newUser.lastName,
      documentType: newUser.documentType,
      documentValue: newUser.documentValue,
      alias: generateRandomAlias(),
      policies: { general: 6 } as Record<string, number>,
      isPartner: true,
    });

    await recurrenceService.create({
      userId: user.id,
      planId: 1,
      startDate: new Date(),
      renewalDate: new Date("2300-01-01"),
      status: "ACTIVE",
      type: "MANUAL",
    });

    const token = await createJWT(
      "user",
      {
        id: 0,
        influencerId: influencer.id,
        email: user.email,
        firstName: "",
        lastName: "",
        policies: { general: 4 } as Record<string, number>, // transient
      },
      sc.vars.keys.user.private,
      expirationTime,
    );

    await emailNotification.sendOnboardEmail(user.email, influencer, {
      Name: user.firstName + " " + user.lastName,
      ResetUrl: `https://${influencer.domain}/recuperar/?token=${token}`,
    });
  }
};
