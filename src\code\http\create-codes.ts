import { Middleware } from "@koa/router";
import {
  AppError,
  AuthAdmin,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { code as ct } from "@mainframe-peru/types";
import { codeService } from "../service";

export const createCodesEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  null
> = async ({ request, response, state }) => {
  const body = ct.CreateCodesRequestSchema.parse(request.body);

  // Validate that codes array is not empty
  if (!body.codes || body.codes.length === 0) {
    throw new AppError({
      code: "EmptyCodesArray",
      message: "Codes array cannot be empty",
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  }

  // Validate that all codes are unique within the request
  const uniqueCodes = new Set(body.codes);
  if (uniqueCodes.size !== body.codes.length) {
    throw new AppError({
      code: "DuplicateCodesInRequest",
      message: "Duplicate codes found in request",
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  }

  await codeService.createBatch(
    body.codes,
    body.type,
    body.entityId,
    state.auth.influencerId,
  );

  response.body = null;
};
