import { InferSelectModel } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { attributeTable } from "../schema";

export type AttributeEntity = InferSelectModel<typeof attributeTable>;

class AttributeRepository extends RepositoryBase<typeof attributeTable> {
  constructor() {
    super(attributeTable);
  }
}

export const attributeRepository = new AttributeRepository();
