import { business as bu } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken } from "../common";

describe("list business tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    for (let i = 0; i < 100; i++) {
      await businessService.create({
        name: `test-${i}`,
        influencerId: TestData.influencer.id,
        imageUrl: crypto.randomUUID(),
        description: "description",
        ruc: "123456789",
      });
    }
  });

  test("list business", async () => {
    const response = await request(app)
      .get("/live/business/list")
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody: bu.ListBusinessResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(100);
  });

  test("list business by name", async () => {
    const query: bu.ListBusinessRequest = {
      name: "test-1",
    };

    const response = await request(app)
      .get("/live/business/list")
      .query(query)
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody: bu.ListBusinessResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(11);
  });
});
