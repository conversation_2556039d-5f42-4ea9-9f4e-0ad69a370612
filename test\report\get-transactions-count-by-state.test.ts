import { report } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { TransactionEntity } from "../../src/transaction/repository";
import { transactionService } from "../../src/transaction/service";
import { userService } from "../../src/user";
import { TestData, adminAuthToken, userAuthToken } from "../common";

describe("List Transaction", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
  });

  test("get transactions count by state", async () => {
    const ts: TransactionEntity[] = [];
    for (let i = 0; i < 10; i++) {
      const t = await transactionService.create({
        id: i + 10 ** 6,
        publicId: crypto.randomUUID(),
        userId: TestData.user.id,
        influencerId: TestData.influencer.id,
        state: Enums.TransactionState.Values.SUCCESS,
        amount: "123.45",
        currency: Enums.Currency.Values.PEN,
        type: Enums.TransactionType.Values.PURCHASE,
      });
      ts.push(t);
    }

    const response = await request(app)
      .get("/live/report/transactions-count-by-state")
      .query({
        influencerId: TestData.influencer.id,
      })
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    const body = response.body as report.TransactionCountByStateResponse;
    expect(body.length).toEqual(30);
    expect(body[29]).toEqual(
      expect.objectContaining({
        successCount: 10,
        errorCount: 0,
      }),
    );
  });
});
