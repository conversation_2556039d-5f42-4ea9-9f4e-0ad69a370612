import { Middleware } from "@koa/router";
import {
  AppError,
  AuthAdmin,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { business as bu } from "@mainframe-peru/types";
import { businessService } from "../service";

export const getBusinessEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin | AuthEndUser>,
  unknown,
  bu.GetBusinessResponse
> = async ({ request, response, state }) => {
  const params = bu.GetBusinessRequestSchema.parse(request.query);

  const entity = await businessService.get("id", params.id);

  if (!entity || entity.influencerId != state.auth.influencerId) {
    throw new AppError({
      code: "BusinessNotFound",
      message: "No se encontró el business solicitado",
      statusCode: "NOT_FOUND",
    });
  }
  response.body = bu.GetBusinessResponseSchema.parse(entity);
};
