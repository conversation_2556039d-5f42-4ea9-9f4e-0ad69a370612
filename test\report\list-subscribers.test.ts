import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { planService } from "../../src/plan";
import { productService } from "../../src/product/service";
import { recurrenceService } from "../../src/recurrence";
import { userService } from "../../src/user";
import { TestData, adminAuthToken } from "../common";

describe("List subscribers", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await productService.create(TestData.product);
    await planService.create(TestData.plan);
    await userService.create(TestData.user);
    await recurrenceService.create(TestData.recurrence);
  });

  test("list subscribers", async () => {
    const response = await request(app)
      .get("/live/report/list-subscribers")
      .query({
        influencerId: TestData.influencer.id,
      })
      .set("Cookie", `session=${await adminAuthToken}`)
      .expect("Content-Type", /text\/csv/);
    expect(response.status).toBe(200);
  });
});
