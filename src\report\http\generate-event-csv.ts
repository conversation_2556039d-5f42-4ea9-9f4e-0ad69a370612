import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { report as rt } from "@mainframe-peru/types";
import { reportService } from "../service";

export const generateEventCsv: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  rt.EventListGenerationResponse
> = async ({ request, response, state }) => {
  const body = rt.EventListGenerationRequestSchema.parse(request.body);

  const eventListUrl = await reportService.generateParticipantsCsv({
    eventId: body.id,
    influencerId:
      state.auth.iss === "mainframe:officer"
        ? body.influencerId || ""
        : state.auth.influencerId,
  });

  response.body = {
    id: body.id,
    url: eventListUrl,
  };
};
