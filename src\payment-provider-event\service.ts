import { AppError } from "@mainframe-peru/common-core";
import { card, common, ext } from "@mainframe-peru/types";
import { culqiApi } from "../ext";
import { influencerService } from "../influencer";
import { ServiceBase } from "../service-base";
import { transactionService } from "../transaction";
import { CompleteUser, userService } from "../user";
import { userPaymentProviderService } from "../user-payment-provider";
import {
  PaymentProviderEventEntity,
  paymentProviderEventRepository,
} from "./repository";

class PaymentProviderEventService extends ServiceBase<
  typeof paymentProviderEventRepository,
  PaymentProviderEventEntity
> {
  constructor() {
    super(paymentProviderEventRepository);
  }

  // Method to trigger Culqi's user creation and all its dependencies (events) in our database
  async executeUserCreation(
    userId: number,
  ): Promise<ext.culqi.CreateCustomerResponse> {
    const user = await userService.checkUserForCharge(userId);
    const influencer = await influencerService.get("id", user.influencerId);

    const culqiResponse = await culqiApi.createCustomer(
      {
        email: user.email,
        address: user.line1,
        address_city: user.city,
        country_code: user.country as common.CountryAlpha2,
        first_name: user.firstName,
        last_name: user.lastName,
        metadata: { userId: user.id },
        phone_number: user.phone,
      },
      influencer?.providersConfiguration?.CULQI?.apiKey,
    );
    const culqiCustomer = await culqiResponse.json();

    await paymentProviderEventService.create({
      userId: userId,
      type: "USER_CREATION",
      state: culqiResponse.ok ? "SUCCESS" : "FAIL",
      provider: "CULQI",
      externalId: culqiResponse.ok
        ? (culqiCustomer as ext.culqi.CreateCustomerResponse).id
        : (culqiCustomer as ext.culqi.ErrorResponse).charge_id,
      responseMessage: culqiResponse.ok
        ? undefined
        : ((culqiCustomer as ext.culqi.ErrorResponse).user_message ??
          (culqiCustomer as ext.culqi.ErrorResponse).merchant_message),
      external: culqiCustomer,
    });

    if (!culqiResponse.ok) {
      throw new AppError({
        code: "CulqiError",
        statusCode: "BAD_REQUEST",
        responseBody: culqiCustomer,
      });
    } else {
      return culqiCustomer as ext.culqi.CreateCustomerResponse;
    }
  }

  // Method to trigger Culqi's card creation and all its dependencies (events) in our database
  async executeCardCreation(
    userId: number,
    input: card.AddCardOnUserRequest,
    influencerId: string,
  ): Promise<ext.culqi.CreateCardResponse> {
    if (input.provider === "CULQI") {
      const paymentProviderId =
        await userPaymentProviderService.getUserPaymentProvider(
          userId,
          "CULQI",
        );
      if (paymentProviderId === undefined) {
        throw new AppError({
          code: "NoExternalIdOnUser",
          message: "El usuario debe guardarse primero en Culqi.",
          statusCode: "BAD_REQUEST",
        });
      }

      const influencer = await influencerService.get("id", influencerId);

      const culqiSaveCardResponse = await culqiApi.createCard(
        {
          customer_id: paymentProviderId.value,
          metadata: {},
          token_id: input.tokenId,
          validate: false,
          authentication_3DS: input.authentication_3DS,
        },
        influencer?.providersConfiguration?.CULQI?.apiKey,
      );

      const culqiCard = await culqiSaveCardResponse.json();
      await paymentProviderEventService.create({
        userId: userId,
        type: "CARD_CREATION",
        state: culqiSaveCardResponse.ok ? "SUCCESS" : "FAIL",
        provider: "CULQI",
        externalId: culqiSaveCardResponse.ok
          ? (culqiCard as ext.culqi.CreateCardResponse).id
          : (culqiCard as ext.culqi.ErrorResponse).charge_id,
        responseMessage: culqiSaveCardResponse.ok
          ? undefined
          : ((culqiCard as ext.culqi.ErrorResponse).user_message ??
            (culqiCard as ext.culqi.ErrorResponse).merchant_message),
        external: culqiCard,
      });

      if (culqiSaveCardResponse.ok) {
        if (culqiSaveCardResponse.status === 200) {
          throw new AppError({
            code: "3DSAuthenticationRequired",
            message: "Culqi está solicitando autenticación 3DS",
            logLevel: "INFO",
            statusCode: "BAD_REQUEST",
          });
        }
        return culqiCard as ext.culqi.CreateCardResponse;
      }

      throw new AppError({
        code: "CulqiSaveCardError",
        message: "Culqi falló al guardar la tarjeta del usuario",
        logLevel: "ERROR",
        statusCode: "IM_A_TEAPOT",
        data: culqiCard,
      });
    }

    throw new AppError({
      code: "UnhandledPaymentProvider",
      message: `Unknown payment provider ${input.provider}`,
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  }

  // Method to trigger Culqi's charge creation and all its dependencies (events) in our database
  async culqiChargeCreation(
    user: CompleteUser,
    amountS: string,
    currency: common.Currency,
    source: string,
    metadata: Record<string, unknown>,
    transactionId: number,
    allowRetry: boolean,
  ): Promise<ext.culqi.CreateChargeResponse> {
    let amount = 0;
    try {
      const a = amountS.replace(".", "");
      amount = parseInt(a);
    } catch {
      throw new AppError({
        code: "InvalidAmountError",
        message: "El monto dado ${body.amount} no es válido.",
        statusCode: "BAD_REQUEST",
      });
    }

    const influencer = await influencerService.get("id", user.influencerId);
    const culqiResponse = await culqiApi.createCharge(
      {
        amount,
        antifraud_details: {
          address: user.line1,
          address_city: user.city,
          country_code: user.country,
          first_name: user.firstName,
          last_name: user.lastName,
          phone_number: user.phone,
        },
        capture: true,
        currency_code: currency,
        email: user.email,
        metadata,
        source_id: source,
      },
      influencer?.providersConfiguration?.CULQI?.apiKey,
    );

    const culqiCharge = await culqiResponse.json();
    let externalId: string | undefined;
    let responseMessage: string | undefined;
    if (culqiResponse.status === 201) {
      const res = culqiCharge as ext.culqi.CreateChargeResponse;
      externalId = res.id;
      responseMessage =
        res.outcome.user_message ?? res.outcome.merchant_message;
    } else if (culqiResponse.status === 200) {
      responseMessage = (culqiCharge as unknown as ext.culqi.threeDSResponse)
        .user_message;
    } else {
      const res = culqiCharge as ext.culqi.ErrorResponse;
      externalId = res.charge_id;
      responseMessage = res.user_message ?? res.merchant_message;
    }
    const providerEvent = await paymentProviderEventService.create({
      userId: user.id,
      transactionId: transactionId,
      type: "CHARGE_CREATION",
      state: culqiResponse.status === 201 ? "SUCCESS" : "FAIL",
      provider: "CULQI",
      externalId,
      responseMessage,
      external: culqiCharge,
    });

    await transactionService.update(transactionId, {
      state: culqiResponse.status === 201 ? "SUCCESS" : "FAIL",
      lastPaymentProviderEventId: providerEvent.id,
    });

    if (culqiResponse.status === 200) {
      throw new AppError({
        code: "3DSAuthenticationRequired",
        message: "Culqi está solicitando autenticación 3DS",
        logLevel: "INFO",
        statusCode: "BAD_REQUEST",
        allowRetry: false,
      });
    }

    if (!culqiResponse.ok) {
      throw new AppError({
        code: "CulqiError",
        statusCode: "BAD_REQUEST",
        responseBody: culqiCharge,
        allowRetry,
      });
    }

    return culqiCharge as ext.culqi.CreateChargeResponse;
  }
}

export const paymentProviderEventService = new PaymentProviderEventService();
