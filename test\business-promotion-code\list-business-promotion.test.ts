import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken } from "../common";
import { businessPromotionService } from "../../src/business-promotion";
import { businessPromotionCodeService } from "../../src/business-promotion-code";

describe("list business promotion codes tests", () => {
  test("list business promotion codes", async () => {
    await influencerService.create(TestData.influencer);
    await businessService.create(TestData.business);
    await businessPromotionService.create(TestData.businessPromotion);
    for (let i = 0; i < 45; i++) {
      await businessPromotionCodeService.create({
        code: "ABC123",
        promotionId: TestData.businessPromotion.id,
      });
    }
    const query: bpc.ListBusinessPromotionCodeRequest = {
      promotionId: TestData.businessPromotion.id,
    };
    const response = await request(app)
      .get("/live/business-promotion-code/list")
      .query(query)
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody: bpc.ListBusinessPromotionCodeResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(45);
  });
});
