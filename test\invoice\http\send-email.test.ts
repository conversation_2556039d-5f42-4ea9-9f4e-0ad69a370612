import { PublishCommand } from "@aws-sdk/client-sns";
import request from "supertest";
import { app } from "../../../src/api-handler";
import { influencerService } from "../../../src/influencer";
import { invoiceService } from "../../../src/invoice";
import { sc } from "../../../src/services";
import { transactionService } from "../../../src/transaction";
import { userService } from "../../../src/user";
import { TestData, adminAuthToken } from "../../common";

jest.mock("node-fetch");

describe("create invoice tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
  });

  test("should send an invoice", async () => {
    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    const transaction = await transactionService.create({
      ...TestData.transaction,
      currency: "PEN",
      state: "SUCCESS",
    });
    await invoiceService.create(TestData.invoice);

    const response = await request(app)
      .put("/live/invoice/send")
      .set("Cookie", `session=${await adminAuthToken}`)
      .send({ transactionId: transaction.id });

    expect(response.status).toBe(204);

    // Expect email to be sent
    expect(snsMock).toHaveBeenCalledTimes(1);
    const command = snsMock.mock.lastCall[0] as PublishCommand;
    expect(command.input).toEqual({
      Message: expect.any(String),
      MessageAttributes: {
        service: {
          DataType: "String",
          StringValue: "email",
        },
      },
      TopicArn: sc.vars.snsTopicArn,
    });
    expect(command.input.Message).toContain(TestData.user.email);
    expect(command.input.Message).toContain(TestData.user.firstName);
    expect(command.input.Message).toContain(
      `Notificaciones PChuJoy <notifications@${sc.vars.env}.pchujoy.app>`,
    );
    expect(command.input.Message).toContain("invoice");
  });
});
