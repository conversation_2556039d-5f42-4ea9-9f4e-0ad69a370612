import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { createUser } from "./create-user";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.use(authMiddleware);

router.post(
  "/",
  getClientPoliciesValidationMiddleware({
    officer: {
      card: ["PUT_CARD"],
    },
    admin: {
      card: ["PUT_CARD"],
    },
    user: {
      general: ["TRANSIENT"],
    },
  }),
  createUser,
);

export const userPaymentProvideRouter = router;
