import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { getBusinessPromotionEndpoint } from "./get-business-promotion";
import { createBusinessPromotionEndpoint } from "./create-business-promotion";
import { updateBusinessPromotionEndpoint } from "./update-business-promotion";
import { deleteBusinessPromotionEndpoint } from "./delete-business-promotion";
import { listBusinessPromotionEndpoint } from "./list-business-promotion";
import { invalidateCacheEndpoint } from "./invalidate-cache";

const router = new Router();
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.get("/", getBusinessPromotionEndpoint);

router.get("/list", listBusinessPromotionEndpoint);

router.post(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      businessPromotion: ["PUT_PROMOTION"],
    },
  }),
  createBusinessPromotionEndpoint,
);

router.put(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      businessPromotion: ["PUT_PROMOTION"],
    },
  }),
  updateBusinessPromotionEndpoint,
);

router.delete(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      businessPromotion: ["DELETE_PROMOTION"],
    },
  }),
  deleteBusinessPromotionEndpoint,
);

router.post(
  "/invalidate-cache",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      businessPromotion: ["PUT_PROMOTION"],
    },
  }),
  invalidateCacheEndpoint,
);

export const businessPromotionRouter = router;
