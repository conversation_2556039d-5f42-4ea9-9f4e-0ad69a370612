import { AppError } from "@mainframe-peru/common-core";
import { common, ext } from "@mainframe-peru/types";

class ZendeskApi {
  async createOrUpdateUser(
    body: ext.zendesk.CreateOrUpdateCustomerRequest,
    zendeskConfiguration: common.ProviderConfiguration,
  ) {
    if (
      !zendeskConfiguration.email ||
      !zendeskConfiguration.apiKey ||
      !zendeskConfiguration.domain
    ) {
      throw new AppError({
        code: "InvalidZendeskConfiguration",
        message: "La configuración de Zendesk es incorrecta",
        statusCode: "INTERNAL_SERVER_ERROR",
        logLevel: "ERROR",
      });
    }
    const zendeskEmail = zendeskConfiguration.email;
    const zendeskToken = zendeskConfiguration.apiKey;

    await fetch(
      `https://${zendeskConfiguration.domain}.zendesk.com/api/v2/users/create_or_update.json`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization:
            "Basic " +
            Buffer.from(`${zendeskEmail}/token:${zendeskToken}`).toString(
              "base64",
            ),
        },
        body: JSON.stringify(body),
      },
    );
  }
}

export const zendeskApi = new ZendeskApi();
