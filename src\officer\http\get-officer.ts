import { Middleware } from "@koa/router";
import {
  AppError,
  AuthorizedContextState,
  Auth,
  Policies,
  officerPoliciesConstant,
} from "@mainframe-peru/common-core";
import { officer } from "@mainframe-peru/types";
import { officerService } from "../service";

export const getOfficer: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  officer.GetOfficerResponse
> = async ({ request, response }) => {
  const params = officer.GetOfficerRequestSchema.parse(request.query);
  const entity = await officerService.get("email", params.email);

  if (!entity) {
    throw new AppError({
      code: "OfficerNotFound",
      message: "No se encontró al oficial solicitado",
      statusCode: "NOT_FOUND",
    });
  }

  response.body = officer.GetOfficerResponseSchema.parse({
    ...entity,
    policies: officer.PoliciesSchema.parse(
      Policies.unmask(
        entity.policies as Record<string, number>,
        officerPoliciesConstant,
      ),
    ),
  });
};
