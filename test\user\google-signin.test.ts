import { user } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { userService } from "../../src/user";
import { TestData } from "../common";
import { userRepository } from "../../src/user/repository";
import { Enums } from "@mainframe-peru/types/build/common";
import { mockReCaptchaResponse } from "../mocks";

jest.mock("google-auth-library", () => {
  return {
    OAuth2Client: jest.fn().mockImplementation(() => ({
      verifyIdToken: jest.fn().mockResolvedValue({
        getPayload: jest.fn().mockReturnValue({
          sub: "1234567890",
          email: "<EMAIL>",
          given_name: "<PERSON><PERSON><PERSON><PERSON>",
          family_name: "<PERSON><PERSON><PERSON>",
          picture: "https://example.com/avatar.png",
        }),
      }),
    })),
  };
});

jest.mock("node-fetch");

describe("sign in user tests", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
  });

  test("successfully authenticating a user (creation)", async () => {
    const userRepositorySpy = jest.spyOn(userRepository, "create");

    const body: user.UserGoogleSignIn = {
      influencerId: TestData.influencer.id,
      idToken: "xxxxx",
    };

    const response = await request(app)
      .post("/live/user/google-signin")
      .send(body);

    expect(response.statusCode).toEqual(200);

    const jwt = response.header["set-cookie"][0].split("; ")[0].substring(8);
    const payload = jwt.split(".")[1];
    const parsed = JSON.parse(Buffer.from(payload, "base64").toString("utf-8"));

    expect(parsed).toEqual(
      expect.objectContaining({
        id: expect.any(Number),
        influencerId: TestData.influencer.id,
        email: TestData.user.email,
        firstName: TestData.user.firstName,
        lastName: TestData.user.lastName,
        iss: "mainframe:user",
      }),
    );

    // User has been created
    expect(userRepositorySpy).toHaveBeenCalledTimes(1);
  });

  test("successfully authenticating a user (already existing)", async () => {
    await userService.create(TestData.user);

    jest.clearAllMocks();
    const userRepositorySpy = jest.spyOn(userRepository, "create");

    const body: user.UserGoogleSignIn = {
      influencerId: TestData.influencer.id,
      idToken: "xxxxx",
    };

    const response = await request(app)
      .post("/live/user/google-signin")
      .send(body);

    expect(response.statusCode).toEqual(200);

    // User hasn't been created, already existed
    expect(userRepositorySpy).toHaveBeenCalledTimes(0);
  });

  test("trying to create a user using credentials, but was created with Google already", async () => {
    mockReCaptchaResponse();
    await userService.create({
      ...TestData.user,
      authenticationType: "GOOGLE",
    });

    const testEntity: user.CreateUserRequest = {
      influencerId: TestData.influencer.id,
      email: TestData.user.email,
      password: "1234567890",
      firstName: "Generic",
      lastName: "Name",
      phone: "928192929",
      gender: Enums.Gender.Enum.M,
      birthDate: new Date(),
      documentType: Enums.DocumentType.Enum.DNI,
      documentValue: "7777777",
      reCaptchaToken: "123123123",
    };

    // Creating the user
    const response = await request(app).post("/live/user").send(testEntity);

    expect(response.statusCode).toEqual(400);
    expect(JSON.parse(response.text)).toMatchObject({
      code: "InvalidAuthenticationMethod",
    });
  });

  test("trying to login a user using credentials, but was created with Google already", async () => {
    mockReCaptchaResponse();
    await userService.create({
      ...TestData.user,
      authenticationType: "GOOGLE",
    });

    const body: user.LoginUserRequest = {
      influencerId: TestData.influencer.id,
      email: TestData.user.email,
      password: "legitpassword",
    };

    const response = await request(app).post("/live/user/login").send(body);

    expect(response.statusCode).toEqual(400);
    expect(JSON.parse(response.text)).toMatchObject({
      code: "InvalidAuthenticationMethod",
    });
  });
});
