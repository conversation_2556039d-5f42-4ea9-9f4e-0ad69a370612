import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import { businessPromotionCodeService } from "../service";
import { businessPromotionService } from "../../business-promotion/service";
import { recurrenceService } from "../../recurrence";

export const getBusinessPromotionCodeEndpoint: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  bpc.GetBusinessPromotionCodeResponse
> = async ({ request, response, state }) => {
  const params = bpc.GetBusinessPromotionCodeRequestSchema.parse(request.query);

  const promotion = await businessPromotionService.getSecure({
    id: params.promotionId,
    influencerId: state.auth.influencerId,
  });

  const recurrence = await recurrenceService.getActiveRecurrence(
    state.auth.id,
    state.auth.influencerId,
  );
  if (!recurrence) {
    throw new AppError({
      code: "RecurrenceNotFound",
      message: "Este usuario no tiene una suscripción activa",
      statusCode: "NOT_FOUND",
      logLevel: "DEBUG",
    });
  }

  const code =
    promotion.type === "PERSONAL"
      ? await businessPromotionCodeService.getByUser({
          promotionId: params.promotionId,
          userId: state.auth.id,
        })
      : await businessPromotionCodeService.getUserOrAvailableCode({
          promotionId: params.promotionId,
          userId: state.auth.id,
        });

  if (!code) {
    throw new AppError({
      code: "CodeNotFound",
      message: "Este usuario no tiene un código de promocion asignado",
      statusCode: "NOT_FOUND",
      logLevel: "INFO",
    });
  }

  response.body = bpc.GetBusinessPromotionCodeResponseSchema.parse({
    code: code.code,
  });
};
