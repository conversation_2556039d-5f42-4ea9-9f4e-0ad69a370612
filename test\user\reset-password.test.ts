import { PublishCommand } from "@aws-sdk/client-sns";
import { user } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { sc } from "../../src/services";
import { userService } from "../../src/user";
import { TestData, transientUserAuthToken } from "../common";

describe("update user password tests", () => {
  test("reset user password", async () => {
    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);

    const requestBody: user.ResetPasswordRequest = {
      email: "    <EMAIL>  ",
      influencerId: TestData.influencer.id,
    };

    // Update the password
    const response = await request(app)
      .post("/live/user/reset-password")
      .send(requestBody);

    expect(response.statusCode).toEqual(204);
    expect(snsMock).toHaveBeenCalled();
    const command = snsMock.mock.lastCall[0] as PublishCommand;
    expect(command.input).toEqual({
      Message: expect.any(String),
      MessageAttributes: {
        service: {
          DataType: "String",
          StringValue: "email",
        },
      },
      TopicArn: sc.vars.snsTopicArn,
    });
    expect(command.input.Message).toContain(TestData.user.email);
    expect(command.input.Message).toContain(
      `notification@${sc.vars.env}.pchujoy.app`,
    );
    expect(command.input.Message).toContain(TestData.influencer.domain);
  });

  test("update user password", async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);

    const requestBody: user.UpdatePasswordRequest = {
      password: "new-password",
      token: await transientUserAuthToken,
    };

    // Update the password
    const response = await request(app)
      .put("/live/user/update-password")
      .send(requestBody);

    expect(response.statusCode).toEqual(204);

    const user = await userService.get("id", TestData.user.id);

    expect(user).toEqual(
      expect.objectContaining({
        hash: expect.any(String),
      }),
    );
  });
});
