import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { recurrenceService } from "../service";
import { restrictParams } from "../../common";
import { userService } from "../../user";
import { recurrence } from "@mainframe-peru/types";

export const cancelRecurrence: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  null
> = async ({ request, response, state }) => {
  const query = request.query
    ? recurrence.CancelRecurrenceRequestSchema.parse(request.query)
    : {};
  restrictParams(state.auth, query);
  if (!query.userId) {
    throw new AppError({
      code: "UserIdIsRequired",
      message: "Se requiere un userId",
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  }

  const user = await userService.get("id", query.userId);
  if (user?.influencerId != query.influencerId) {
    throw new AppError({
      code: "UserNotFound",
      message: "Sesión inválida",
      logLevel: "ERROR",
      statusCode: "FORBIDDEN",
    });
  }

  await recurrenceService.handleCancelFutureRecurrence(query.userId);
  response.body = null;
};
