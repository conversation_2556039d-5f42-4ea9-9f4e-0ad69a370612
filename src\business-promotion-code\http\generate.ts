import { Middleware } from "@koa/router";
import { AuthAdmin, AuthorizedContextState } from "@mainframe-peru/common-core";
import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import { businessPromotionService } from "../../business-promotion/service";
import { businessPromotionCodeService } from "../service";

export const generateCodesEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  bpc.GeneratePromotionCodesResponse
> = async ({ request, response, state }) => {
  const params = bpc.GeneratePromotionCodesRequestSchema.parse(request.body);

  await businessPromotionService.getSecure({
    id: params.promotionId,
    influencerId: state.auth.influencerId,
  });

  await businessPromotionCodeService.generateCodes({
    promotionId: params.promotionId,
    amount: params.amount,
  });
  response.body = {
    amount: params.amount,
  };
};
