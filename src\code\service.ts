import { AppError } from "@mainframe-peru/common-core";
import { ServiceBase } from "../service-base";
import { codeRepository, CodeEntity } from "./repository";
import { transactionService } from "../transaction";
import { transactionDetailService } from "../transaction-detail/service";
import { productService } from "../product/service";
import { recurrenceService } from "../recurrence";
import { planService } from "../plan";
import { CodeType } from "@mainframe-peru/types/build/common";

class CodeService extends ServiceBase<typeof codeRepository, CodeEntity> {
  constructor() {
    super(codeRepository);
  }

  async createBatch(
    codes: string[],
    type: CodeType,
    entityId: number,
    influencerId: string,
  ): Promise<void> {
    if(type === "RECURRENCE") {
      const plan = await planService.get("id", entityId);
      if (!plan || plan.influencerId !== influencerId) {
        throw new AppError({
          code: "PlanNotFound",
          message: "Plan not found or does not belong to this influencer",
          logLevel: "INFO",
          statusCode: "NOT_FOUND",
        });
      }
    }
   
    // Check for duplicate codes
    const existingCodes = await Promise.all(
      codes.map(code => this.repository.findByCodeAndInfluencer(code, influencerId))
    );

    const duplicates = codes.filter((_, index) => existingCodes[index]);
    if (duplicates.length > 0) {
      throw new AppError({
        code: "DuplicateCodes",
        message: `The following codes already exist: ${duplicates.join(", ")}`,
        logLevel: "INFO",
        statusCode: "BAD_REQUEST",
      });
    }

    const newCodes: CodeEntity[] = codes.map(code => ({
      code,
      influencerId,
      userId: null,
      redeemedDate: null,
      type: "RECURRENCE",
      entityId,
    }));

    return await this.repository.createBatch(newCodes);
  }

  async redeemCode(
    code: string,
    influencerId: string,
    userId: number,
  ): Promise<void> {
    // Find the code
    const existingCode = await this.repository.findByCodeAndInfluencer(code, influencerId);
    
    if (!existingCode) {
      throw new AppError({
        code: "CodeNotFound",
        message: "Code not found",
        logLevel: "INFO",
        statusCode: "NOT_FOUND",
      });
    }

    // Check if already redeemed
    if (existingCode.userId) {
      throw new AppError({
        code: "CodeAlreadyClaimed",
        message: "Code has already been claimed",
        logLevel: "INFO",
        statusCode: "BAD_REQUEST",
      });
    }

    if(existingCode.type === "RECURRENCE") {
      // Get the plan associated with the code
      const plan = await planService.get("id", existingCode.entityId);
      if (!plan) {
        throw new AppError({
          code: "PlanNotFound",
          message: "Plan associated with code not found",
          logLevel: "CRITICAL",
          statusCode: "INTERNAL_SERVER_ERROR",
        });
      }

      const product = await productService.get("id", plan.productId);
      if (!product) {
        throw new AppError({
          code: "ProductNotFound",
          message: "Product associated with plan not found",
          logLevel: "CRITICAL",
          statusCode: "INTERNAL_SERVER_ERROR",
        });
      }

      if(Number(product.amount) > 0) {
        throw new AppError({
          code: "ProductNotFree",
          message: "Product associated with plan is not free",
          logLevel: "INFO",
          statusCode: "BAD_REQUEST",
        });
      }

      const activeRecurrence =
        await recurrenceService.getActiveRecurrence(userId);
      if(activeRecurrence) {
        throw new AppError({
          code: "UserHasRecurrence",
          message: "El usuario de la sesión tiene un pago recurrente activo",
          logLevel: "INFO",
          statusCode: "BAD_REQUEST",
        });
      }

      const recurrence = await recurrenceService.create({
        planId: plan.id,
        renewalDate: new Date(),
        status: "ACTIVE",
        type: "CODE",
        userId,
        invoiceDestination: undefined,
      });

      // Create transaction
      const transaction = await transactionService.create({
        publicId: crypto.randomUUID(),
        userId,
        influencerId,
        amount: product.amount.toString(),
        currency: product.currency,
        state: "SUCCESS", // Code redemption is automatically successful
        type: "RECURRENCE_CODE_REDEMPTION",
      });

      // Create transaction detail
      await transactionDetailService.create({
        transactionId: transaction.id,
        entityId: recurrence.id,
        quantity: 1,
        amount: product.amount.toString(),
        productId: product.id,
      });
    }

    // Redeem the code
    const redeemedCode = await this.repository.redeemCode(
      code,
      influencerId,
      userId,
      new Date(),
    );

    if (!redeemedCode) {
      throw new AppError({
        code: "CodeRedemptionFailed",
        message: "Failed to redeem code",
        logLevel: "ERROR",
        statusCode: "INTERNAL_SERVER_ERROR",
      });
    }
  }
}

export const codeService = new CodeService();
