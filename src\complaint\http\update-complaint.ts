import {
  AppError,
  AuthAdmin,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { complaint as comp } from "@mainframe-peru/types";
import { Middleware } from "koa";
import { complaintService } from "../service";
import { transformNullToUndefined } from "../../helpers";

export const updateComplaint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  comp.UpdateComplaintResponse
> = async ({ request, response, state }) => {
  const data = comp.UpdateComplaintRequestSchema.parse(request.body);

  const complaint = await complaintService.get("id", request.body.id);

  if (!complaint) {
    throw new AppError({
      code: "ComplaintNotFounded",
      message: "No se pudo encontrar la queja o reclamo",
      statusCode: "BAD_REQUEST",
    });
  }

  if (state.auth.influencerId !== complaint.influencerId) {
    throw new AppError({
      code: "ComplaintNotFounded",
      message: "No se pudo encontrar la queja o reclamo",
      statusCode: "BAD_REQUEST",
    });
  }

  const complaintUpdated = await complaintService.update(data.id, {
    status: data.status,
  });

  response.body = {
    complaint: transformNullToUndefined(complaintUpdated),
  };
};
