DO $$ BEGIN
 CREATE TYPE "core_backend"."event_status" AS ENUM('ACTIVE', 'INACTIVE');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "core_backend"."event_type" AS ENUM('PRIZE', 'PRODUCT', 'GAME', 'IN-PERSON');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "core_backend"."event" (
	"id" serial PRIMARY KEY NOT NULL,
	"influencer_id" text NOT NULL,
	"name" varchar NOT NULL,
	"image_url" varchar NOT NULL,
	"description" varchar NOT NULL,
	"status" "core_backend"."event_status" DEFAULT 'ACTIVE' NOT NULL,
	"type" "core_backend"."event_type" NOT NULL,
	"start_date" timestamp,
	"event_date" timestamp,
	"end_date" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "core_backend"."event" ADD CONSTRAINT "event_influencer_id_influencer_id_fk" FOREIGN KEY ("influencer_id") REFERENCES "core_backend"."influencer"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
