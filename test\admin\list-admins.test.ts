import { admin } from "@mainframe-peru/types";
import request from "supertest";
import { adminService } from "../../src/admin";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer";
import { TestData, officerAuthToken } from "../common";

describe("list influencer admins tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);

    const testEntity: admin.CreateAdminRequest = {
      influencerId: TestData.influencer.id,
      email: "<EMAIL>",
      password: "123123123123123123",
      firstName: "John",
      lastName: "Tester",
      policies: {},
    };

    await adminService.processCreation({
      ...testEntity,
      influencerId: TestData.influencer.id,
    });

    const testEntity2: admin.CreateAdminRequest = {
      influencerId: TestData.influencer.id,
      email: "<EMAIL>",
      password: "123123123123123123",
      firstName: "Seb",
      lastName: "Tester",
      policies: {},
    };

    await adminService.processCreation({
      ...testEntity2,
      influencerId: TestData.influencer.id,
    });
  });

  test("Lists admins", async () => {
    const queryParams: admin.ListAdminsRequest = {
      influencerId: TestData.influencer.id,
    };

    const response = await request(app)
      .get("/live/admin/list-admins")
      .query(queryParams)
      .set("Cookie", `session=${await officerAuthToken}`);

    const responseBody = response.body as admin.ListAdminsResponse;

    expect(response.status).toBe(200);
    expect(responseBody.length).toBeGreaterThanOrEqual(2);
  });

  test("Lists admins with compound query", async () => {
    const queryParams: admin.ListAdminsRequest = {
      influencerId: TestData.influencer.id,
      compound: "se",
    };

    const response = await request(app)
      .get("/live/admin/list-admins")
      .query(queryParams)
      .set("Cookie", `session=${await officerAuthToken}`);

    const responseBody = response.body as admin.ListAdminsResponse;

    expect(response.status).toBe(200);
    expect(responseBody.length).toBeGreaterThanOrEqual(1);
  });
});
