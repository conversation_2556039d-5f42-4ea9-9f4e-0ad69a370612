import { Middleware } from "@koa/router";
import { AppError } from "@mainframe-peru/common-core";
import { ppe } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import { cardService } from "../../card";
import { invoiceQueue } from "../../invoice";
import { getPaymentExecutionVariant } from "../../payment/variants";
import { userService } from "../../user";
import { paymentProviderEventService } from "../service";

export const charge: Middleware<unknown, unknown, null> = async (ctx) => {
  const body = ctx.request.body as ppe.ChargeRequest;
  ctx.response.body = null;

  const user = await userService.checkUserForCharge(body.userId);

  const paymentExecution = getPaymentExecutionVariant({
    influencerId: user.influencerId,
    request: body,
  });

  const { transactionId } = await paymentExecution.getRunningTransaction();

  let token: string;
  if (body.source.source === "CARD") {
    const card = body.source.cardId
      ? await cardService.get("id", body.source.cardId)
      : await cardService.getDefaultUserCard(body.userId);

    if (!card) {
      throw new AppError({
        code: "CardNotFound",
        message: "No se pudo encontrar la tarjeta por ID",
        statusCode: "NOT_FOUND",
      });
    }
    if (card.userId != body.userId) {
      throw new AppError({
        code: "InvalidCardOwner",
        message: "El usuario solicitado no es el propietario de esta tarjeta",
        statusCode: "NOT_FOUND",
      });
    }

    if (card.paymentProvider !== Enums.PaymentProvider.Enum.CULQI) return;

    token = card.token;
  } else {
    token = body.source.token;
  }

  try {
    await paymentProviderEventService.culqiChargeCreation(
      {
        ...user,
        ...body.userUpdate,
      },
      body.amount,
      body.currency,
      token,
      {
        ...body.variant,
      },
      transactionId,
      paymentExecution.allowRetry,
    );

    await invoiceQueue.sendInvoiceCreation({
      transactionId,
      attempt: 0,
      invoiceDestination: body.invoiceDestination,
    });

    await paymentExecution.endCharge({ user });
  } catch (e) {
    if (
      paymentExecution.allowRetry
        ? Number(ctx.request.get("retry-count")) >= 2
        : true
    ) {
      await paymentExecution.failCharge();
    }
    throw e;
  }

  if (body.userUpdate) {
    await userService.update(user.id, body.userUpdate);
  }
};
