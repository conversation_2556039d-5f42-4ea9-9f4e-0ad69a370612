import { Auth } from "@mainframe-peru/common-core";
import { PgSelectBase } from "drizzle-orm/pg-core";
import { format } from "fast-csv";
import { Response } from "koa";

export type QueryParams = {
  influencerId?: string;
  userId?: number;
};

export function restrictParams<T extends QueryParams>(
  authState: Auth,
  query: T,
): T {
  if (authState.iss === "mainframe:user") {
    query.influencerId = authState.influencerId;
    query.userId = authState.id;
  } else if (authState.iss === "mainframe:admin") {
    query.influencerId = authState.influencerId;
  }
  return query;
}

export function addLimitAndOffsetToQuery<T>(
  filter: {
    offset?: number;
    limit?: number;
  },
  query: T,
): T {
  const q = query as PgSelectBase<"", { a: 1 }, "single">; // placeholder type
  if (filter.limit === undefined) {
    q.limit(100);
  } else if (filter.limit !== -1) {
    q.limit(filter.limit);
  }

  if (filter.offset !== undefined) {
    q.offset(filter.offset);
  }
  return query;
}

export function setCsvResponse(
  items: unknown[],
  response: Response,
  filenamePrefix: string,
): void {
  response.status = 200;
  response.set("Content-Type", "text/csv");
  response.set(
    "Content-Disposition",
    `attachment; filename="${filenamePrefix}-${new Date().toISOString()}.csv"`,
  );

  const csvStream = format({ headers: true });
  response.body = csvStream;
  csvStream.pipe(response.res);
  items.forEach((row) => csvStream.write(row));
  csvStream.end();
}

export * from "./notification";
export * as quest from "./attributes";
