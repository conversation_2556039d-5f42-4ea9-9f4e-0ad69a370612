import { common } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import {
  boolean,
  decimal,
  index,
  integer,
  jsonb,
  numeric,
  pgSchema,
  primaryKey,
  serial,
  text,
  timestamp,
  uuid,
  varchar,
} from "drizzle-orm/pg-core";

export const coreBackendSchema = pgSchema("core_backend");

export const influencerStatusEnum = coreBackendSchema.enum(
  "influencer_status",
  Enums.InfluencerStatus.options,
);
export const authenticationTypeEnum = coreBackendSchema.enum(
  "authentication_type",
  Enums.AuthenticationType.options,
);
export const genderEnum = coreBackendSchema.enum(
  "gender",
  Enums.Gender.options,
);
export const documentTypeEnum = coreBackendSchema.enum(
  "document_type",
  Enums.DocumentType.options,
);
export const eventStatusEnum = coreBackendSchema.enum(
  "event_status",
  Enums.EventStatus.options,
);
export const eventTypeEnum = coreBackendSchema.enum(
  "event_type",
  Enums.EventType.options,
);
export const transactionStateEnum = coreBackendSchema.enum(
  "transaction_state",
  Enums.TransactionState.options,
);
export const transactionTypeEnum = coreBackendSchema.enum(
  "transaction_type",
  Enums.TransactionType.options,
);
export const transactionChannelEnum = coreBackendSchema.enum(
  "transaction_channel",
  Enums.TransactionChannel.options,
);
export const invoiceDestination = coreBackendSchema.enum(
  "invoice_destination",
  Enums.InvoiceDestination.options,
);
export const paymentProviderEventStateEnum = coreBackendSchema.enum(
  "payment_provider_event_state",
  Enums.PaymentProviderEventState.options,
);
export const paymentProviderEventTypeEnum = coreBackendSchema.enum(
  "payment_provider_event_type",
  Enums.PaymentProviderEventType.options,
);
export const paymentProviderEnum = coreBackendSchema.enum(
  "payment_provider",
  Enums.PaymentProvider.options,
);
export const currencyEnum = coreBackendSchema.enum(
  "currency",
  Enums.Currency.options,
);
export const recurrenceTypeEnum = coreBackendSchema.enum(
  "recurrence_type",
  Enums.RecurrenceType.options,
);
export const recurrenceStatusSchema = coreBackendSchema.enum(
  "recurrence_status",
  Enums.RecurrenceStatus.options,
);
export const complaintTypeEnum = coreBackendSchema.enum(
  "complaint_type",
  Enums.ComplaintType.options,
);
export const complaintSupportStatusEnum = coreBackendSchema.enum(
  "complaint_support_status",
  Enums.SupportStatus.options,
);
export const complaintAgeCategory = coreBackendSchema.enum(
  "complaint_age_category",
  Enums.AgeCategory.options,
);
export const attributeType = coreBackendSchema.enum(
  "attribute_type",
  Enums.AttributeType.options,
);
export const attributeEntity = coreBackendSchema.enum(
  "attribute_entity",
  Enums.AttributeEntity.options,
);
export const businessPromotionType = coreBackendSchema.enum(
  "business_promotion_type",
  Enums.BusinessPromotionType.options,
);
export const businessPromotionStatus = coreBackendSchema.enum(
  "business_promotion_status",
  Enums.BusinessPromotionStatus.options,
);
export const businessPromotionGroup = coreBackendSchema.enum(
  "business_promotion_group",
  Enums.BusinessPromotionGroup.options,
);
export const businessPromotionCategory = coreBackendSchema.enum(
  "business_promotion_category",
  Enums.BusinessPromotionCategory.options,
);
export const eventInviteStatusEnum = coreBackendSchema.enum(
  "event_invite_status",
  Enums.EventInviteStatus.options,
);

export const officerTable = coreBackendSchema.table("officer", {
  id: serial("id").primaryKey(),
  email: text("email").notNull(),
  firstName: text("first_name").notNull(),
  lastName: text("last_name").notNull(),
  hash: text("hash").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  policies: jsonb("policies").notNull().default({}),
});

export const influencerTable = coreBackendSchema.table("influencer", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  status: influencerStatusEnum("status").default("ACTIVE").notNull(),
  logoUrl: text("logo_url").notNull(),
  transientUsers: boolean("transient_users").notNull().default(false),
  domain: text("domain").notNull(),
  attributes: jsonb("attributes").$type<common.AttributeValues>(),
  emailsConfiguration: jsonb().$type<common.EmailsConfiguration>(),
  providersConfiguration: jsonb().$type<common.ProvidersConfiguration>(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const adminTable = coreBackendSchema.table("admin", {
  id: serial("id").primaryKey(),
  influencerId: text("influencer_id")
    .references(() => influencerTable.id)
    .notNull(),
  email: text("email").notNull(),
  firstName: text("first_name"),
  lastName: text("last_name"),
  hash: text("hash"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  policies: jsonb("policies").default({}),
});

export const planTable = coreBackendSchema.table("plan", {
  id: serial("id").primaryKey(),
  // Foreign keys
  influencerId: text("influencer_id")
    .references(() => influencerTable.id)
    .notNull(),
  productId: integer("product_id")
    .references(() => productTable.id)
    .notNull(),
  name: text("name").notNull(),
  active: boolean("active").default(true),
  frequency: integer("frequency").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const userTable = coreBackendSchema.table("user", {
  id: serial("id").primaryKey(),
  influencerId: text("influencer_id")
    .references(() => influencerTable.id)
    .notNull(),
  authenticationType: authenticationTypeEnum("authentication_type").default(
    "EMAIL",
  ),
  email: text("email").notNull(),
  firstName: text("first_name").notNull(),
  lastName: text("last_name").notNull(),
  alias: text("alias").notNull(),
  hash: text("hash"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
  policies: jsonb("policies").default({}),
  phone: text("phone"),
  gender: genderEnum("gender").default("NO"),
  birthDate: timestamp("birth_date"),
  documentType: documentTypeEnum("document_type"),
  documentValue: text("document_value"),
  companyId: text("company_id"),
  // TODO: move the address info to another table?
  country: text("country"),
  city: text("city"),
  province: text("province"),
  district: text("district"),
  zipCode: text("zip_code"),
  line1: text("line1"),
  line2: text("line2"),
  attributes: jsonb("attributes").$type<common.AttributeValues>(),
  isPartner: boolean("is_partner"),
});

export const userPaymentProviderTable = coreBackendSchema.table(
  "user_payment_provider",
  {
    userId: integer("user_id")
      .references(() => userTable.id)
      .notNull(),
    paymentProvider: paymentProviderEnum("payment_provider").notNull(),
    value: text("value").notNull(),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
);

export const transactionTable = coreBackendSchema.table(
  "transaction",
  {
    id: serial("id").primaryKey(),
    publicId: uuid("public_id").notNull(),
    userId: integer("user_id")
      .references(() => userTable.id)
      .notNull(),
    influencerId: text("influencer_id")
      .references(() => influencerTable.id)
      .notNull(),
    type: transactionTypeEnum("type").notNull(),
    state: transactionStateEnum("state").notNull(),
    channel: transactionChannelEnum("channel").notNull().default("CARD"),
    amount: decimal("amount").notNull(),
    currency: currencyEnum("currency").notNull(),
    lastPaymentProviderEventId: integer("last_payment_provider_event_id"),
    createdAt: timestamp("created_at").notNull().defaultNow(),
  },
  (table) => {
    return {
      publicIdIndex: index("transaction_public_id_index").on(table.publicId),
    };
  },
);

export const transactionDetailTable = coreBackendSchema.table(
  "transaction_detail",
  {
    id: serial("id").primaryKey(),
    transactionId: integer("transaction_id")
      .references(() => transactionTable.id)
      .notNull(),
    amount: decimal("amount").notNull(),
    productId: integer("product_id")
      .references(() => productTable.id)
      .notNull(),
    entityId: integer("entity_id").notNull(),
    quantity: integer("quantity").notNull(),
  },
);

export const paymentProviderEventTable = coreBackendSchema.table(
  "payment_provider_event",
  {
    id: serial("id").primaryKey(),
    userId: integer("user_id")
      .references(() => userTable.id)
      .notNull(),
    transactionId: integer("transaction_id").references(
      () => transactionTable.id,
    ),
    state: paymentProviderEventStateEnum("state").notNull(),
    provider: paymentProviderEnum("provider"),
    type: paymentProviderEventTypeEnum("type"),
    externalId: text("external_id"),
    responseMessage: text("response_message"),
    external: jsonb("external"),
    createdAt: timestamp("created_at").notNull().defaultNow(),
  },
);

export const recurrenceTable = coreBackendSchema.table("recurrence", {
  id: serial("id").primaryKey(),
  type: recurrenceTypeEnum("type").notNull(),
  status: recurrenceStatusSchema("status").notNull(),
  userId: integer("user_id")
    .references(() => userTable.id)
    .notNull(),
  planId: integer("plan_id")
    .references(() => planTable.id)
    .notNull(),
  startDate: timestamp("start_date").notNull().defaultNow(),
  renewalDate: timestamp("renewal_date").notNull(),
  endDate: timestamp("end_date"),
  invoiceDestination: invoiceDestination("invoice_destination")
    .notNull()
    .default("PERSON"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const cardTable = coreBackendSchema.table("card", {
  id: serial("id").primaryKey(),
  userId: integer("user_id")
    .references(() => userTable.id)
    .notNull(),
  default: boolean("default").notNull(),
  paymentProvider: paymentProviderEnum("payment_provider").notNull(),
  token: text("token").notNull(),
  number: text("number").notNull(),
  brand: text("brand").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const invoiceTable = coreBackendSchema.table("invoice", {
  id: serial("id").primaryKey(),
  transactionId: integer("transaction_id")
    .references(() => transactionTable.id)
    .notNull(),
  userId: integer("user_id")
    .references(() => userTable.id)
    .notNull(),
  invoiceOrigin: integer("invoice_origin").notNull(),
  voucherType: integer("voucher_type").notNull(),
  serie: text("serie").notNull(),
  number: integer("number").notNull(),
  link: text("link").notNull(),
  acceptedBySunat: integer("accepted_by_sunat").notNull(),
  sunatDescription: text("sunat_description").notNull(),
  sunatNote: text("sunat_note"),
  sunatResponseCode: text("sunat_responsecode").notNull(),
  sunatSoapError: text("sunat_soap_error"),
  stringQrCode: text("string_qr_code").notNull(),
  hashCode: text("hash_code").notNull(),
  payload: jsonb("payload"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const complaintTable = coreBackendSchema.table("complaint", {
  id: serial("id").primaryKey(),
  influencerId: text("influencer_id")
    .notNull()
    .references(() => influencerTable.id),
  firstName: varchar("first_name").notNull(),
  lastName: varchar("last_name").notNull(),
  email: varchar("email").notNull(),
  phone: varchar("phone").notNull(),
  documentType: varchar("document_type").notNull(),
  documentValue: varchar("document_value").notNull(),
  ageCategory: complaintAgeCategory("age_category").notNull(),
  status: complaintSupportStatusEnum().default("OPENED").notNull(),
  type: complaintTypeEnum().default("COMPLAINT").notNull(),
  subject: varchar().notNull(),
  description: varchar().notNull(),
  country: varchar(),
  region: varchar(),
  city: varchar(),
  district: varchar(),
  address: varchar(),
  purchaseId: varchar(),
  purchaseAmount: varchar(),
  purchaseCurrency: varchar(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const eventTable = coreBackendSchema.table("event", {
  id: serial("id").primaryKey(),
  influencerId: text("influencer_id")
    .notNull()
    .references(() => influencerTable.id),
  name: varchar("name").notNull(),
  imageUrl: varchar("image_url").notNull(),
  description: varchar("description").notNull(),
  status: eventStatusEnum("status").notNull().default("ACTIVE"),
  type: eventTypeEnum("type").notNull(),
  startDate: timestamp("start_date"),
  eventDate: timestamp("event_date"),
  endDate: timestamp("end_date"),
  priority: integer("priority"),
  location: text("location"),
  participationProductId: integer("participation_product_id").references(
    () => productTable.id,
  ),
  participationForm: jsonb().$type<common.AttributeKey[]>(),
  inviteLimit: integer("invite_limit"),
  inviteForm: jsonb("invite_form").$type<common.AttributeKey[]>(),
  eventListUrl: text("event_list_url"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const attributeTable = coreBackendSchema.table(
  "attribute",
  {
    id: text("id").notNull(),
    influencerId: text("influencer_id")
      .notNull()
      .references(() => influencerTable.id),
    description: text("description"),
    text: text("text").notNull(),
    type: attributeType("type").notNull(),
    entity: attributeEntity("entity").notNull().default("USER"),
    options: jsonb().$type<
      {
        id: string;
        value: string;
      }[]
    >(),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
  (table) => [primaryKey({ columns: [table.id, table.influencerId] })],
);

export const businessTable = coreBackendSchema.table("business", {
  id: serial("id").primaryKey(),
  influencerId: text("influencer_id")
    .notNull()
    .references(() => influencerTable.id),
  name: text("name").notNull(),
  description: text("description"),
  ruc: text("ruc"),
  imageUrl: text("image_url"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const businessPromotionTable = coreBackendSchema.table(
  "business_promotion",
  {
    id: serial("id").primaryKey(),
    influencerId: text("influencer_id")
      .notNull()
      .references(() => influencerTable.id),
    businessId: integer("business_id")
      .notNull()
      .references(() => businessTable.id),
    name: text("name").notNull(),
    type: businessPromotionType("type").notNull(),
    status: businessPromotionStatus("status").notNull(),
    description: text("description"),
    value: text("value").notNull(),
    content: text("content").notNull(),
    termsAndConditions: text("terms_and_conditions"),
    expirationDate: timestamp("expirationDate").notNull(),
    imageUrl: text("image_url"),
    group: businessPromotionGroup("group"),
    category: businessPromotionCategory("category"),
    priority: integer("priority"),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
);

export const businessPromotionCodeTable = coreBackendSchema.table(
  "business_promotion_code",
  {
    id: serial("id").primaryKey(),
    promotionId: integer("promotion_id")
      .notNull()
      .references(() => businessPromotionTable.id),
    userId: integer("user_id").references(() => userTable.id),
    code: text("code").notNull(),
    createdAt: timestamp("created_at").notNull().defaultNow(),
  },
  (table) => [index("promotion_code_idx").on(table.promotionId, table.code)],
);

export const userEventParticipationTable = coreBackendSchema.table(
  "user_event_participation",
  {
    userId: integer("user_id")
      .references(() => userTable.id)
      .notNull(),
    eventId: integer("event_id")
      .references(() => eventTable.id)
      .notNull(),
    quantity: integer("quantity").notNull(),
    participationFormValues: jsonb().$type<common.AttributeValues>(),
  },
  (table) => [primaryKey({ columns: [table.userId, table.eventId] })],
);

export const userEventInviteTable = coreBackendSchema.table(
  "user_event_invite",
  {
    id: uuid("id").primaryKey().notNull(),
    userId: integer("user_id")
      .references(() => userTable.id)
      .notNull(),
    eventId: integer("event_id")
      .references(() => eventTable.id)
      .notNull(),
    email: text("email").notNull(),
    participationFormValues: jsonb().$type<common.AttributeValues>(),
    status: eventInviteStatusEnum("status").notNull().default("PENDING"),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    acceptedAt: timestamp("accepted_at"),
  },
  (table) => [
    index("user_event_invite_email_event_idx").on(table.email, table.eventId),
  ],
);

export const productTable = coreBackendSchema.table("product", {
  id: serial("id").primaryKey(),
  influencerId: text("influencer_id")
    .notNull()
    .references(() => influencerTable.id),
  amount: numeric("amount").notNull(),
  currency: currencyEnum("currency").notNull(),
  description: text("description"),
  sunatCode: text("sunat_code"),
});
