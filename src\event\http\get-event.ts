import { Middleware } from "@koa/router";
import { AppError } from "@mainframe-peru/common-core";
import { event } from "@mainframe-peru/types";
import { eventService } from "../service";

export const getEventEndpoint: Middleware<
  unknown,
  unknown,
  event.GetEventResponse
> = async ({ request, response }) => {
  const params = event.GetEventRequestSchema.parse(request.query);

  const entity = await eventService.get("id", params.id);

  if (!entity) {
    throw new AppError({
      code: "EventNotFound",
      message: "No se encontró el evento solicitado",
      statusCode: "NOT_FOUND",
    });
  }

  response.set("Cache-Control", "s-maxage=1800");
  response.body = event.GetEventResponseSchema.parse(entity);
};
