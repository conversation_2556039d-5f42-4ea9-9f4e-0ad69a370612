import { user } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { userService } from "../../src/user";
import { TestData, officerAuthToken } from "../common";

describe("delete user tests", () => {
  test("deletes an existing user by id", async () => {
    await influencerService.create(TestData.influencer);
    const testEntity = await userService.create(TestData.user);

    const queryParams: user.DeleteUserRequest = {
      id: testEntity.id,
    };

    const response = await request(app)
      .delete("/live/user")
      .query(queryParams)
      .set("Cookie", `session=${await officerAuthToken}`);

    expect(response.status).toBe(200);

    const user = await userService.get("id", testEntity.id);

    expect(user).toBeUndefined();
  });
});
