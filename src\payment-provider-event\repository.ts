import { InferSelectModel } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { paymentProviderEventTable } from "../schema";

export type PaymentProviderEventEntity = InferSelectModel<
  typeof paymentProviderEventTable
>;

class PaymentProviderEventRepository extends RepositoryBase<
  typeof paymentProviderEventTable
> {
  constructor() {
    super(paymentProviderEventTable);
  }
}

export const paymentProviderEventRepository =
  new PaymentProviderEventRepository();
