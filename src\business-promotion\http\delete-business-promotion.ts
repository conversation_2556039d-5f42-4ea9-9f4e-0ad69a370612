import { Middleware } from "@koa/router";
import {
  AuthAdmin,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { businessPromotion as bp } from "@mainframe-peru/types";
import { businessPromotionService } from "../service";

export const deleteBusinessPromotionEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin | AuthEndUser>,
  unknown,
  null
> = async ({ request, response, state }) => {
  const params = bp.DeleteBusinessPromotionRequestSchema.parse(request.query);
  await businessPromotionService.getSecure({
    id: params.id,
    influencerId: state.auth.influencerId,
  });
  await businessPromotionService.delete(params.id);

  response.body = null;
};
