import { Middleware } from "@koa/router";
import {
  AppError,
  comparePassword,
  createJWT,
} from "@mainframe-peru/common-core";
import { admin } from "@mainframe-peru/types";
import { sc } from "../../services";
import { adminService } from "../service";
import { AdminEntity } from "../repository";

export const loginAdmin: Middleware<
  unknown,
  unknown,
  admin.LoginAdminResponse
> = async ({ request, response }) => {
  const body = admin.LoginAdminRequestSchema.parse(request.body);
  const dbResult = await adminService.getAdmin(body.influencerId, body.email);

  if (!dbResult) {
    throw new AppError({
      logLevel: "NONE",
      code: "FailedLogin",
      message: "El admin no existe",
      statusCode: "UNAUTHORIZED",
    });
  }
  const entity = dbResult as AdminEntity;

  const result = await comparePassword(body.password, entity.hash as string);

  // Successful login
  if (!result) {
    throw new AppError({
      logLevel: "NONE",
      code: "FailedLogin",
      message: "Contraseña incorrecta",
      statusCode: "UNAUTHORIZED",
    });
  }
  const expirationTime = Math.floor(Date.now() / 1000) + 2 * 60 * 60; // expires in 2 hours
  response.set(
    "Set-Cookie",
    `session=${await createJWT(
      "admin",
      {
        id: entity.id,
        influencerId: entity.influencerId,
        email: entity.email,
        firstName: entity.firstName as string,
        lastName: entity.lastName as string,
        policies: entity.policies as Record<string, number>,
      },
      sc.vars.keys.admin.private,
      expirationTime, // expires in 2 hours
    )}; Max-Age=${expirationTime}; Secure; SameSite=Strict; Path=/; HttpOnly`,
  );

  response.body = admin.LoginAdminResponseSchema.parse({
    ...entity,
    sessionExp: expirationTime,
  });
};
