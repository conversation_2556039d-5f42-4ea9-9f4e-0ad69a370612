import { Middleware } from "@koa/router";
import {
  AppError,
  getClientTypeFrom<PERSON><PERSON>ie,
  getJWTSetCookieHeader,
} from "@mainframe-peru/common-core";
import { user as ut } from "@mainframe-peru/types";
import { sc } from "../../services";
import { userService } from "../service";
import { reCaptchaApi } from "../../ext";

export const createUserEndpoint: Middleware<
  unknown,
  unknown,
  ut.CreateUserResponse
> = async ({ request, response }) => {
  const body = ut.CreateUserRequestSchema.parse(request.body);
  const clientType = getClientTypeFromCookie(request.header.cookie);

  if (!clientType || clientType === "user") {
    if (!body.reCaptchaToken) {
      throw new AppError({
        code: "MissingReCaptcha",
        message: "User must send reCaptcha",
        logLevel: "INFO",
        statusCode: "BAD_REQUEST",
      });
    }

    await reCaptchaApi.validate(body.reCaptchaToken, request.ip);
  }

  const user = await userService.processCreation(body);
  response.body = {
    id: user.id,
    isNew: true,
  };

  if (!clientType || clientType === "user") {
    response.set(
      "Set-Cookie",
      await getJWTSetCookieHeader({
        type: "user",
        duration: 2 * 60,
        key: sc.vars.keys.user.private,
        payload: {
          id: user.id,
          influencerId: body.influencerId,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          policies: user.policies as Record<string, number>,
        },
      }),
    );
  }
};
