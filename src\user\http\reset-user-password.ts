import { Middleware } from "@koa/router";
import {
  AuthEndUser,
  AuthorizedContextState,
  createJWT,
} from "@mainframe-peru/common-core";
import { emailNotification } from "../../common";
import { userService } from "../service";
import { ResetPasswordRequestSchema } from "@mainframe-peru/types/build/user";
import { sc } from "../../services";
import { influencerService } from "../../influencer";

export const resetPasswordEndpoint: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  null
> = async ({ response, request }) => {
  response.body = null;
  const body = ResetPasswordRequestSchema.parse(request.body);
  const user = await userService.getUser(body.influencerId, body.email);
  if (!user) {
    return;
  }

  const expirationTime = Math.floor(Date.now() / 1000) + 60 * 60;
  const token = await createJWT(
    "user",
    {
      id: 0,
      influencerId: body.influencerId,
      email: user.email,
      firstName: "",
      lastName: "",
      policies: { general: 4 } as Record<string, number>, // Transient
    },
    sc.vars.keys.user.private,
    expirationTime,
  );

  const influencer = await influencerService.get("id", body.influencerId);
  if (!influencer) return;

  await emailNotification.sendResetPassword(user.email, influencer, {
    Name: user.firstName + " " + user.lastName,
    ResetUrl: `https://${influencer.domain}/recuperar/?token=${token}`,
  });
};
