import {
  AppError,
  AuthAdmin,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { business as bu } from "@mainframe-peru/types";
import { Middleware } from "koa";
import { isImageExtensionValid, transformNullToUndefined } from "../../helpers";
import { sc } from "../../services";
import { storageService } from "../../storage/service";
import { businessService } from "../service";

export const updateBusinessEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  bu.UpdateBusinessResponse
> = async ({ request, response, state }) => {
  const data = bu.UpdateBusinessRequestSchema.parse(request.body);

  const entity = await businessService.get("id", data.id);

  if (!entity) {
    throw new AppError({
      code: "BusinessNotFound",
      message: "No se pudo encontrar el business solicitado",
      statusCode: "BAD_REQUEST",
    });
  }

  if (entity.influencerId !== state.auth.influencerId) {
    throw new AppError({
      code: "BusinessInvalid",
      message: "Business inválido",
      statusCode: "BAD_REQUEST",
    });
  }

  let imageUrl: undefined | string = undefined;
  if (request.file) {
    if (!isImageExtensionValid(request.file.mimetype)) {
      throw new AppError({
        code: "InvalidImage",
        message: "La imagen no tiene una extensión válida.",
        logLevel: "ERROR",
        statusCode: "BAD_REQUEST",
      });
    }
    imageUrl = await storageService.uploadFile({
      bucket: sc.vars.modulesStorageBucket,
      folder: "business",
      fileName: crypto.randomUUID(),
      buffer: request.file.buffer,
      mimetype: request.file.mimetype,
      metadata: {
        name: data.name || entity.name,
        influencerId: state.auth.influencerId,
      },
    });
  }

  const businessUpdated = await businessService.update(data.id, {
    name: data.name,
    description: data.description,
    ruc: data.ruc,
    imageUrl: imageUrl,
  });

  response.body = transformNullToUndefined({
    id: businessUpdated.id,
    name: businessUpdated.name,
    description: businessUpdated.description,
    imageUrl: businessUpdated.imageUrl,
    ruc: businessUpdated.ruc,
    createdAt: businessUpdated.createdAt,
    updatedAt: businessUpdated.updatedAt,
  });
};
