import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { getBusinessEndpoint } from "./get-business";
import { createBusinessEndpoint } from "./create-business";
import { updateBusinessEndpoint } from "./update-business";
import multer from "@koa/multer";
import { listBusinessEndpoint } from "./list-business";

const router = new Router();
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

const upload = multer({ storage: multer.memoryStorage() });
export const uploadMiddleware = upload.single("file");

router.get(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      business: ["READ_BUSINESS"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  getBusinessEndpoint,
);

router.post(
  "/",
  authMiddleware,
  uploadMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      business: ["PUT_BUSINESS"],
    },
  }),
  createBusinessEndpoint,
);

router.put(
  "/",
  authMiddleware,
  uploadMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      business: ["PUT_BUSINESS"],
    },
  }),
  updateBusinessEndpoint,
);

router.get(
  "/list",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      business: ["LIST_BUSINESS"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  listBusinessEndpoint,
);

export const businessRouter = router;
