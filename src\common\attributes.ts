import * as z from "zod";
import { AppError } from "@mainframe-peru/common-core";
import { common } from "@mainframe-peru/types";

export type ValidateValuesInput = {
  attributes: common.AttributeKey[];
  values: common.AttributeValues;
};

/**
 * Validates all values pass rules defined by the attribute
 * @param input Defined attributes and a list of values
 * @returns Set of valid attribute ids
 * @throws AppError when validation fails
 */
export function parseValues(
  input: ValidateValuesInput,
): common.AttributeValues {
  // Build validator
  let validator = z.object({});
  for (const at of input.attributes) {
    let x: z.ZodType | undefined;
    if (at.type === "MULTI" || at.type === "SINGLE") {
      if (!at.options || at.options.length < 1) {
        throw new AppError({
          code: "NoAttributeOptions",
          message: "No available options on attribute",
        });
      }
      const acceptedIds = at.options.map((o) => o.id) as [string, ...string[]];
      if (at.type === "MULTI") {
        x = z.array(z.enum(acceptedIds));
      } else if (at.type === "SINGLE") {
        x = z.enum(acceptedIds);
      }
    } else if (at.type === "TEXT") {
      x = z.string();
    } else if (at.type === "FILE") {
      x = z.string().url();
    }
    if (x) {
      if (!at.required) {
        x = x.optional();
      }
      validator = validator.extend({
        [at.id]: x,
      });
    }
  }

  // Validate values
  const result = validator.safeParse(input.values);
  if (!result.success) {
    throw new AppError({
      code: "ValidationFailed",
      message: "Some attribute values are not correct",
      statusCode: "BAD_REQUEST",
      logLevel: "INFO",
      responseBody: {
        issues: result.error.issues,
      },
    });
  }
  return result.data;
}
