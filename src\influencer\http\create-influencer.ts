import { Middleware } from "@koa/router";
import { influencer } from "@mainframe-peru/types";
import { influencerService } from "../service";
import { InfluencerEntity } from "../repository";

export const createInfluencerEndpoint: Middleware<
  unknown,
  unknown,
  influencer.CreateInfluencerResponse
> = async ({ request, response }) => {
  const body = influencer.CreateInfluencerRequestSchema.parse(request.body);
  const entity = await influencerService.create(body as InfluencerEntity);

  response.body = {
    id: entity.id,
  };
};
