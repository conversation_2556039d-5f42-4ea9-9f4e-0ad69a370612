import { Middleware } from "@koa/router";
import {
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { payment } from "@mainframe-peru/types";
import { getPaymentExecutionVariant } from "../variants";

export const executePayment: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  payment.ExecutePaymentResponse
> = async ({ request, response, state }) => {
  const params = payment.ExecutePaymentRequestSchema.parse(request.body);

  const paymentExecution = getPaymentExecutionVariant({
    influencerId: state.auth.influencerId,
    request: {
      amount: "",
      currency: "PEN",
      invoiceDestination: params.invoiceDestination,
      source: params.source,
      userId: state.auth.id,
      userUpdate: params.userUpdate,
      productId: -1,
      variant: {
        variant: "RECURRENCE_CREATION",
        planId: params.entityId,
        transactionId: -1,
      },
    },
  });
  await paymentExecution.validate();
  const { transactionId: publicTransactionId } =
    await paymentExecution.startCharge();

  response.body = {
    publicTransactionId,
  };
};
