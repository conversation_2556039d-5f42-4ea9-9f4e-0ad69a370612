import { Middleware } from "@koa/router";
import {
  AppError,
  AuthAdmin,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { invoice as it } from "@mainframe-peru/types";
import { userService } from "../../user";
import { invoiceService } from "../service";

export const getInvoiceEndpoint: Middleware<
  AuthorizedContextState<AuthEndUser | AuthAdmin>,
  unknown,
  it.http.GetInvoiceResponse
> = async ({ request, response, state }) => {
  const params = it.http.GetInvoiceRequestSchema.parse(request.query);

  const invoice = await invoiceService.get(
    "transactionId",
    params.transactionId,
  );
  if (!invoice) {
    throw new AppError({
      code: "InvoiceNotFound",
      message: "Could not find invoice",
      logLevel: "INFO",
      statusCode: "NOT_FOUND",
    });
  }

  // Validate that the user owns the invoice
  const user = await userService.get("id", invoice.userId);
  if (
    !user ||
    state.auth.influencerId !== user.influencerId ||
    (state.auth.iss === "mainframe:user" && state.auth.id !== user.id)
  ) {
    throw new AppError({
      code: "InvoiceNotFound",
      message: "Could not find invoice",
      logLevel: "INFO",
      statusCode: "NOT_FOUND",
    });
  }

  response.body = {
    id: invoice.id,
    transactionId: invoice.transactionId,
    link: invoice.link,
  };
};
