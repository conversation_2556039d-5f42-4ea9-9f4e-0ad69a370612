import { Middleware } from "@koa/router";
import { officer } from "@mainframe-peru/types";
import {
  AppError,
  hashPassword,
  officerPoliciesConstant,
  Policies,
} from "@mainframe-peru/common-core";
import { OfficerEntity } from "../repository";
import { officerService } from "../service";

export const putOfficer: Middleware<
  unknown,
  unknown,
  officer.PutOfficerResponse
> = async ({ request, response }) => {
  const body = officer.PutOfficerRequestSchema.parse(request.body);
  const currentEntity = await officerService.get("email", body.email);

  let hash: string;
  let id: number | undefined;
  // If entity exists (update flow)
  if (currentEntity) {
    id = currentEntity.id;
    if (body.password) {
      hash = await hashPassword(body.password);
    } else {
      hash = currentEntity.hash;
    }
  } else if (!body.password) {
    // If it doesn't (create flow), but a password is not provided
    throw new AppError({
      code: "MissingPassword",
      message: "Se necesita una contraseña para crear un oficial.",
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  } else {
    hash = await hashPassword(body.password);
  }

  const newEntity: Omit<OfficerEntity, "id" | "createdAt"> = {
    email: body.email,
    hash: hash,
    firstName: body.firstName,
    lastName: body.lastName,
    policies: Policies.mask(body.policies, officerPoliciesConstant),
  };

  const entity = await (id
    ? officerService.update(id, newEntity)
    : officerService.create(newEntity));

  response.body = {
    email: entity.email,
  };
};
