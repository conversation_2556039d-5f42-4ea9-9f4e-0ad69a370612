#!/bin/bash

set -e

case $1 in
  dev)
    ssh -N -L 6789:common-infra-rdsinstance-98twuhfpqtkg.c3geg6cq4wre.us-east-1.rds.amazonaws.com:5432 mf-dev-bastion
    ;;
  stg)
    ssh -N -L 6789:common-infra-rdsinstance-5gkxbzruugst.c5wmmccyadaq.us-east-1.rds.amazonaws.com:5432 mf-stg-bastion
    ;;
  prod)
    ssh -N -L 6789:common-infra-rdscluster-prjylpu34jlh.cluster-cbqkg0acylvx.us-east-1.rds.amazonaws.com:5432 mf-prod-bastion
    ;;
  *)
    echo "Usage ./connect.sh {dev|stg|prod}"
  ;;
esac

# case $1 in
#   dev)
#     ssh -f -L 6789:common-infra-rdsinstance-98twuhfpqtkg.c3geg6cq4wre.us-east-1.rds.amazonaws.com:5432 mf-dev-bastion sleep 10
#     ;;
#   stg)
#     ssh -f -L 6789:common-infra-rdsinstance-5gkxbzruugst.c5wmmccyadaq.us-east-1.rds.amazonaws.com:5432 mf-stg-bastion sleep 10
#     ;;
#   prod)
#     ssh -f -L 6789:common-infra-rdscluster-prjylpu34jlh.cluster-cbqkg0acylvx.us-east-1.rds.amazonaws.com:5432 mf-prod-bastion sleep 10
#     ;;
#   *)
#     echo "Usage ./connect.sh {dev|stg|prod}"
#   ;;
# esac

# psql -U mainframe -h localhost -d mainframe -p 6789
