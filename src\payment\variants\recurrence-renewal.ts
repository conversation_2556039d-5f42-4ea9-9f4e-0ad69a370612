import { AppError } from "@mainframe-peru/common-core";
import { ppe } from "@mainframe-peru/types";
import { add, differenceInDays } from "date-fns";
import { paymentProviderQueue } from "../../payment-provider-event";
import { planService } from "../../plan";
import { recurrenceService } from "../../recurrence";
import { recurrenceRepository } from "../../recurrence/repository";
import { transactionService } from "../../transaction";
import { transactionDetailService } from "../../transaction-detail/service";
import {
  EndChargeInput,
  PaymentExecutionVariant,
  ValidateOutput,
} from "./base";

export class RecurrenceR<PERSON>wal extends PaymentExecutionVariant<ppe.RecurrenceRenewalChargeVariant> {
  async _validate(): Promise<ValidateOutput> {
    throw new Error("hi");
  }

  async getRunningTransaction(): Promise<{ transactionId: number }> {
    const transaction = await transactionService.create({
      publicId: crypto.randomUUID(),
      influencerId: this.influencerId,
      userId: this.request.userId,
      state: "PROCESSING",
      amount: this.request.amount,
      currency: this.request.currency,
      type: "RECURRENCE",
      channel: "CARD",
    });

    await transactionDetailService.create({
      transactionId: transaction.id,
      entityId: this.variant.recurrenceId,
      amount: this.request.amount,
      productId: this.request.productId,
      quantity: 1,
    });

    return { transactionId: transaction.id };
  }

  async startCharge(): Promise<{ transactionId: string }> {
    await paymentProviderQueue.sendCharge(this.request);

    return {
      transactionId: "",
    };
  }

  async endCharge(input: EndChargeInput): Promise<void> {
    const { planId } = this.variant;
    const { user } = input;

    // Get plan
    const plan = await planService.get("id", planId);
    if (!plan) {
      throw new AppError({
        code: "PlanDoesNotExist",
        message: "No se encontró el plan dado",
        statusCode: "NOT_FOUND",
        logLevel: "INFO",
      });
    }

    const [recurrence] = await recurrenceService.list({
      userId: user.id,
      status: "ACTIVE",
    });

    if (!recurrence) {
      throw new AppError({
        code: "RecurrenceNotFound",
        message: "El usuario no tiene un pago recurrente",
        statusCode: "NOT_FOUND",
        logLevel: "ERROR",
      });
    }

    const renewalDate = add(recurrence.renewalDate, {
      days: plan.frequency,
    });

    // If this recurrence is active, just update renewal date
    await recurrenceService.update(recurrence.id, {
      renewalDate,
    });
  }

  async failCharge(): Promise<void> {
    const [recurrence] = await recurrenceService.list({
      userId: this.request.userId,
      status: "ACTIVE",
    });

    const difference = differenceInDays(
      new Date().toDateString(),
      recurrence.renewalDate.toDateString(),
    );

    if (recurrence && difference > 2) {
      // If this recurrence is active, just update renewal date
      await recurrenceRepository.update(recurrence.id, {
        endDate: new Date(),
        status: "INACTIVE",
      });
    } else if (recurrence && difference <= 2) {
      await recurrenceRepository.update(recurrence.id, {
        status: "PAUSED",
      });
    }
  }

  get allowRetry() {
    return true;
  }
}
