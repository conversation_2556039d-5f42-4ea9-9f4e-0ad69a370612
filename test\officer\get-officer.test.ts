import { officer } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { officerService } from "../../src/officer";
import { officerAuthToken } from "../common";

describe("get officer tests", () => {
  test("gets an existing officer by id", async () => {
    const testEntity = await officerService.create({
      id: 10 ** 6,
      email: "<EMAIL>",
      hash: "123123123123123123",
      firstName: "<PERSON>",
      lastName: "Tester",
      policies: {
        officer: 1,
        transaction: 3,
      },
    });

    const queryParams: officer.GetOfficerRequest = {
      email: testEntity.email,
    };

    const response = await request(app)
      .get("/live/officer")
      .query(queryParams)
      .set("Cookie", `session=${await officerAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual(
      expect.objectContaining({
        email: testEntity.email,
        firstName: testEntity.firstName,
        lastName: testEntity.lastName,
        policies: expect.objectContaining({
          officer: expect.objectContaining({
            PUT_OFFICER: true,
          }),
          transaction: expect.objectContaining({
            PUT_TRANSACTION: true,
            READ_TRANSACTION: true,
          }),
        }),
      }),
    );
  });
});
