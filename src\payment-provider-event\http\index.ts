import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { getPaymentProviderEvent } from "./get-ppe";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.get(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    admin: {
      paymentProviderEvent: ["GET_EVENT"],
    },
  }),
  getPaymentProviderEvent,
);

export const paymentProviderEventRouter = router;
