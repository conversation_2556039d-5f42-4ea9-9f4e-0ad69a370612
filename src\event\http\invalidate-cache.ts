import {
  CloudFrontClient,
  CreateInvalidationCommand,
} from "@aws-sdk/client-cloudfront";
import { Middleware } from "@koa/router";
import { sc } from "../../services";

export const invalidateCacheEndpoint: Middleware<
  unknown,
  unknown,
  null
> = async ({ response }) => {
  const command = new CreateInvalidationCommand({
    DistributionId: sc.vars.fccFrontendDistributionId,
    InvalidationBatch: {
      Paths: {
        Quantity: 1,
        Items: [`/api/event/*`],
      },
      CallerReference: crypto.randomUUID(), // required
    },
  });
  await new CloudFrontClient().send(command);

  response.body = null;
};
