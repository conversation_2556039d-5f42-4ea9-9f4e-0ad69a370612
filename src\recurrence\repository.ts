import { common } from "@mainframe-peru/types";
import {
  InferSelectModel,
  and,
  count,
  eq,
  inArray,
  lte,
  sql,
} from "drizzle-orm";
import { NTU, transformNullToUndefined } from "../helpers";
import { RepositoryBase } from "../repository-base";
import { planTable, recurrenceTable, userTable } from "../schema";
import { sc } from "../services";

export type RecurrenceEntity = InferSelectModel<typeof recurrenceTable>;

export type ActiveRecurrenceCountOutput = {
  total: number;
  plan: { id: number; name: string; count: number }[];
};

class RecurrenceRepository extends RepositoryBase<typeof recurrenceTable> {
  constructor() {
    super(recurrenceTable);
  }

  async findRecurrenceByStatus(
    userId: number,
    status: common.RecurrenceStatus | common.RecurrenceStatus[],
  ): Promise<NTU<RecurrenceEntity> | undefined> {
    const recurrences = await (
      await sc.getDB()
    )
      .select()
      .from(this.table)
      .where(
        and(
          eq(this.table.userId, userId),
          inArray(
            this.table.status,
            typeof status === "string" ? [status] : status,
          ),
        ),
      );
    return recurrences != undefined && recurrences.length > 0
      ? transformNullToUndefined(recurrences[0])
      : undefined;
  }

  async findRecurrenceToRenew(): Promise<RecurrenceEntity[]> {
    const db = await sc.getDB();
    const result = db
      .select()
      .from(this.table)
      .where(
        and(
          lte(
            sql`core_backend.recurrence.renewal_date::date`,
            new Date().toISOString().split("T")[0],
          ),
          inArray(this.table.status, ["ACTIVE", "PAUSED"]),
        ),
      );
    return result;
  }

  async getActiveRecurrence(
    userId: number,
    influencerId: string,
  ): Promise<NTU<RecurrenceEntity> | undefined> {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .leftJoin(userTable, eq(this.table.userId, userTable.id))
      .where(
        and(
          eq(this.table.userId, userId),
          eq(userTable.influencerId, influencerId),
          eq(this.table.status, "ACTIVE"),
        ),
      );
    return result != undefined && result.length > 0
      ? transformNullToUndefined(result[0].recurrence)
      : undefined;
  }

  async getActiveRecurrenceByEmail(
    email: string,
    influencerId: string,
  ): Promise<NTU<RecurrenceEntity> | undefined> {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .leftJoin(userTable, eq(this.table.userId, userTable.id))
      .where(
        and(
          eq(userTable.email, email),
          eq(userTable.influencerId, influencerId),
          eq(this.table.status, "ACTIVE"),
        ),
      );
    return result != undefined && result.length > 0
      ? transformNullToUndefined(result[0].recurrence)
      : undefined;
  }

  async getActiveRecurrenceCount(
    influencerId: string,
  ): Promise<ActiveRecurrenceCountOutput> {
    const db = await sc.getDB();
    const countByPlan = await db
      .select({ id: planTable.id, name: planTable.name, count: count() })
      .from(this.table)
      .innerJoin(planTable, eq(this.table.planId, planTable.id))
      .innerJoin(userTable, eq(this.table.userId, userTable.id))
      .where(
        and(
          eq(userTable.influencerId, influencerId),
          eq(this.table.status, "ACTIVE"),
        ),
      )
      .groupBy(planTable.id);
    return {
      total: countByPlan.reduce((p, c) => p + c.count, 0),
      plan: countByPlan,
    };
  }
}

export const recurrenceRepository = new RecurrenceRepository();
