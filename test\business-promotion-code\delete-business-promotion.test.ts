import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken } from "../common";
import { businessPromotionService } from "../../src/business-promotion";
import { businessPromotionCodeService } from "../../src/business-promotion-code";
import { faker } from "@faker-js/faker";

describe("delete business promotion code tests", () => {
  test("delete a single business promotion code", async () => {
    await influencerService.create(TestData.influencer);
    await businessService.create(TestData.business);
    await businessPromotionService.create(TestData.businessPromotion);
    const code = await businessPromotionCodeService.create({
      code: "ABC123",
      promotionId: TestData.businessPromotion.id,
    });

    const body: bpc.DeleteBusinessPromotionCodeRequest = {
      all: false,
      id: code.id,
    };

    const response = await request(app)
      .post("/live/business-promotion-code/delete")
      .send(body)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(204);

    const deleted = await businessPromotionCodeService.get("id", code.id);
    expect(deleted).toBeUndefined();
  });

  test("delete a all business promotion codes", async () => {
    await influencerService.create(TestData.influencer);
    await businessService.create(TestData.business);
    await businessPromotionService.create(TestData.businessPromotion);
    for (let i = 0; i < 100; i++) {
      await businessPromotionCodeService.create({
        code: faker.string.alphanumeric(6),
        promotionId: TestData.businessPromotion.id,
      });
    }

    const body: bpc.DeleteBusinessPromotionCodeRequest = {
      all: true,
      promotionId: TestData.businessPromotion.id,
    };

    const response = await request(app)
      .post("/live/business-promotion-code/delete")
      .send(body)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(204);

    const deleted = await businessPromotionCodeService.list({
      promotionId: TestData.businessPromotion.id,
    });
    expect(deleted.length).toEqual(0);
  });
});
