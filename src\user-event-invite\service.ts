import { AppError } from "@mainframe-peru/common-core";
import { common } from "@mainframe-peru/types";
import { quest } from "../common";
import { eventService } from "../event";
import { ServiceBase } from "../service-base";
import { UserEventInviteEntity, userEventInviteRepository } from "./repository";
import { EventEntity } from "../event/repository";

export type CreateInviteInput = {
  event: EventEntity;
  userId: number;
  emails: string[];
};

export type AcceptInviteInput = {
  inviteId: string;
  participationFormValues?: common.AttributeValues;
};

class UserEventInviteService extends ServiceBase<
  typeof userEventInviteRepository,
  UserEventInviteEntity
> {
  constructor() {
    super(userEventInviteRepository);
  }

  async createInvites(
    input: CreateInviteInput,
  ): Promise<UserEventInviteEntity[]> {
    const { event, emails, userId } = input;
    // Check invite limit
    if (event.inviteLimit) {
      const currentInviteCount = await this.repository.countInvitesByEventId(
        event.id,
      );
      if (currentInviteCount + emails.length > event.inviteLimit) {
        throw new AppError({
          code: "InviteLimitExceeded",
          message: `Cannot create invites. Limit of ${event.inviteLimit} would be exceeded.`,
          statusCode: "BAD_REQUEST",
          logLevel: "INFO",
        });
      }
    }

    const invites: UserEventInviteEntity[] = [];

    for (const email of emails) {
      // Check if invite already exists for this email and event
      const existingInvite = await this.repository.findByEmailAndEventId(
        email,
        event.id,
      );

      if (existingInvite) {
        throw new AppError({
          code: "InviteAlreadyExists",
          message: `Invite already exists for email ${email}`,
          statusCode: "BAD_REQUEST",
          logLevel: "INFO",
        });
      }

      const invite = await this.repository.create({
        id: crypto.randomUUID(),
        eventId: event.id,
        userId,
        email,
        status: "PENDING",
      });
      invites.push(invite);
    }

    return invites;
  }

  async acceptInvite(input: AcceptInviteInput): Promise<void> {
    const invite = await this.repository.find("id", input.inviteId);
    if (!invite) {
      throw new AppError({
        code: "InviteNotFound",
        message: "Invite not found",
        statusCode: "NOT_FOUND",
        logLevel: "INFO",
      });
    }

    if (invite.status !== "PENDING") {
      throw new AppError({
        code: "InviteAlreadyProcessed",
        message: "Invite has already been processed",
        statusCode: "BAD_REQUEST",
        logLevel: "INFO",
      });
    }

    const event = await eventService.get("id", invite.eventId);
    if (!event) {
      throw new AppError({
        code: "EventNotFound",
        message: "Event not found",
        statusCode: "NOT_FOUND",
        logLevel: "INFO",
      });
    }

    if (event.endDate && event.endDate < new Date()) {
      throw new AppError({
        code: "EventEnded",
        message: "The given event has already ended",
        statusCode: "BAD_REQUEST",
        logLevel: "INFO",
      });
    }

    // Validate participation form values if event has invite form
    if (event.inviteForm && !input.participationFormValues) {
      throw new AppError({
        code: "MissingParticipationFormValues",
        message: "Missing participation form values",
        statusCode: "BAD_REQUEST",
        logLevel: "INFO",
      });
    }

    let validatedFormValues: common.AttributeValues | undefined;
    if (event.inviteForm && input.participationFormValues) {
      validatedFormValues = quest.parseValues({
        attributes: event.inviteForm,
        values: input.participationFormValues,
      });
    }

    await this.repository.acceptInvite(input.inviteId, validatedFormValues);
  }
}

export const userEventInviteService = new UserEventInviteService();
