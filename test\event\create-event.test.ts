import { event } from "@mainframe-peru/types";
import "aws-sdk-client-mock-jest";
import request from "supertest";
import { app } from "../../src/api-handler";
import { eventService } from "../../src/event";
import { EventEntity } from "../../src/event/repository";
import { influencerService } from "../../src/influencer";
import { sc } from "../../src/services";
import { adminAuthToken, TestData } from "../common";

jest.mock("@aws-sdk/client-scheduler");

describe("create event tests", () => {
  test("create a event", async () => {
    await influencerService.create(TestData.influencer);

    const requestBody: event.CreateEventRequest = {
      type: TestData.event.type,
      status: TestData.event.status,
      influencerId: TestData.event.influencerId,
      name: TestData.event.name,
      description: TestData.event.description,
      eventDate: new Date(2050, 0, 0),
      startDate: TestData.event.startDate || undefined,
      participationForm: TestData.event.participationForm || undefined,
      imageUrl: "https://www.google.com/qwerty/",
      location: TestData.event.location || undefined,
    };
    const response = await request(app)
      .post("/live/event")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    const entity = (await eventService.get(
      "id",
      response.body.id,
    )) as EventEntity;

    expect(response.status).toBe(200);
    expect(entity).toEqual(
      expect.objectContaining({
        id: response.body.id,
        type: TestData.event.type,
        influencerId: TestData.event.influencerId,
        startDate: TestData.event.startDate,
        imageUrl: requestBody.imageUrl,
        participationForm: TestData.event.participationForm,
        location: TestData.event.location,
      }),
    );

    expect(sc.scheduler.send).toHaveBeenCalledTimes(1);
  });
});
