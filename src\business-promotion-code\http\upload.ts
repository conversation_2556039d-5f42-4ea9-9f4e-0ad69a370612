import { Middleware } from "@koa/router";
import { AuthAdmin, AuthorizedContextState } from "@mainframe-peru/common-core";
import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import { businessPromotionService } from "../../business-promotion/service";
import { businessPromotionCodeService } from "../service";

export const uploadCodesEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  bpc.UploadPromotionCodesResponse
> = async ({ request, response, state }) => {
  const params = bpc.UploadPromotionCodesRequestSchema.parse(request.body);

  await businessPromotionService.getSecure({
    id: params.promotionId,
    influencerId: state.auth.influencerId,
  });

  await businessPromotionCodeService.insertMultiple({
    codes: params.codes,
    promotionId: params.promotionId,
  });
  response.body = {
    amount: params.codes.length,
  };
};
