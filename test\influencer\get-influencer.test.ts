import { influencer } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { TestData, adminAuthToken } from "../common";

describe("get influencer tests", () => {
  test("gets an existing influencer by id", async () => {
    const testEntity = await influencerService.create({
      ...TestData.influencer,
      attributes: {
        banner: "test-url.com",
      },
    });

    const queryParams: influencer.GetInfluencerRequest = {
      id: TestData.influencer.id,
    };
    const response = await request(app)
      .get("/live/influencer")
      .query(queryParams)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual(
      expect.objectContaining({
        id: testEntity.id,
        name: testEntity.name,
        status: testEntity.status,
        domain: testEntity.domain,
        attributes: {
          banner: "test-url.com",
        },
      }),
    );
  });

  test("publicly gets an existing influencer by id", async () => {
    const testEntity = await influencerService.create(TestData.influencer);
    const queryParams: influencer.PublicGetInfluencerRequest = {
      id: TestData.influencer.id,
    };

    const response = await request(app)
      .get("/live/influencer/public")
      .query(queryParams);

    expect(response.status).toBe(200);
    expect(response.body).toEqual(
      expect.objectContaining({
        id: testEntity.id,
        name: testEntity.name,
        logoUrl: testEntity.logoUrl,
        domain: testEntity.domain,
        googleToken: testEntity.providersConfiguration?.GOOGLE?.apiKey,
      }),
    );
  });

  test("get influencer attribute values", async () => {
    const attributes = {
      banner: crypto.randomUUID(),
      terms: crypto.randomUUID(),
    };
    await influencerService.create({
      ...TestData.influencer,
      attributes,
    });

    const queryParams: influencer.PublicGetInfluencerRequest = {
      id: TestData.influencer.id,
    };

    const response = await request(app)
      .get("/live/influencer/public")
      .query(queryParams);

    expect(response.status).toBe(200);
    expect(response.body).toEqual(
      expect.objectContaining({
        attributes,
      }),
    );
  });
});
