CREATE TYPE "core_backend"."event_invite_status" AS ENUM('PENDING', 'ACCEPTED');--> statement-breakpoint
CREATE TABLE "core_backend"."user_event_invite" (
	"id" uuid PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"event_id" integer NOT NULL,
	"email" text NOT NULL,
	"participationFormValues" jsonb,
	"status" "core_backend"."event_invite_status" DEFAULT 'PENDING' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"accepted_at" timestamp
);
--> statement-breakpoint
ALTER TABLE "core_backend"."event" ADD COLUMN "invite_limit" integer;--> statement-breakpoint
ALTER TABLE "core_backend"."event" ADD COLUMN "inviteForm" jsonb;--> statement-breakpoint
ALTER TABLE "core_backend"."user_event_invite" ADD CONSTRAINT "user_event_invite_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core_backend"."user_event_invite" ADD CONSTRAINT "user_event_invite_event_id_event_id_fk" FOREIGN KEY ("event_id") REFERENCES "core_backend"."event"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "user_event_invite_email_event_idx" ON "core_backend"."user_event_invite" USING btree ("email","event_id");