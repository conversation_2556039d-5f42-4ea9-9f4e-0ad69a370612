import {
  AppError,
  AuthAdmin,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { event } from "@mainframe-peru/types";
import { Middleware } from "koa";
import { transformNullToUndefined } from "../../helpers";
import { eventService } from "../service";

export const updateEventEndpoint: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  event.UpdateEventResponse
> = async ({ request, response, state }) => {
  const body = event.UpdateEventRequestSchema.parse(request.body);
  const entity = await eventService.get("id", request.body.id);

  if (!entity) {
    throw new AppError({
      code: "EventNotFound",
      message: "No se pudo encontrar el event solicitado",
      statusCode: "BAD_REQUEST",
    });
  }

  if (state.auth.influencerId !== entity.influencerId) {
    throw new AppError({
      code: "EventInvalid",
      message: "Event inválido",
      statusCode: "BAD_REQUEST",
    });
  }

  const eventUpdated = await eventService.update(body.id, {
    influencerId: body.influencerId,
    name: body.name,
    type: body.type,
    status: body.status,
    description: body.description,
    eventDate: body.eventDate,
    startDate: body.startDate,
    endDate: body.endDate,
    participationForm: body.participationForm,
    imageUrl: body.imageUrl,
    inviteLimit: body.inviteLimit,
    inviteForm: body.inviteForm,
    location: body.location,
  });

  if (
    eventUpdated.type === "PRIZE" &&
    eventUpdated.eventDate &&
    entity.eventDate?.getTime() !== eventUpdated.eventDate?.getTime()
  ) {
    await eventService.createOrUpdateEventListGenerationSchedule(
      eventUpdated,
      "update",
    );
  }

  response.body = event.UpdateEventResponseSchema.parse(
    transformNullToUndefined(eventUpdated),
  );
};
