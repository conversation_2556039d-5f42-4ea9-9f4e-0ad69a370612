import {
  CountryAlpha2,
  DocumentType,
  Enums,
  TransactionState,
  TransactionType,
} from "@mainframe-peru/types/build/common";
import { InferSelectModel, and, desc, eq, sql } from "drizzle-orm";
import { transformNullToUndefined } from "../helpers";
import { RepositoryBase } from "../repository-base";
import { transactionDetailTable, transactionTable, userTable } from "../schema";
import { sc } from "../services";
import { addLimitAndOffsetToQuery } from "../common";

export type TransactionEntity = InferSelectModel<typeof transactionTable>;
type TransactionUserEntity = {
  email: string;
  firstName: string;
  lastName: string;
  documentType: DocumentType;
  documentValue: string;
  phone: string;
  country: CountryAlpha2;
  city: string;
  province: string;
  district: string;
  zipCode: string;
  line1: string;
};

export type DetailedTransactionEntity = Omit<
  TransactionEntity,
  "lastPaymentProviderEventId"
> &
  Partial<TransactionUserEntity>;

export type TransactionByStateCount = {
  date: string;
  successCount: number;
  errorCount: number;
};

class TransactionRepository extends RepositoryBase<typeof transactionTable> {
  constructor() {
    super(transactionTable);
  }

  async findTransactions(filter: {
    detailed?: boolean;
    influencerId?: string;
    userId?: number;
    type?: TransactionType;
    state?: TransactionState;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }): Promise<DetailedTransactionEntity[]> {
    const isDetailed = filter.detailed != undefined && filter.detailed;
    const userJoin = isDetailed
      ? {
          email: userTable.email,
          firstName: userTable.firstName,
          lastName: userTable.lastName,
          documentType: userTable.documentType,
          documentValue: userTable.documentValue,
          phone: userTable.phone,
          country: userTable.country,
          city: userTable.city,
          province: userTable.province,
          district: userTable.district,
          zipCode: userTable.zipCode,
          line1: userTable.line1,
        }
      : undefined;

    const query = (await sc.getDB())
      .select({
        id: this.table.id,
        publicId: this.table.publicId,
        userId: this.table.userId,
        influencerId: this.table.influencerId,
        state: this.table.state,
        type: this.table.type,
        amount: this.table.amount,
        currency: this.table.currency,
        createdAt: this.table.createdAt,
        channel: this.table.channel,
        ...userJoin,
      })
      .from(this.table);

    if (isDetailed) {
      query.leftJoin(userTable, eq(this.table.userId, userTable.id));
    }

    const conditions = [];
    if (filter.influencerId) {
      conditions.push(eq(this.table.influencerId, filter.influencerId));
    }
    if (filter.userId) {
      conditions.push(eq(this.table.userId, filter.userId));
    }
    if (filter.type) {
      conditions.push(eq(this.table.type, filter.type));
    }
    if (filter.state) {
      conditions.push(eq(this.table.state, filter.state));
    }
    if (filter.startDate) {
      const startOfDay = new Date(filter.startDate.setUTCHours(0, 0, 0, 0));
      conditions.push(sql`${this.table.createdAt} >= ${startOfDay}`);
    }
    if (filter.endDate) {
      const endOfDay = new Date(filter.endDate.setUTCHours(23, 59, 59, 999));
      conditions.push(sql`${this.table.createdAt} <= ${endOfDay}`);
    }

    // Concatenating the conditions
    query.where(and(...conditions));
    addLimitAndOffsetToQuery(filter, query);
    query.orderBy(desc(this.table.createdAt));
    return transformNullToUndefined(await query);
  }

  async getTransactionsCountByState(filter: {
    influencerId?: string;
    startDate?: Date;
    endDate?: Date;
  }): Promise<TransactionByStateCount[]> {
    const influencerCondition = filter.influencerId
      ? sql`${this.table.influencerId} = ${filter.influencerId}`
      : sql`TRUE`;

    const query = (await sc.getDB())
      .select({
        date: sql`TO_CHAR(date_series.date, 'DD/MM')`.as("date"),
        successCount:
          sql`COALESCE(COUNT(${this.table.createdAt}) FILTER (WHERE ${this.table.state} = ${Enums.TransactionState.Values.SUCCESS}), 0)`.as(
            "success_count",
          ),
        errorCount:
          sql`COALESCE(COUNT(${this.table.createdAt}) FILTER (WHERE ${this.table.state} = ${Enums.TransactionState.Values.FAIL}), 0)`.as(
            "error_count",
          ),
      })
      .from(
        sql`generate_series(CURRENT_DATE - INTERVAL '29 days', (CURRENT_TIMESTAMP at time zone 'America/Lima')::date, INTERVAL '1 day') AS date_series(date)`,
      )
      .leftJoin(
        this.table,
        and(
          sql`date_series.date = (${this.table.createdAt} - interval '5 hours')::date`,
          influencerCondition,
        ),
      );

    return transformNullToUndefined(
      await query.groupBy(sql`date_series.date`).orderBy(sql`date_series.date`),
    ) as TransactionByStateCount[];
  }

  async getCompleteTransaction(id: number, influencerId: string) {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .innerJoin(
        transactionDetailTable,
        eq(this.table.id, transactionDetailTable.transactionId),
      )
      .where(
        and(eq(this.table.id, id), eq(this.table.influencerId, influencerId)),
      );
    return result.length > 0 ? result[0] : undefined;
  }
}

export const transactionRepository = new TransactionRepository();
