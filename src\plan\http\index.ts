import Router from "@koa/router";
import {
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { getPlanEndpoint } from "./get-plan";
import { createPlanEndpoint } from "./create-plan";
import { publicGetPlan } from "./public-get-plan";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.get(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      plan: ["READ_PLAN"],
    },
    admin: {
      plan: ["READ_PLAN"],
    },
    user: {
      general: ["REGULAR"],
    },
  }),
  getPlanEndpoint,
);

router.post(
  "/",
  authMiddleware,
  getClientPoliciesValidationMiddleware({
    officer: {
      plan: ["PUT_PLAN"],
    },
    admin: {
      plan: ["PUT_PLAN"],
    },
  }),
  createPlanEndpoint,
);

// Public
router.get("/public", publicGetPlan);

export const planRouter = router;
