import serverlessExpress from "@codegenie/serverless-express";
import { bodyParser } from "@koa/bodyparser";
import Router from "@koa/router";
import {
  buildSQSHandler,
  errorMiddleware,
  eventSource,
  loggerMiddleware,
} from "@mainframe-peru/common-core";
import Koa from "koa";
import { invoiceSqsRouter } from "./invoice";
import { paymentProviderSqsRouter } from "./payment-provider-event";
import { recurrenceSqsRouter } from "./recurrence";

const koa = new Koa();
const router = new Router();

/**
 * Middlewares
 */
koa.use(errorMiddleware);
koa.use(bodyParser());
koa.use(loggerMiddleware);

/**
 * Endpoints
 */
router.use("/recurrence", recurrenceSqsRouter.routes());
router.use("/payment-provider", paymentProviderSqsRouter.routes());
router.use("/invoice", invoiceSqsRouter.routes());

/**
 * Finish setup
 */
koa.use(router.routes());

export const app = koa.callback();
export const handler = buildSQSHandler(
  serverlessExpress({
    app,
    eventSource,
    eventSourceRoutes: {
      AWS_SQS: "/",
    },
  }),
);
