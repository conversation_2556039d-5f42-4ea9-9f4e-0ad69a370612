import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { event } from "@mainframe-peru/types";
import { Middleware } from "koa";
import { eventService } from "../service";
import { transformNullToUndefined } from "../../helpers";

export const listEventsEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  event.ListEventsResponse | event.PublicListEventsResponse
> = async ({ request, response, state }) => {
  if (state.auth) {
    const query = event.ListEventsRequestSchema.parse(request.query);

    const events = await eventService.getEvents(query);

    response.body = event.ListEventsResponseSchema.parse(
      transformNullToUndefined(events),
    );
  } else {
    const query = event.PublicListEventsRequestSchema.parse(request.query);

    const events = await eventService.getEvents({
      status: "ACTIVE",
      ...query,
    });

    response.set("Cache-Control", "s-maxage=1800");
    response.body = event.PublicListEventsResponseSchema.parse(
      transformNullToUndefined(events),
    );
  }
};
