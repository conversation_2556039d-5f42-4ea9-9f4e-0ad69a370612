import { businessPromotionCode as bpc } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { businessPromotionService } from "../../src/business-promotion";
import { businessPromotionCodeService } from "../../src/business-promotion-code";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken } from "../common";

describe("generate business promotion code tests", () => {
  test("generate a single business promotion code", async () => {
    await influencerService.create(TestData.influencer);
    await businessService.create(TestData.business);
    await businessPromotionService.create(TestData.businessPromotion);

    const body: bpc.GeneratePromotionCodesRequest = {
      amount: 50,
      promotionId: TestData.businessPromotion.id,
    };

    const response = await request(app)
      .post("/live/business-promotion-code/generate")
      .send(body)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);

    const created = await businessPromotionCodeService.list({
      promotionId: TestData.businessPromotion.id,
    });
    expect(created.length).toEqual(50);
  });
});
