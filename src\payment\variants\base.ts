import { common, ppe } from "@mainframe-peru/types";
import { UserEntity } from "../../user/repository";
import { cardService } from "../../card";
import { AppError } from "@mainframe-peru/common-core";

export type PaymentExecutionVariantInput = {
  request: ppe.ChargeRequest;
  influencerId: string;
};

export type EndChargeInput = {
  user: UserEntity;
};

export type ValidateOutput = {
  amount: string;
  currency: common.Currency;
  productId: number;
};

export abstract class PaymentExecutionVariant<T extends ppe.ChargeVariant> {
  protected request: ppe.ChargeRequest;
  protected variant: T;
  protected influencerId: string;

  constructor(input: PaymentExecutionVariantInput) {
    this.request = input.request;
    this.variant = input.request.variant as T;
    this.influencerId = input.influencerId;
  }

  async validate(): Promise<void> {
    await this.validateCard();
    const { amount, currency, productId } = await this._validate();
    this.request.amount = amount;
    this.request.currency = currency;
    this.request.productId = productId;
  }

  abstract getRunningTransaction(): Promise<{ transactionId: number }>;
  protected abstract _validate(): Promise<ValidateOutput>;
  abstract startCharge(): Promise<{ transactionId: string }>;
  abstract endCharge(input: EndChargeInput): Promise<void>;
  abstract failCharge(): Promise<void>;

  abstract get allowRetry(): boolean;

  private async validateCard(): Promise<void> {
    const { source } = this.request;
    if (source.source === "CARD") {
      // Validate card exists
      const cards = await cardService.listByUserId(this.request.userId);
      const card = source.cardId
        ? cards.find((c) => c.id === source.cardId)
        : cards.find((c) => c.default);
      if (!card) {
        throw new AppError({
          code: "CardNotFound",
          message: "No se encontró la tarjeta seleccionada para recurrencia",
          statusCode: "NOT_FOUND",
          logLevel: "INFO",
        });
      }
    }
  }
}
