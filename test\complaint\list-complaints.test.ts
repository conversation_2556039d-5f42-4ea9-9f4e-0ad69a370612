import request from "supertest";
import { app } from "../../src/api-handler";
import { complaintService } from "../../src/complaint/service";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken } from "../common";
import { common, complaint } from "@mainframe-peru/types";
import { faker } from "@faker-js/faker";
import {
  ComplaintType,
  SupportStatus,
} from "@mainframe-peru/types/build/common";
import { InferInsertModel } from "drizzle-orm";
import { complaintTable } from "../../src/schema";

describe("List complaints test", () => {
  const mockIssues = (
    numberOfIssues: number,
    typeOfIssues?: ComplaintType,
    influencerId?: string,
    status?: SupportStatus,
  ): InferInsertModel<typeof complaintTable>[] => {
    function getRandomElement<T>(array: T[]) {
      return array[Math.floor(Math.random() * array.length)];
    }
    return Array(numberOfIssues)
      .fill(1)
      .map(() => {
        return {
          influencerId: !influencerId ? TestData.influencer.id : influencerId,
          subject: faker.string.sample(),
          description: faker.string.sample(),
          email: faker.internet.email(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          documentType: "DNI",
          documentValue: "12345678",
          phone: faker.phone.number(),
          ageCategory: getRandomElement(["ADULT", "MINOR"]),
          type: !typeOfIssues
            ? getRandomElement(["COMPLAINT", "CLAIM"])
            : typeOfIssues,
          status: !status
            ? getRandomElement(["CLOSED", "OPENED", "ARCHIVED"])
            : status,
        };
      });
  };

  test("List all claims and complaints", async () => {
    await influencerService.create(TestData.influencer);
    await complaintService.create(TestData.complaint);

    const response = await request(app)
      .get("/live/complaint/list-issues")
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toEqual(200);
    expect(response.body.complaints.length).toEqual(1);
  });

  test("List all clamis", async () => {
    await influencerService.create(TestData.influencer);
    await influencerService.create({
      id: "pepito",
      name: "Bizz name",
      status: common.Enums.InfluencerStatus.Enum.ACTIVE,
      logoUrl: "www",
      transientUsers: false,
      domain: "fcc.pchujoy.app",
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    for (const item of mockIssues(100, "CLAIM", "pepito")) {
      await complaintService.create(item);
    }

    for (const item of mockIssues(50, "CLAIM")) {
      await complaintService.create(item);
    }

    const query: complaint.ListComplaintsRequest = {
      type: "CLAIM",
    };

    const response = await request(app)
      .get("/live/complaint/list-issues")
      .query(query)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toEqual(200);
    expect(response.body.complaints.length).toEqual(50);
  });

  test("List all complaints", async () => {
    await influencerService.create(TestData.influencer);
    await influencerService.create({
      id: "pepito",
      name: "Bizz name",
      status: common.Enums.InfluencerStatus.Enum.ACTIVE,
      logoUrl: "www",
      transientUsers: false,
      domain: "fcc.pchujoy.app",
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    for (const item of mockIssues(100, "CLAIM", "pepito")) {
      await complaintService.create(item);
    }

    for (const item of mockIssues(10, "COMPLAINT")) {
      await complaintService.create(item);
    }

    const query: complaint.ListComplaintsRequest = {
      type: "COMPLAINT",
    };

    const response = await request(app)
      .get("/live/complaint/list-issues")
      .query(query)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toEqual(200);
    expect(response.body.complaints.length).toEqual(10);
  });

  test("List all complaints by status", async () => {
    await influencerService.create(TestData.influencer);
    await influencerService.create({
      id: "pepito",
      name: "Bizz name",
      status: common.Enums.InfluencerStatus.Enum.ACTIVE,
      logoUrl: "www",
      transientUsers: false,
      domain: "fcc.pchujoy.app",
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    for (const item of mockIssues(100, "CLAIM", "pepito")) {
      await complaintService.create(item);
    }

    for (const item of mockIssues(
      10,
      "COMPLAINT",
      TestData.influencer.id,
      "OPENED",
    )) {
      await complaintService.create(item);
    }

    for (const item of mockIssues(
      25,
      "COMPLAINT",
      TestData.influencer.id,
      "CLOSED",
    )) {
      await complaintService.create(item);
    }

    for (const item of mockIssues(
      200,
      "COMPLAINT",
      TestData.influencer.id,
      "ARCHIVED",
    )) {
      await complaintService.create(item);
    }

    const query: complaint.ListComplaintsRequest = {
      type: "COMPLAINT",
      status: "ARCHIVED",
    };

    const response = await request(app)
      .get("/live/complaint/list-issues")
      .query(query)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toEqual(200);
    expect(response.body.complaints.length).toEqual(200);
  });

  test("List all claims by one status", async () => {
    await influencerService.create(TestData.influencer);
    await influencerService.create({
      id: "pepito",
      name: "Bizz name",
      status: common.Enums.InfluencerStatus.Enum.ACTIVE,
      logoUrl: "www",
      transientUsers: false,
      domain: "fcc.pchujoy.app",
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    for (const item of mockIssues(100, "CLAIM", "pepito")) {
      await complaintService.create(item);
    }

    for (const item of mockIssues(
      10,
      "COMPLAINT",
      TestData.influencer.id,
      "OPENED",
    )) {
      await complaintService.create(item);
    }

    for (const item of mockIssues(
      25,
      "COMPLAINT",
      TestData.influencer.id,
      "CLOSED",
    )) {
      await complaintService.create(item);
    }

    for (const item of mockIssues(
      200,
      "COMPLAINT",
      TestData.influencer.id,
      "ARCHIVED",
    )) {
      await complaintService.create(item);
    }

    const query: complaint.ListComplaintsRequest = {
      type: "CLAIM",
      status: "ARCHIVED",
    };

    const response = await request(app)
      .get("/live/complaint/list-issues")
      .query(query)
      .set("Accept", "application/json")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toEqual(200);
    expect(response.body.complaints.length).toEqual(0);
  });
});
