import { card } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import { randomUUID } from "node:crypto";
import request from "supertest";
import { app } from "../../src/api-handler";
import { cardService } from "../../src/card/service";
import { influencerService } from "../../src/influencer/service";
import { userService } from "../../src/user";
import { TestData, userAuthToken } from "../common";

describe("payment.default-card", () => {
  test("set user default card", async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);

    let lastId = 0;
    for (const i of [0, 1]) {
      const c = await cardService.create({
        userId: TestData.user.id,
        brand: "Visa",
        default: i === 0,
        number: "4111...1111",
        paymentProvider: Enums.PaymentProvider.Enum.CULQI,
        token: randomUUID(),
      });
      lastId = c.id;
    }

    const requestBody: card.SetDefaultCardRequest = {
      id: lastId,
    };
    const response = await request(app)
      .post("/live/card/default")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);
    expect(response.status).toBe(200);
    const body = response.body as card.SetDefaultCardResponse;
    expect(body.id).toEqual(lastId);
    expect(body.default).toEqual(true);

    const stored = await cardService.listByUserId(TestData.user.id);

    expect(stored?.length).toEqual(2);
    expect(stored?.[0]).toEqual(
      expect.objectContaining({
        default: false,
      }),
    );
    expect(stored?.[1]).toEqual(
      expect.objectContaining({
        default: true,
      }),
    );
  });
});
