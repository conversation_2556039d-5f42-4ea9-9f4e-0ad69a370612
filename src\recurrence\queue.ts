import {
  SendMessageCommand,
  SendMessageCommandOutput,
} from "@aws-sdk/client-sqs";
import { sc } from "../services";
import { recurrence } from "@mainframe-peru/types";

export const recurrenceQueue = {
  async sendRecurrenceCancelation(
    request: recurrence.CancelRecurrenceRequest,
  ): Promise<SendMessageCommandOutput> {
    const command = new SendMessageCommand({
      QueueUrl: sc.vars.sqsQueueUrl,
      MessageBody: JSON.stringify(request),
      MessageAttributes: {
        path: {
          DataType: "String",
          StringValue: "/recurrence/cancellation",
        },
        method: {
          DataType: "String",
          StringValue: "POST",
        },
        origin: {
          DataType: "String",
          StringValue: "core-backend",
        },
      },
    });
    return await sc.sqs.send(command);
  },
};
