CREATE TYPE "core_backend"."code_type" AS ENUM('RECURRENCE');--> statement-breakpoint
ALTER TYPE "core_backend"."recurrence_type" ADD VALUE 'CODE';--> statement-breakpoint
ALTER TYPE "core_backend"."transaction_type" ADD VALUE 'RECURRENCE_CODE_REDEMPTION';--> statement-breakpoint
CREATE TABLE "core_backend"."code" (
	"code" text NOT NULL,
	"influencer_id" text NOT NULL,
	"type" "core_backend"."code_type" NOT NULL,
	"product_id" integer NOT NULL,
	"user_id" integer,
	"redeemed_date" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "code_code_influencer_id_pk" PRIMARY KEY("code","influencer_id")
);
--> statement-breakpoint
ALTER TABLE "core_backend"."code" ADD CONSTRAINT "code_influencer_id_influencer_id_fk" FOREIGN KEY ("influencer_id") REFERENCES "core_backend"."influencer"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "core_backend"."code" ADD CONSTRAINT "code_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;