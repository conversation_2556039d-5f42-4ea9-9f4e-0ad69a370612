import { ServiceBase } from "../service-base";
import {
  TransactionDetailEntity,
  transactionDetailRepository,
} from "./repository";

class TransactionDetailService extends ServiceBase<
  typeof transactionDetailRepository,
  TransactionDetailEntity
> {
  constructor() {
    super(transactionDetailRepository);
  }

  listWithProducts: typeof transactionDetailRepository.listWithProducts = (
    ...p
  ) => this.repository.listWithProducts(...p);
}

export const transactionDetailService = new TransactionDetailService();
