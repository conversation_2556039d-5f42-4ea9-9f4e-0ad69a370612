import { card, ext } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import fetch, { Response } from "node-fetch";
import { randomUUID } from "node:crypto";
import request from "supertest";
import { app } from "../../src/api-handler";
import { cardService } from "../../src/card";
import { influencerService } from "../../src/influencer";
import { userService } from "../../src/user";
import { userPaymentProviderService } from "../../src/user-payment-provider";
import { TestData, userAuthToken } from "../common";
import { mockCulqiCreateCard } from "../mocks";

jest.mock("node-fetch");

describe("payment.add-card", () => {
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
  test("add card on user", async () => {
    await influencerService.create(TestData.influencer);
    const user = await userService.create(TestData.user);

    await userPaymentProviderService.create({
      userId: user.id,
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      value: "user-token-external",
    });

    const culqiCreateCustomerResponse = mockCulqiCreateCard();

    const requestBody: card.AddCardOnUserRequest = {
      tokenId: randomUUID(),
      provider: Enums.PaymentProvider.Enum.CULQI,
    };
    const response = await request(app)
      .post("/live/card")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);

    const card = await cardService.get("id", response.body.id);
    expect(card).toEqual(
      expect.objectContaining({
        id: response.body.id,
        createdAt: expect.any(Date),
        default: true,
        paymentProvider: Enums.PaymentProvider.Enum.CULQI,
        brand: culqiCreateCustomerResponse.source.iin.card_brand,
        token: culqiCreateCustomerResponse.id,
      }),
    );
  });

  test("add card on user with 3DS", async () => {
    await influencerService.create(TestData.influencer);
    const user = await userService.create(TestData.user);

    await userPaymentProviderService.create({
      userId: user.id,
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      value: "user-token-external",
    });

    const json = jest.fn();
    const culqiCreateCardResponse: ext.culqi.CreateCardReviewResponse = {
      user_message: "El usuario necesita autenticarse",
      action_code: "REVIEW",
    };
    json.mockResolvedValue(culqiCreateCardResponse);
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      json,
    } as unknown as Response);

    const requestBody: card.AddCardOnUserRequest = {
      tokenId: randomUUID(),
      provider: Enums.PaymentProvider.Enum.CULQI,
    };
    const response = await request(app)
      .post("/live/card")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(400);
    expect(response.body.code).toEqual("3DSAuthenticationRequired");
  });
});
