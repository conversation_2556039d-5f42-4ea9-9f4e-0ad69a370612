import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer";
import { userService } from "../../src/user";
import { TestData, userAuthToken } from "../common";
import { eventService } from "../../src/event";
import { userEventInviteService } from "../../src/user-event-invite";
import { userEventInvite } from "@mainframe-peru/types";

describe("User Event Invite Acceptance", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
  });

  test("should get invite details", async () => {
    const event = await eventService.create({
      influencerId: TestData.influencer.id,
      name: "Test Event",
      description: "Test Description",
      type: "PRIZE",
      status: "ACTIVE",
      imageUrl: "https://example.com/image.jpg",
    });

    const invite = await userEventInviteService.create({
      id: crypto.randomUUID(),
      eventId: event.id,
      userId: TestData.user.id,
      email: "<EMAIL>",
    });

    const query: userEventInvite.GetUserEventInviteRequest = {
      id: invite.id,
    };
    const response = await request(app)
      .get("/live/user-event-invite")
      .query(query);

    expect(response.status).toEqual(200);
    expect(response.body).toMatchObject({
      id: invite.id,
      status: "PENDING",
    });
  });

  test("should accept invite successfully", async () => {
    const event = await eventService.create({
      influencerId: TestData.influencer.id,
      name: "Test Event",
      description: "Test Description",
      type: "PRIZE",
      status: "ACTIVE",
      imageUrl: "https://example.com/image.jpg",
      inviteForm: [
        {
          id: "name",
          text: "Full Name",
          type: "TEXT",
          required: true,
        },
      ],
    });

    const invite = await userEventInviteService.create({
      id: crypto.randomUUID(),
      eventId: event.id,
      userId: TestData.user.id,
      email: "<EMAIL>",
    });

    const body: userEventInvite.AcceptUserEventInviteRequest = {
      id: invite.id,
      participationFormValues: {
        name: "John Doe",
      },
    };

    const response = await request(app)
      .post("/live/user-event-invite/accept")
      .send(body);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      success: true,
    });

    // Verify invite was accepted
    const updatedInvite = await userEventInviteService.get("id", invite.id);
    expect(updatedInvite?.status).toEqual("ACCEPTED");
    expect(updatedInvite?.userId).toEqual(TestData.user.id);
    expect(updatedInvite?.acceptedAt).toBeDefined();
  });

  test("should fail to accept already processed invite", async () => {
    const event = await eventService.create({
      influencerId: TestData.influencer.id,
      name: "Test Event",
      description: "Test Description",
      type: "PRIZE",
      status: "ACTIVE",
      imageUrl: "https://example.com/image.jpg",
    });

    const invite = await userEventInviteService.create({
      id: crypto.randomUUID(),
      eventId: event.id,
      userId: TestData.user.id,
      email: "<EMAIL>",
    });

    // Accept invite first time
    await userEventInviteService.acceptInvite({
      inviteId: invite.id,
    });

    const body: userEventInvite.AcceptUserEventInviteRequest = {
      id: invite.id,
    };

    const response = await request(app)
      .post("/live/user-event-invite/accept")
      .send(body)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toEqual(400);
    expect(response.body.code).toEqual("InviteAlreadyProcessed");
  });

  test("should fail with invalid invite ID", async () => {
    const body: userEventInvite.AcceptUserEventInviteRequest = {
      id: "00000000-0000-0000-0000-000000000000",
    };

    const response = await request(app)
      .post("/live/user-event-invite/accept")
      .send(body);

    expect(response.status).toEqual(404);
    expect(response.body.code).toEqual("InviteNotFound");
  });
});
