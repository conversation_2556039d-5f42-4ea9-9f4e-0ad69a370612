import { Middleware } from "@koa/router";
import { AppError } from "@mainframe-peru/common-core";
import { transaction } from "@mainframe-peru/types";
import { transactionService } from "../service";
import { paymentProviderEventService } from "../../payment-provider-event/service";

export const publicGetTransaction: Middleware<
  unknown,
  unknown,
  transaction.PublicGetTransactionResponse
> = async ({ request, response }) => {
  const query = transaction.PublicGetTransactionRequestSchema.parse(
    request.query,
  );

  const transactionResponse = await transactionService.get(
    "publicId",
    query.publicId,
  );
  if (!transactionResponse) {
    throw new AppError({
      code: "TransactionNotFound",
      message: "No se pudo encontrar la transacción",
      logLevel: "INFO",
      statusCode: "NOT_FOUND",
    });
  }

  const paymentEvent = transactionResponse.lastPaymentProviderEventId
    ? await paymentProviderEventService.get(
        "id",
        transactionResponse.lastPaymentProviderEventId,
      )
    : undefined;

  const result = {
    ...transactionResponse,
    paymentExternalId: paymentEvent ? paymentEvent.externalId : undefined,
    paymentResponseMessage: paymentEvent
      ? paymentEvent.responseMessage
      : undefined,
  };

  response.body = transaction.PublicGetTransactionResponseSchema.parse(result);
};
