import { logger } from "@mainframe-peru/common-core";
import { Enums } from "@mainframe-peru/types/build/common";
import { type ScheduledEvent } from "aws-lambda";
import { getPaymentExecutionVariant } from "./payment/variants";
import { planService } from "./plan";
import { recurrenceQueue, recurrenceService } from "./recurrence";

export const handler = async (event: ScheduledEvent) => {
  logger.info("Received scheduled event", { event });

  const planProducts = await planService.getPlansProduct();
  const plansById = new Map(planProducts.map((p) => [p.plan.id, p]));

  const recurrences = await recurrenceService.findRecurrenceToRenew();
  for (const rp of recurrences) {
    try {
      if (rp.type === Enums.RecurrenceType.Values.CARD) {
        if (!rp.endDate) {
          const planProduct = plansById.get(rp.planId);
          if (planProduct) {
            const paymentExecution = getPaymentExecutionVariant({
              influencerId: planProduct.plan.influencerId,
              request: {
                amount: planProduct.product.amount,
                currency: planProduct.product.currency,
                invoiceDestination: rp.invoiceDestination,
                userId: rp.userId,
                productId: planProduct.product.id,
                variant: {
                  variant: "RECURRENCE_RENEWAL",
                  planId: planProduct.plan.id,
                  recurrenceId: rp.id,
                },
                source: {
                  source: rp.type,
                },
              },
            });
            await paymentExecution.startCharge();
            logger.info("Sent payment execution event", {
              plan: planProduct.plan,
              recurrence: rp,
            });
          }
        } else {
          await recurrenceQueue.sendRecurrenceCancelation({
            userId: rp.userId,
          });
          logger.info("Sent card payment cancellation event", {
            recurrence: rp,
          });
        }
      } else if (rp.type === Enums.RecurrenceType.Values.MANUAL) {
        await recurrenceQueue.sendRecurrenceCancelation({
          userId: rp.userId,
        });
        logger.info("Sent manual payment cancellation event", {
          recurrence: rp,
        });
      }
    } catch (e) {
      logger.error("Failed queuing trigger", { trigger: rp, error: e });
    }
  }
};
