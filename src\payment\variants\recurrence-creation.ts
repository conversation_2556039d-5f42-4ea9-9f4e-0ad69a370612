import { AppError } from "@mainframe-peru/common-core";
import { common, ppe } from "@mainframe-peru/types";
import { add, isFuture, sub } from "date-fns";
import { emailNotification } from "../../common";
import { influencerService } from "../../influencer";
import { paymentProviderQueue } from "../../payment-provider-event";
import { planService } from "../../plan";
import { recurrenceService } from "../../recurrence";
import { recurrenceRepository } from "../../recurrence/repository";
import { transactionService } from "../../transaction";
import { transactionDetailService } from "../../transaction-detail/service";
import {
  EndChargeInput,
  PaymentExecutionVariant,
  ValidateOutput,
} from "./base";
import { productService } from "../../product/service";

export class RecurrenceCreation extends PaymentExecutionVariant<ppe.RecurrenceCreationChargeVariant> {
  async _validate(): Promise<ValidateOutput> {
    const { userId, source } = this.request;
    const { planId } = this.variant;
    // Check no recurrence creation is running on SQS
    const chargingRP = await recurrenceRepository.findRecurrenceByStatus(
      userId,
      "CREATING",
    );
    if (chargingRP) {
      throw new AppError({
        code: "RecurrenceHasBeenQueued",
        message:
          "Este usuario ya ha puesto en cola una creación de recurrencia",
        statusCode: "BAD_REQUEST",
        logLevel: "INFO",
      });
    }

    // Validate plan exists
    const plan = await planService.getByIdAndInfluencer(
      planId,
      this.influencerId,
    );
    if (!plan) {
      throw new AppError({
        code: "PlanDoesNotExist",
        message: "No se encontró el plan dado",
        statusCode: "NOT_FOUND",
        logLevel: "INFO",
      });
    }

    // Validate no active recurrent payment already exists
    const activeRecurrence =
      await recurrenceService.getActiveRecurrence(userId);
    if (activeRecurrence) {
      if (source.source === "CARD") {
        throw new AppError({
          code: "UserHasRecurrence",
          message: "El usuario de la sesión tiene un pago recurrente activo",
          logLevel: "INFO",
          statusCode: "BAD_REQUEST",
        });
      } else if (source.source === "YAPE") {
        // Check if an active CARD subscription already exists
        if (activeRecurrence.type === common.Enums.RecurrenceType.Enum.CARD) {
          throw new AppError({
            code: "UserHasRecurrence",
            message: "El usuario de la sesión tiene un pago recurrente activo",
            logLevel: "INFO",
            statusCode: "BAD_REQUEST",
          });
        }
        // Validate plan matches recurrent payment
        if (this.variant.planId !== activeRecurrence.planId) {
          throw new AppError({
            code: "PlanDoesNotMatchRP",
            message:
              "El plan indicado no coincide con el pago recurrente activo",
          });
        }

        // Check user is within one week before renewal
        if (isFuture(sub(activeRecurrence.renewalDate, { weeks: 1 }))) {
          throw new AppError({
            code: "CannotRenewNow",
            message:
              "La renovación debe realizarse una semana o menos antes del vencimiento",
            statusCode: "BAD_REQUEST",
            logLevel: "INFO",
          });
        }
      }
    }

    // Validate token is yape
    if (source.source === "YAPE" && !source.token.startsWith("ype_")) {
      throw new AppError({
        code: "InvalidToken",
        message: "El token dado debe ser yape.",
        statusCode: "BAD_REQUEST",
        logLevel: "INFO",
      });
    }

    const product = await productService.get("id", plan.productId);

    if (!product) {
      throw new AppError({
        code: "ProductNotFound",
        message: "Product not found",
        logLevel: "CRITICAL",
        statusCode: "INTERNAL_SERVER_ERROR",
      });
    }

    return {
      amount: product.amount,
      currency: product.currency,
      productId: plan.productId,
    };
  }

  async getRunningTransaction(): Promise<{ transactionId: number }> {
    return { transactionId: this.variant.transactionId };
  }

  async startCharge(): Promise<{ transactionId: string }> {
    // Queue first charge
    const recurrence = await recurrenceService.create({
      planId: this.variant.planId,
      renewalDate: new Date(),
      status: "CREATING",
      type: this.request.source.source === "CARD" ? "CARD" : "MANUAL",
      userId: this.request.userId,
      invoiceDestination: this.request.invoiceDestination,
    });

    const transaction = await transactionService.create({
      publicId: crypto.randomUUID(),
      influencerId: this.influencerId,
      userId: this.request.userId,
      state: "PROCESSING",
      amount: this.request.amount,
      currency: this.request.currency,
      type: "RECURRENCE",
      channel: this.request.source.source,
    });

    await transactionDetailService.create({
      transactionId: transaction.id,
      entityId: recurrence.id,
      amount: this.request.amount,
      productId: this.request.productId,
      quantity: 1,
    });

    await paymentProviderQueue.sendCharge({
      ...this.request,
      variant: {
        variant: "RECURRENCE_CREATION",
        planId: this.variant.planId,
        transactionId: transaction.id,
      },
    });

    return {
      transactionId: transaction.publicId,
    };
  }

  async endCharge(input: EndChargeInput): Promise<void> {
    const { planId } = this.variant;
    const { user } = input;

    // Get plan
    const plan = await planService.get("id", planId);
    if (!plan) {
      throw new AppError({
        code: "PlanDoesNotExist",
        message: "No se encontró el plan dado",
        statusCode: "NOT_FOUND",
        logLevel: "INFO",
      });
    }

    const [recurrence] = await recurrenceService.list({
      userId: user.id,
      status: "CREATING",
    });

    if (!recurrence) {
      throw new AppError({
        code: "RecurrenceNotFound",
        message: "El usuario no tiene un pago recurrente",
        statusCode: "NOT_FOUND",
        logLevel: "ERROR",
      });
    }

    const renewalDate = add(recurrence.renewalDate, {
      days: plan.frequency,
    });

    await recurrenceService.update(recurrence.id, {
      status: "ACTIVE",
      renewalDate,
    });
    const influencer = await influencerService.get("id", user.influencerId);
    if (!influencer) {
      throw new AppError({ code: "MissingInfluencer", logLevel: "CRITICAL" });
    }
    await emailNotification.sendWelcomeEmail(user.email, influencer, {
      Name: user.firstName + " " + user.lastName,
    });
  }

  async failCharge(): Promise<void> {
    const [recurrence] = await recurrenceService.list({
      userId: this.request.userId,
      status: "CREATING",
    });

    if (recurrence) {
      // If this recurrence is new, change state
      await recurrenceRepository.delete(recurrence.id);
    }
  }

  get allowRetry() {
    return false;
  }
}
