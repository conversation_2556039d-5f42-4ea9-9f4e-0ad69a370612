import { common, recurrence as recurrenceT } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../../src/api-handler";
import { influencerService } from "../../../src/influencer/service";
import { planService } from "../../../src/plan";
import { recurrenceService } from "../../../src/recurrence/service";
import { userService } from "../../../src/user";
import { TestData, adminAuthToken, userAuthToken } from "../../common";
import { productService } from "../../../src/product/service";

describe("recurrence.get-recurrence", () => {
  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await productService.create(TestData.product);
    await planService.create(TestData.plan);
  });

  test("get active recurrent payment (as user)", async () => {
    const recurrence = await recurrenceService.create(TestData.recurrence);

    const response = await request(app)
      .get("/live/recurrence")
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    const body = response.body as recurrenceT.GetRecurrenceResponse;

    expect(body).toEqual(
      expect.objectContaining({
        plan: {
          amount: TestData.product.amount,
          currency: TestData.product.currency,
          frequency: TestData.plan.frequency,
          name: TestData.plan.name,
        },
        status: common.Enums.RecurrenceStatus.Enum.ACTIVE,
        type: common.Enums.RecurrenceType.Enum.CARD,
        renewalDate: recurrence.renewalDate.toISOString(),
        startDate: recurrence.startDate?.toISOString(),
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
      }),
    );
  });

  test("get active recurrent payment (as admin)", async () => {
    const recurrence = await recurrenceService.create(TestData.recurrence);

    const query: recurrenceT.GetRecurrenceRequest = {
      userId: TestData.user.id,
    };
    const response = await request(app)
      .get("/live/recurrence")
      .query(query)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    const body = response.body as recurrenceT.GetRecurrenceResponse;

    expect(body).toEqual(
      expect.objectContaining({
        plan: {
          amount: TestData.product.amount,
          currency: TestData.product.currency,
          frequency: TestData.plan.frequency,
          name: TestData.plan.name,
        },
        status: common.Enums.RecurrenceStatus.Enum.ACTIVE,
        type: common.Enums.RecurrenceType.Enum.CARD,
        renewalDate: recurrence.renewalDate.toISOString(),
        startDate: recurrence.startDate?.toISOString(),
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
      }),
    );
  });
});
