import { businessPromotion as bp } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { businessService } from "../../src/business";
import { influencerService } from "../../src/influencer";
import { TestData, adminAuthToken } from "../common";
import { businessPromotionService } from "../../src/business-promotion";

describe("delete business promotion tests", () => {
  test("delete a business promotion", async () => {
    await influencerService.create(TestData.influencer);
    await businessService.create(TestData.business);
    const promotion = await businessPromotionService.create(
      TestData.businessPromotion,
    );

    const query: bp.DeleteBusinessPromotionRequest = {
      id: promotion.id,
    };

    const response = await request(app)
      .delete("/live/business-promotion")
      .query(query)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(204);

    const deleted = await businessPromotionService.get("id", promotion.id);
    expect(deleted).toBeUndefined();
  });
});
