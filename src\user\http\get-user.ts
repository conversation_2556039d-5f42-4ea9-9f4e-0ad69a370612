import { Middleware } from "@koa/router";
import {
  AppError,
  Auth,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { user } from "@mainframe-peru/types";
import { userService } from "../service";
import { recurrenceService } from "../../recurrence";

export const getUserEndpoint: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  user.GetUserResponse
> = async ({ request, response, state }) => {
  let entity;
  if (state.auth.iss == "mainframe:user") {
    entity = await userService.get("id", state.auth.id);
  } else {
    const query = user.GetUserRequestSchema.parse(request.query);
    entity = await userService.get("id", query.id);
  }

  if (!entity) {
    throw new AppError({
      code: "UserNotFound",
      message: "No se encontró al usuario solicitado",
      statusCode: "NOT_FOUND",
    });
  }

  const activeRecurrence = await recurrenceService.getActiveRecurrence(
    entity.id,
  );

  response.body = user.GetUserResponseSchema.parse({
    ...entity,
    activeRecurrence: !!activeRecurrence,
    planId: activeRecurrence?.planId,
  });
};
