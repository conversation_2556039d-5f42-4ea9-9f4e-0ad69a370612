DO $$ BEGIN
 CREATE TYPE "core_backend"."transaction_channel" AS ENUM('CARD', 'YAPE');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
ALTER TABLE "core_backend"."complaint" RENAME COLUMN "complaint_id" TO "id";--> statement-breakpoint
ALTER TABLE "core_backend"."complaint" RENAME COLUMN "cage_category" TO "age_category";--> statement-breakpoint
ALTER TABLE "core_backend"."transaction" ADD COLUMN "channel" "core_backend"."transaction_channel" DEFAULT 'CARD' NOT NULL;

update core_backend.transaction t
set channel = cast(case when r.type = 'CARD' then 'CARD' else 'YAPE' end as core_backend.transaction_channel)
from core_backend.transaction_detail td
         inner join core_backend.recurrence r on td.entity_id = r.id
where t.id = td.transaction_id;
