import request from "supertest";
import { influencerService } from "../../../src/influencer";
import { userService } from "../../../src/user";
import { productService } from "../../../src/product/service";
import { app } from "../../../src/api-handler";
import { TestData, adminAuthToken } from "../../common";
import { common, code as ct } from "@mainframe-peru/types";

describe("create codes tests", () => {
  beforeEach(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
  });

  test("should create codes successfully", async () => {
    // Create a product first
    const product = await productService.create(TestData.product);

    const requestBody: ct.CreateCodesRequest = {
      codes: ["CODE_001", "CODE_002", "CODE_003"],
      productId: product.id,
    };

    const response = await request(app)
      .post("/live/code")
      .set("Cookie", `session=${await adminAuthToken}`)
      .send(requestBody);

    expect(response.status).toBe(200);
    const responseBody: ct.CreateCodesResponse = response.body;
    expect(responseBody).toEqual({
      success: true,
      codesCreated: 3,
      codes: expect.arrayContaining([
        expect.objectContaining({
          code: "CODE_001",
          productId: product.id,
          createdAt: expect.any(String),
        }),
        expect.objectContaining({
          code: "CODE_002",
          productId: product.id,
          createdAt: expect.any(String),
        }),
        expect.objectContaining({
          code: "CODE_003",
          productId: product.id,
          createdAt: expect.any(String),
        }),
      ]),
    });
  });

  test("should fail with empty codes array", async () => {
    const product = await productService.create(TestData.product);

    const requestBody: ct.http.CreateCodesRequest = {
      codes: [],
      productId: product.id,
    };

    const response = await request(app)
      .post("/live/code")
      .set("Cookie", `session=${await adminAuthToken}`)
      .send(requestBody);

    expect(response.status).toBe(400);
    const errorBody = response.body as common.HttpApiError;
    expect(errorBody.code).toBe("EmptyCodesArray");
  });

  test("should fail with duplicate codes in request", async () => {
    const product = await productService.create(TestData.product);

    const requestBody: ct.http.CreateCodesRequest = {
      codes: ["DUPLICATE_CODE", "UNIQUE_CODE", "DUPLICATE_CODE"],
      productId: product.id,
    };

    const response = await request(app)
      .post("/live/code")
      .set("Cookie", `session=${await adminAuthToken}`)
      .send(requestBody);

    expect(response.status).toBe(400);
    const errorBody = response.body as common.HttpApiError;
    expect(errorBody.code).toBe("DuplicateCodesInRequest");
  });

  test("should fail with non-existent product", async () => {
    const requestBody: ct.http.CreateCodesRequest = {
      codes: ["CODE_001"],
      productId: 99999, // Non-existent product ID
    };

    const response = await request(app)
      .post("/live/code")
      .set("Cookie", `session=${await adminAuthToken}`)
      .send(requestBody);

    expect(response.status).toBe(404);
    const errorBody = response.body as common.HttpApiError;
    expect(errorBody.code).toBe("ProductNotFound");
  });

  test("should fail without admin authentication", async () => {
    const product = await productService.create(TestData.product);

    const requestBody: ct.http.CreateCodesRequest = {
      codes: ["CODE_001"],
      productId: product.id,
    };

    const response = await request(app)
      .post("/live/code")
      .send(requestBody);

    expect(response.status).toBe(401);
  });

  test("should fail with invalid request body", async () => {
    const invalidRequestBody = {
      // Missing required fields
    };

    const response = await request(app)
      .post("/live/code")
      .set("Cookie", `session=${await adminAuthToken}`)
      .send(invalidRequestBody);

    expect(response.status).toBe(400);
  });

  test("should fail when trying to create duplicate codes", async () => {
    const product = await productService.create(TestData.product);

    // Create codes first time
    const firstRequest: ct.http.CreateCodesRequest = {
      codes: ["EXISTING_CODE"],
      productId: product.id,
    };

    await request(app)
      .post("/live/code")
      .set("Cookie", `session=${await adminAuthToken}`)
      .send(firstRequest);

    // Try to create the same code again
    const secondRequest: ct.http.CreateCodesRequest = {
      codes: ["EXISTING_CODE"],
      productId: product.id,
    };

    const response = await request(app)
      .post("/live/code")
      .set("Cookie", `session=${await adminAuthToken}`)
      .send(secondRequest);

    expect(response.status).toBe(400);
    const errorBody = response.body as common.HttpApiError;
    expect(errorBody.code).toBe("DuplicateCodes");
  });
});
