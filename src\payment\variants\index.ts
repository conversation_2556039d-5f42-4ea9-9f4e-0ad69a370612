import { ppe } from "@mainframe-peru/types";
import { PaymentExecutionVariant, PaymentExecutionVariantInput } from "./base";
import { RecurrenceCreation } from "./recurrence-creation";
import { Recurrence<PERSON><PERSON><PERSON> } from "./recurrence-renewal";
import { AppError } from "@mainframe-peru/common-core";

export function getPaymentExecutionVariant(
  input: PaymentExecutionVariantInput,
): PaymentExecutionVariant<ppe.ChargeVariant> {
  if (input.request.variant.variant === "RECURRENCE_CREATION") {
    return new RecurrenceCreation(input);
  } else if (input.request.variant.variant === "RECURRENCE_RENEWAL") {
    return new RecurrenceRenewal(input);
  }
  throw new AppError({});
}
