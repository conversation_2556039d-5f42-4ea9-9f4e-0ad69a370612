import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
  verifyClientJWT,
} from "@mainframe-peru/common-core";
import { userService } from "../service";
import { UpdatePasswordRequestSchema } from "@mainframe-peru/types/build/user";
import { sc } from "../../services";

export const updateUserPasswordEndpoint: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  null
> = async ({ request, response }) => {
  const { token, password } = UpdatePasswordRequestSchema.parse(request.body);

  const auth = (await verifyClientJWT(
    token,
    sc.vars.keys.user.public,
  )) as AuthEndUser;

  const user = await userService.getUser(auth.influencerId, auth.email);
  if (!user) {
    throw new AppError({
      code: "UserIdIsRequired",
      message: "A userId is required",
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  }

  await userService.updatePassword(user.id, password);
  response.status = 204;
};
