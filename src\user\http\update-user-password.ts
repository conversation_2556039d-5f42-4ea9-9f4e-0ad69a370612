import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
  verifyClientJWT,
} from "@mainframe-peru/common-core";
import { userService } from "../service";
import { UpdatePasswordRequestSchema } from "@mainframe-peru/types/build/user";
import { sc } from "../../services";

export const updateUserPasswordEndpoint: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  null
> = async ({ request, response,cookies }) => {
  const body = UpdatePasswordRequestSchema.parse(request.body);

  let token: string | undefined = cookies.get("session");

  if (!token) {
    token = body.token;
  }

  if (!token) {
    throw new AppError({
      code: "MissingToken",
      message: "Token is required either in cookie or body",
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  }

  const auth = (await verifyClientJWT(
    token,
    sc.vars.keys.user.public,
  )) as AuthEndUser;

  const user = await userService.getUser(auth.influencerId, auth.email);
  if (!user) {
    throw new AppError({
      code: "UserIdIsRequired",
      message: "A userId is required",
      logLevel: "INFO",
      statusCode: "BAD_REQUEST",
    });
  }

  await userService.updatePassword(user.id, body.password);
  response.status = 204;
};
