import { influencer } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import request from "supertest";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer/service";
import { officerAuthToken } from "../common";

describe("list influencer admins tests", () => {
  test("Lists influencers", async () => {
    await influencerService.create({
      id: "Molitalia",
      name: "Molitalia name",
      status: Enums.InfluencerStatus.Enum.ACTIVE,
      logoUrl: "www",
      domain: "domain.com",
      createdAt: new Date(),
    });

    await influencerService.create({
      id: "Nivea",
      name: "Nivea name",
      status: Enums.InfluencerStatus.Enum.ACTIVE,
      logoUrl: "www",
      domain: "domain.com",
      createdAt: new Date(),
    });

    const response = await request(app)
      .get("/live/influencer/list-influencers")
      .set("<PERSON>ie", `session=${await officerAuthToken}`);

    const responseBody = response.body as influencer.ListInfluencerResponse;

    expect(response.status).toBe(200);
    expect(responseBody.length).toBeGreaterThanOrEqual(2);
  });

  test("Public lists influencers", async () => {
    const testEntity = await influencerService.create({
      id: "Molitalia",
      name: "Molitalia name",
      status: Enums.InfluencerStatus.Enum.ACTIVE,
      logoUrl: "www",
      domain: "domain.com",
      createdAt: new Date(),
    });

    const testEntity2 = await influencerService.create({
      id: "Nivea",
      name: "Nivea name",
      status: Enums.InfluencerStatus.Enum.ACTIVE,
      logoUrl: "www",
      domain: "domain.com",
      createdAt: new Date(),
    });

    const response = await request(app).get(
      "/live/influencer/public-list-influencers",
    );

    const responseBody =
      response.body as influencer.PublicListInfluencerResponse;

    expect(response.status).toBe(200);
    expect(responseBody.length).toBeGreaterThanOrEqual(2);
    expect(responseBody).toContainEqual({
      id: testEntity.id,
      name: testEntity.name,
    });
    expect(responseBody).toContainEqual({
      id: testEntity2.id,
      name: testEntity2.name,
    });
  });
});
