import { Middleware } from "@koa/router";
import {
  AppError,
  AuthAdmin,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { report } from "@mainframe-peru/types";
import { eventService } from "../../event";
import { reportService } from "../service";
import { setCsvResponse } from "../../common";

export const listEventParticipants: Middleware<
  AuthorizedContextState<AuthAdmin>,
  unknown,
  report.ListEventParticipantsResponse | NodeJS.WritableStream
> = async ({ request, response, state }) => {
  const body = report.ListEventParticipantsRequestSchema.parse(request.query);

  const event = await eventService.get("id", body.eventId);
  if (!event || event.influencerId !== state.auth.influencerId) {
    throw new AppError({
      code: "EventNotFound",
      message: "Could not find event",
      statusCode: "NOT_FOUND",
      logLevel: "INFO",
    });
  }

  const participants = await reportService.listEventParticipants({
    eventId: body.eventId,
    influencerId: state.auth.influencerId,
  });

  const responseBody: report.ListEventParticipantsResponse =
    reportService.eventParticipantToCsvRecords(participants);

  if (request.accepts("application/json")) {
    response.body = responseBody;
  } else if (request.accepts("text/csv")) {
    setCsvResponse(responseBody, response, "users");
  }
};
