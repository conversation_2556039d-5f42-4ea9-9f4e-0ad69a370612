import { card as cardT, ext } from "@mainframe-peru/types";
import { Enums } from "@mainframe-peru/types/build/common";
import fetch, { Response } from "node-fetch";
import { randomUUID } from "node:crypto";
import request from "supertest";
import { app } from "../../src/api-handler";
import { cardService } from "../../src/card/service";
import { influencerService } from "../../src/influencer";
import { planService } from "../../src/plan";
import { recurrenceService } from "../../src/recurrence";
import { userService } from "../../src/user";
import { TestData, userAuthToken } from "../common";
import { productService } from "../../src/product/service";

jest.mock("node-fetch");

describe("payment.delete-card", () => {
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

  beforeAll(async () => {
    await influencerService.create(TestData.influencer);
    await userService.create(TestData.user);
    await productService.create(TestData.product);
    await planService.create(TestData.plan);
  });

  test("failing delete card on user", async () => {
    const requestBody: cardT.DeleteUserCardRequest = {
      id: 0,
    };
    const response = await request(app)
      .post("/live/card/delete")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      code: "CardNotFound",
      message: "No se encontró la tarjeta",
    });
  });

  test("failing delete card with recurrent payment ", async () => {
    // Setup user id, saved card and recurrent payment
    const card = await cardService.create({
      id: 10 ** 6,
      userId: TestData.user.id,
      brand: "Visa",
      default: true,
      number: "4111...1111",
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      token: randomUUID(),
    });

    await recurrenceService.create(TestData.recurrence);

    // Post request
    const requestBody: cardT.DeleteUserCardRequest = {
      id: card.id,
    };
    const response = await request(app)
      .post("/live/card/delete")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      code: "CardIsDefault",
      message: "Given card is default and a recurrent payment is still active",
    });
  });

  test("delete card on user", async () => {
    // Setup user id and saved card
    const card = await cardService.create({
      id: 10 ** 6,
      userId: TestData.user.id,
      brand: "Visa",
      default: false,
      number: "4111...1111",
      paymentProvider: Enums.PaymentProvider.Enum.CULQI,
      token: randomUUID(),
    });

    // Mock culqi api
    const json = jest.fn();
    const culqiDeleteCardResponse: ext.culqi.DeleteCardResponse = {
      id: "crd_test_RzjTyGUwZioJLpZt",
      deleted: true,
      merchant_message:
        "Se eliminó la tarjeta con ID crd_test_RzjTyGUwZioJLpZt exitosamente.",
    };
    json.mockResolvedValue(culqiDeleteCardResponse);
    mockFetch.mockResolvedValue({ ok: true, json } as unknown as Response);

    // Post request body
    const requestBody: cardT.DeleteUserCardRequest = {
      id: card.id,
    };

    const response = await request(app)
      .post("/live/card/delete")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(204);

    const storedCards = await cardService.listByUserId(TestData.user.id);
    expect(storedCards?.length).toEqual(0);
  });
});
