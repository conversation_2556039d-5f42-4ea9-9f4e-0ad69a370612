import { Middleware } from "@koa/router";
import { logger } from "@mainframe-peru/common-core";
import { invoice } from "@mainframe-peru/types";
import { transactionService } from "../../transaction";
import { invoiceService } from "../service";

export const createInvoice: Middleware<unknown, unknown, null> = async (
  ctx,
) => {
  ctx.response.body = null;
  const { transactionId, invoiceDestination } = ctx.request
    .body as invoice.sqs.CreateInvoiceRequest;

  const transaction = await transactionService.get("id", transactionId);
  if (!transaction) {
    logger.critical("Could not find transaction", { body: ctx.request.body });
    return;
  }
  await invoiceService.generateAndCreateInvoice(
    transaction,
    invoiceDestination,
  );
};
