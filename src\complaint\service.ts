import { complaint as comp } from "@mainframe-peru/types";
import { ServiceBase } from "../service-base";
import { ComplaintEntity, complaintRepository } from "./repository";
import { transformNullToUndefined } from "../helpers";

class ComplaintService extends ServiceBase<
  typeof complaintRepository,
  ComplaintEntity
> {
  constructor() {
    super(complaintRepository);
  }

  async getComplaints(
    filters: comp.ListComplaintsRequest & { influencerId: string },
  ): Promise<comp.Complaint[]> {
    return transformNullToUndefined(
      await this.repository.findComplaints(filters),
    );
  }
}

export const complaintService = new ComplaintService();
