import { admin } from "@mainframe-peru/types";
import request from "supertest";
import { adminService } from "../../src/admin";
import { AdminEntity } from "../../src/admin/repository";
import { app } from "../../src/api-handler";
import { influencerService } from "../../src/influencer";
import { TestData, officerAuthToken } from "../common";

describe("get admin tests", () => {
  test("gets an existing admin by id (email)", async () => {
    await influencerService.create(TestData.influencer);

    const testEntity: AdminEntity = {
      id: 159,
      influencerId: TestData.influencer.id,
      email: "<EMAIL>",
      hash: "123123123123123123",
      firstName: "John",
      lastName: "Tester",
      createdAt: new Date(),
      updatedAt: new Date(),
      policies: {},
    };
    await adminService.create(testEntity);

    const queryParams: admin.GetAdminRequest = {
      id: 159,
    };

    const response = await request(app)
      .get("/live/admin")
      .query(queryParams)
      .set("Cookie", `session=${await officerAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual(
      expect.objectContaining({
        id: testEntity.id,
        firstName: testEntity.firstName,
        lastName: testEntity.lastName,
      }),
    );
  });
});
