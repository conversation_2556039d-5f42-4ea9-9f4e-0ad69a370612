import { eq, and, InferSelectModel, InferInsertModel } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { codeTable } from "../schema";
import { sc } from "../services";

export type CodeEntity = InferInsertModel<
  typeof codeTable
>;

class CodeRepository extends RepositoryBase<typeof codeTable, CodeEntity> {
  constructor() {
    super(codeTable);
  }

  async createBatch(codes: CodeEntity[]): Promise<void> {
    const db = await sc.getDB();
    await db.insert(this.table).values(codes);
  }

  async findByCodeAndInfluencer(
    code: string,
    influencerId: string,
  ): Promise<CodeEntity | undefined> {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .where(and(eq(this.table.code, code), eq(this.table.influencerId, influencerId)))
      .limit(1);
    return result[0];
  }

  async redeemCode(
    code: string,
    influencerId: string,
    userId: number,
    redeemedAt: Date,
  ): Promise<CodeEntity | undefined> {
    const db = await sc.getDB();
    const result = await db
      .update(this.table)
      .set({
        userId,
        redeemedAt,
        updatedAt: new Date(),
      })
      .where(and(eq(this.table.code, code), eq(this.table.influencerId, influencerId)))
      .returning();
    return result[0];
  }
}

export const codeRepository = new CodeRepository();
