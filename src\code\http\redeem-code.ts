import { Middleware } from "@koa/router";
import {
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { code as ct } from "@mainframe-peru/types";
import { codeService } from "../service";

export const redeemCodeEndpoint: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  null
> = async ({ request, response, state }) => {
  const body = ct.RedeemCodeRequestSchema.parse(request.body);

  await codeService.redeemCode(
    body.code,
    state.auth.influencerId,
    state.auth.id,
  );

  response.body = null;
};
