import { Middleware } from "@koa/router";
import {
  AuthAdmin,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { attribute as at } from "@mainframe-peru/types";
import { attributeService } from "../service";

export const listAttributes: Middleware<
  AuthorizedContextState<AuthEndUser | AuthAdmin>,
  unknown,
  at.ListAttributesResponse
> = async ({ request, response, state }) => {
  const queryParams = at.ListAttributesRequestSchema.parse(request.query);

  const attributes = await attributeService.list({
    influencerId: state.auth.influencerId,
    entity: queryParams.entity || "INFLUENCER",
  });

  response.body = attributes.map((a) => ({
    id: a.id,
    description: a.description || undefined,
    text: a.text,
    type: a.type,
    options: a.options || undefined,
  }));
};
